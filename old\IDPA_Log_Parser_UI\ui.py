from flask import Flask, session, render_template, request, redirect, g, url_for, Response, send_file
from flask_cors import CORS, cross_origin
import os
import sqlite3
import time
import subprocess
import re
from time import gmtime, strftime
from flask import Flask, render_template, request
import re, os, sys, time, datetime
import fnmatch
from collections import deque
from werkzeug.utils import secure_filename
from werkzeug.datastructures import  FileStorage
from random import randint
import datetime
from flask import flash
import paramiko
from werkzeug.exceptions import HTTPException, NotFound
import time, socket, logging
import jinja2
env = jinja2.Environment()
env.globals.update(zip=zip)
from ast import literal_eval
from logging.handlers import RotatingFileHandler
from logging import handlers
import mainanalyzer as anlz
now = datetime.datetime.now()
from datetime import datetime, timedelta
from flask_login import login_user, logout_user, login_required

app = Flask('IDPA_Analyzer')
CORS(app)
app.secret_key = b'_5#y2L"F4Q8z\n\xec]/'


UPLOAD_FOLDER = 'Uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
ALLOWED_EXTENSIONS = set(['log', 'txt', '1','2','3','4','5','6','7','8','9'])


####################
#LOGGING MODULE
####################
logging.getLogger("paramiko").setLevel(logging.WARNING)
logger = logging.getLogger('')
logger.setLevel(logging.INFO)
format = logging.Formatter("%(asctime)s %(threadName)s %(name)s [%(levelname)s]  %(message)s")
#format = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
ch = logging.StreamHandler(sys.stdout)
ch.setFormatter(format)
fh = handlers.RotatingFileHandler('analyzer_ui.log', maxBytes=(1048576*5), backupCount=7)
fh.setFormatter(format)
logger.addHandler(fh)

def create_db():
    master_db = sqlite3.connect('stats.db')
    master_db.isolation_level = None	 
    master_db.execute('PRAGMA foreign_keys = ON;')		
    master_db.text_factory = str	  
    master_db.execute('''CREATE TABLE IF NOT EXISTS "USER_STATS" (
	"id"	INTEGER NOT NULL UNIQUE,
	"ip_address"	TEXT,
	"ts"	TEXT NOT NULL,
	"orig_logfilename"	TEXT,    
	"logfilename"	TEXT,
	"ret_code"	TEXT NOT NULL,
	"errors_count"	TEXT,
	"errors_removed"	TEXT,
	"errors_left"	TEXT,
	"percentage_reduction"	TEXT,
	"total_solutions"	TEXT,
	"lincoln_path"	TEXT,
	PRIMARY KEY("id" AUTOINCREMENT)
);''')    
    master_db.commit()
    return master_db

try:
    from tqdm import tqdm
except ImportError:
    class TqdmWrap(object):
        # tqdm not installed - construct and return dummy/basic versions
        def __init__(self, *a, **k):
            pass

        def viewBar(self, a, b):
            # original version
            res = a / int(b) * 100
            sys.stdout.write('\rDownloading: %.2f %%' % (res))
            sys.stdout.write('\n')
            sys.stdout.flush()

        def __enter__(self):
            return self

        def __exit__(self, *a):
            return False
else:
    class TqdmWrap(tqdm):
        def viewBar(self, a, b):
            self.total = int(b)
            self.update(int(a - self.n))  # update pbar with increment
			
def get_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # doesn't even have to be reachable
        s.connect(('**************', 1))
        IP = s.getsockname()[0]
    except Exception:
        IP = '127.0.0.1'
    finally:
        s.close()
    return IP 			
def ssh_logs_download(ip, user, password,localfilename,remotepathfilename):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        ssh.connect(hostname=ip, username=user, password=password, timeout=5, compress = True,look_for_keys=False, allow_agent=False)
    except (socket.error,paramiko.AuthenticationException,paramiko.SSHException) as message:
        logger.error("ERROR: SSH connection to "+ip+" failed: " +str(message))
        sys.exit(1)

    sftp = ssh.open_sftp()
    #sftp = ssh.open_sftp()
    with TqdmWrap(ascii=True, unit='b', unit_scale=True) as pbar:
        sftp.get(remotepathfilename,localfilename, callback=pbar.viewBar)
    #cbk, pbar = tqdmWrapViewBar(ascii=True, unit='b', unit_scale=True)
    #sftp.get(remotepathfilename,localfilename,callback=cbk)
    #time.sleep(2)	
    sftp.close()
    #time.sleep(2)	
    ssh.close()
def ssh_ctrl_with_error_read_support(ip, user, password,cmd):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        ssh.connect(hostname=ip, username=user, password=password, timeout=5, compress = True,look_for_keys=False, allow_agent=False)
    except (socket.error,paramiko.AuthenticationException,paramiko.SSHException) as message:
        logger.error("ERROR: SSH connection to "+ip+" failed: " +str(message))
        sys.exit(1)

    stdin, stdout, ssh_stderr = ssh.exec_command(cmd)
    
    error_found = "Null"
    out = stdout.read()
    #blow = ssh_stderr.read()
    if not stdout.channel.recv_exit_status() == 0:
        error_found = ssh_stderr.read()
    stdin.flush()
    ssh.close()
    return out, error_found

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/download', methods=['POST'])
def download():
    filename = request.form['file_to_download']
    logger.info('File name requested for Download : ' + str(filename	))
    return send_file('Uploads/'+filename,                       
                             attachment_filename=filename,
                             as_attachment=True)
    uploads = os.path.join(current_app.root_path, app.config['UPLOAD_FOLDER'])
    return send_from_directory(directory=uploads, filename=filename)

@app.route('/download_log/<filenametodownload>')
def download_log(filenametodownload): #NEW PARSER DOWNLOAD LOG
    logger.info('Request file download : ' + str(filenametodownload))
    return send_file('Uploads/' + str(filenametodownload), mimetype='text/csv', as_attachment=True, cache_timeout=0)

@app.before_request
def before_request():
    if 'logged_in' not in session and request.endpoint == 'reviewboard':
        return redirect(url_for('login'))
        
@app.route('/login', methods=['GET', 'POST']) #HOMEPAGE
def login():
    error = None
    if request.method == 'POST':
        if request.form['username'] != 'admin' or request.form['password'] != 'Hnbguster7':
            error = 'Invalid Credentials. Please try again.'
        else:
            session['logged_in'] = True       
            return redirect(url_for('reviewboard'))
    return render_template('login.html', error=error)

@app.route('/logout')
def logout():
    session.pop('logged_in', None)
    return redirect(url_for('index'))    
    
@app.route('/') #HOMEPAGE
def index():    
    if request.method == 'GET':
        return render_template('index.html')
        
@app.route('/newparser') #HOMEPAGE NEWPARSER
def newparser():
    if request.method == 'GET':
        return render_template('newparser.html')

@app.route('/oldparser') #HOMEPAGE OLDPARSER
def oldparser():
    if request.method == 'GET':
        return render_template('oldparser.html')        

@app.route('/lincoln_process', methods=['GET', 'POST'])
def lincoln_process(): #OLD PARSER LINCOLN FUNCTION
    if request.method == 'POST':		
        main_data = request.form['file_name']	
        filename = main_data.split('!')[0] #selected filename by user
        lincoln_path = main_data.split('!')[1] #lincoln full path
        file_list_obj = main_data.split('!')[2] #fileobj list
        file_list_obj = literal_eval(file_list_obj)
        new_file_name = now.strftime("%Y_%m_%d_%H_%M")+'_'+str(randint(0, 91212121212))+'.log'	 #dummy file name
        ssh_logs_download('lincoln.avamar.com', 'admin', 'changeme','Uploads/'+new_file_name,lincoln_path+'/'+filename)        
        logs_excerpt, total_attempts_made, failure_observed_at, cause , resolution = process_file.process('Uploads/'+new_file_name)
        logger.info('================================')
        logger.info('Filename            : ' + str(new_file_name))
        logger.info('Current time        : ' + str(now.strftime("%Y_%m_%d_%H_%M"))		)
        logger.info('Total Attempts Made : ' + str(total_attempts_made))
        logger.info('Failure Obser at    : ' + str(failure_observed_at))
        logger.info('Cause               : ' + str(cause))
        logger.info('Resolution          : ' + str(resolution)	)
        logger.info('Logs Excerpt        : ' + str(logs_excerpt)	)
	
        logger.info('================================')

        if cause == 'Undefined': #we are not deleting the file and letting it sit so that we can analyze it later for issues in it
	
            flash('Seems like we are not able to identify the problem in this Log. We will make a note of this and will analyze it and improve the system')            
            return render_template('select_file.html', file_list_obj=file_list_obj, lincoln_path=lincoln_path, target=2)    
        if total_attempts_made == 0:
                flash('No Issues found in the log, If there is a real issue in this logfile, kindly let Pankaj know about it. Thanks')        
                return render_template('select_file.html', file_list_obj=file_list_obj, lincoln_path=lincoln_path, target=2)
        try:			
            if int(failure_observed_at) == 0:
                			
                flash('No Issues found in the log, If there is a real issue in this logfile, kindly let Pankaj know about it. Thanks')							
                return render_template('select_file.html', file_list_obj=file_list_obj, lincoln_path=lincoln_path, target=2)
        except:#this mean we are using a custom elif statement from process_file so return normally
            		
            #os.remove('Uploads/'+new_file_name)							
            return render_template('processed_file.html', main_data = zip(logs_excerpt,failure_observed_at,cause,resolution) ,logs_excerpt = logs_excerpt, total_attempts_made=total_attempts_made, failure_observed_at= failure_observed_at, cause=cause , resolution=resolution, filename=filename,lincoln_path=lincoln_path,new_file_name=new_file_name, file_list_obj=file_list_obj) 						                			
       
        #return render_template('processed_file.html', logs_excerpt = logs_excerpt, total_attempts_made=total_attempts_made, failure_observed_at= failure_observed_at, cause=cause , resolution=resolution,filename=filename,lincoln_path=lincoln_path,new_file_name=new_file_name)	
        return render_template('processed_file.html', main_data = zip(logs_excerpt,failure_observed_at,cause,resolution), total_attempts_made=total_attempts_made, lincoln_path=lincoln_path, filename=filename, file_list_obj=file_list_obj, new_file_name=new_file_name)									
			
@app.route('/upload', methods=['GET', 'POST'])
def upload_file(): #OLD PARSER UPLOAD FUNCTION
    if request.method == 'POST':
        filename_html =  request.files['file'] #Getting filename     
        ###########################
        #LINCOLN PROCESS START HERE
        ###########################
        if not filename_html:
            if request.form['lincoln_path']:
                lincoln_path = request.form['lincoln_path']
                try:				
                    #print str(lincoln_path.split('/home/<USER>/logs/')[1])				
                    if len(str(lincoln_path.split('/home/<USER>/logs/')[1])) > 0:
                        last_path_part = lincoln_path.split('/home/<USER>/logs/')[1]					
                        #if last_path_part.split('/')[0].isdigit():
                        if 8 > 7:
                            file_list_obj = []						
                            dir_list = ssh_ctrl_with_error_read_support('lincoln.avamar.com', 'admin', 'changeme','ls '+lincoln_path)
                            file_list = dir_list[0].split('\n')
                            for file in file_list:	
                                if ('.log' in file):
                                    file_list_obj.append(file) #contains the list of server.log files
                            if len(file_list_obj) > 0:	
                                return render_template('select_file.html', file_list_obj=file_list_obj, lincoln_path=lincoln_path, target=2)								
                            else:										
                                flash('No valid server.log or its extensions found in path')
                                return render_template('oldparser.html')                       
                        else:
                            flash('Invalid Path to Lincoln Given')
                            return render_template('oldparser.html')                							
                    else:
                        flash('Please select the File First')
                        return render_template('oldparser.html')                        					
                except Exception as e:
                    flash('Exception : Invalid path to lincoln given. Could be either due to :1) You are not using SR_Number in path 2) You are using "~" sign')
                    return render_template('oldparser.html')                    				
            flash('Please either upload a file or select a valid Lincoln Path')
            return render_template('oldparser.html')
        ###########################
        #REGULAR PROCESS START HERE
        ###########################            
        file = request.files['file']
        new_file_name = now.strftime("%Y_%m_%d_%H_%M")+'_'+str(randint(0, 91212121212))+'.log'	  		
        if file.filename == '':
            flash('No selected file')
            return render_template('oldparser.html')
        if file and allowed_file(file.filename):
            filename = secure_filename(new_file_name)
            file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))			
            logs_excerpt, total_attempts_made, failure_observed_at, cause , resolution = process_file.process('Uploads/'+new_file_name)
            logger.info('================================')	
            logger.info('Filename            : ' + str(new_file_name)	                )
            logger.info('Current time        : ' + str(now.strftime("%Y_%m_%d_%H_%M"))	)				
            logger.info('Total Attempts Made : ' + str(total_attempts_made)             )
            logger.info('Failure Obser at    : ' + str(failure_observed_at)             )
            logger.info('Cause               : ' + str(cause)                           )
            logger.info('Resolution          : ' + str(resolution)		                )
            #logger.info('Logs Excerpt        : ' + str(logs_excerpt))

            logger.info('================================')	
            logger.info('Filename            : ' + str(new_file_name)	                  )
            logger.info('Current time        : ' + str(now.strftime("%Y_%m_%d_%H_%M"))		)			
            logger.info('Total Attempts Made : ' + str(total_attempts_made)               )
            logger.info('Failure Obser at    : ' + str(failure_observed_at)               )
            logger.info('Cause               : ' + str(cause)                             )
            logger.info('Resolution          : ' + str(resolution)				          )
	
            logger.info('================================')
            		
            if cause == 'Undefined': #we are not deleting the file and letting it sit so that we can analyze it later for issues in it
                		
                return render_template('add_file.html')     			
            try:			
                if int(failure_observed_at) == 0:
                    			
                    return render_template('no_issues.html')
            except:#this mean we are using a custom elif statement from process_file so return normally
                		
                return render_template('processed_file.html', main_data = zip(logs_excerpt,failure_observed_at,cause,resolution), total_attempts_made=total_attempts_made, filename=filename,new_file_name=new_file_name)				
       
            
            return render_template('processed_file.html', main_data = zip(logs_excerpt,failure_observed_at,cause,resolution), total_attempts_made=total_attempts_made, filename=filename,new_file_name=new_file_name)							
        else:
            flash('Invalid File Extension(only "log and txt")')		
            return render_template('oldparser.html')			

@app.route('/review_log', methods=['GET', 'POST'])
def review_log(): #NEW PARSER LINCOLN FUNCTION
    if request.method == 'POST':		
        main_data = request.form['file_name']	
        #print main_data        
        ip_address = request.remote_addr        
        filename = main_data.split('!')[0] #selected filename by user
        lincoln_path = main_data.split('!')[1] #lincoln full path
        file_list_obj = main_data.split('!')[2] #fileobj list
        file_list_obj = literal_eval(file_list_obj)
        new_file_name = now.strftime("%Y_%m_%d_%H_%M")+'_'+str(randint(0, 91212121212))+'.log'	 #dummy file name
        file_local_name =   new_file_name            
        logfilename = 'Uploads/'+new_file_name 
        print 'Download : ' + str(logfilename) + ' from ' + str(lincoln_path) + '/' + str(filename)
        try:        
            ssh_logs_download('lincoln.avamar.com', 'admin', 'changeme',logfilename,lincoln_path+'/'+filename)
        except Exception as e:
            logger.error('Exception received while downloading : ' + str(e))        
            return render_template('analyzed.html', error = 'Unable to download file')        
        #logfilename = 'Uploads/2021_01_09_03_01_24467479085.log'        
        #_logname =  '2021_01_09_03_01_24467479085.log'        
        logger.info('Calling analyzer to analyze local logfile : ' + str(logfilename) + ' and remote filename : ' + str(filename) + ' for us...')
        st = time.time()
        ret_code, main_errors_stack, fullerrorstack, total_errors_count, total_errors_removed_or_eliminated, total_errors_left, percentage_reduction , allthreads_list,log_start_time, log_end_time, logage = anlz.main_analyzer(logfilename, False)
        et =time.time()
        difftime = str(round(et - st, 2))
        logger.info('Analyzer finished understanding logs...')
        stats_db = create_db()
        stats_db_cursor = stats_db.cursor()        
        if ret_code:        
            total_solutions = [len(x[9]) for x in allthreads_list]
            total_solutions =  sum(total_solutions)
            
            stats_db_cursor.execute('INSERT OR IGNORE INTO USER_STATS (ip_address, ts, orig_logfilename, logfilename, ret_code, errors_count, errors_removed, errors_left, percentage_reduction, total_solutions, lincoln_path) VALUES (?,?,?,?,?,?,?,?,?,?,?)', (ip_address,  datetime.now(), filename, logfilename, ret_code, total_errors_count, total_errors_removed_or_eliminated, total_errors_left, percentage_reduction, total_solutions, lincoln_path))        
            stats_db.commit()
            stats_db.close()
            return render_template('analyzed.html', fullerrorstack=fullerrorstack, total_errors_count=total_errors_count, total_errors_removed_or_eliminated=total_errors_removed_or_eliminated, total_errors_left=total_errors_left, percentage_reduction=percentage_reduction, allthreads_list=allthreads_list, difftime=difftime,  log_start_time=log_start_time, log_end_time=log_end_time, logage=logage,logfilename=file_local_name, file_list_obj=file_list_obj, total_solutions=total_solutions, org_filename = filename, error = None, lincoln_path=lincoln_path, main_errors_stack=main_errors_stack)
        else:
            stats_db_cursor.execute('INSERT OR IGNORE INTO USER_STATS (ip_address, ts, orig_logfilename, logfilename, ret_code,lincoln_path ) VALUES (?,?,?,?,?,?)', (ip_address,  datetime.now(), filename, logfilename, ret_code, lincoln_path))        
            stats_db.commit()
            stats_db.close()        
            logger.error('We recevied return code False for the logfile :  ' + str(logfilename))        
            return render_template('analyzed.html', error = 'Log file contains insufficient information to decide what is going on !!!') 
    else:
        return redirect(url_for('newparser'))    
			
@app.route('/uploadlog', methods=['GET', 'POST'])
def uploadlog(): #NEW PARSER UPLOAD FUNCTION
    if request.method == 'POST':
        ip_address = request.remote_addr    
        filename_html =  request.files['file'] #Getting filename     
        ###########################
        #LINCOLN PROCESS START HERE
        ###########################
        if not filename_html:
            if request.form['lincoln_path']:
                lincoln_path = request.form['lincoln_path']
                try:				
                    #print str(lincoln_path.split('/home/<USER>/logs/')[1])				
                    if len(str(lincoln_path.split('/home/<USER>/logs/')[1])) > 0:
                        last_path_part = lincoln_path.split('/home/<USER>/logs/')[1]					
                        #if last_path_part.split('/')[0].isdigit():
                        if 8 > 7:
                            file_list_obj = []						
                            dir_list = ssh_ctrl_with_error_read_support('lincoln.avamar.com', 'admin', 'changeme','ls '+lincoln_path)
                            file_list = dir_list[0].split('\n')
                            for file in file_list:	
                                if ('.log' in file):
                                    file_list_obj.append(file) #contains the list of server.log files
                            if len(file_list_obj) > 0:	
                                return render_template('select_file.html', file_list_obj=file_list_obj, lincoln_path=lincoln_path, target=1)								
                            else:										
                                flash('No valid server.log or its extensions found in path')
                                return render_template('newparser.html')                       
                        else:
                            flash('Invalid Path to Lincoln Given')
                            return render_template('newparser.html')                							
                    else:
                        flash('Please select the File First')
                        return render_template('newparser.html')                        					
                except Exception as e:
                    flash('Exception : Invalid path to lincoln given. Could be either due to :1) You are not using SR_Number in path 2) You are using "~" sign' + str(e))
                    return render_template('newparser.html')                    				
            flash('Please either upload a file or select a valid Lincoln Path')
            return render_template('newparser.html')
        ###########################
        #REGULAR PROCESS START HERE
        ########################### 
        logger.info("Request analysis of uploaded logfile...")        
        file = request.files['file']
        new_file_name = now.strftime("%Y_%m_%d_%H_%M")+'_'+str(randint(0, 91212121212))+'.log'	  		
        if file.filename == '':
            flash('No selected file')
            return render_template('newparser.html')
        if file and allowed_file(file.filename):
            filename = secure_filename(new_file_name)
            file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
            logfilename = 'Uploads/'+new_file_name            
            
            logger.info('Calling analyzer to analyze local logfile : ' + str(logfilename) + ' and uploaded filename : ' + str(filename) + ' for us...')
            st = time.time()           
            stats_db = create_db()
            stats_db_cursor = stats_db.cursor()            
            ret_code, main_errors_stack, fullerrorstack, total_errors_count, total_errors_removed_or_eliminated, total_errors_left, percentage_reduction , allthreads_list,log_start_time, log_end_time, logage = anlz.main_analyzer(logfilename, False)
            et =time.time()
            difftime = str(round(et - st, 2))
            logger.info('Analyzer finished understanding logs...')
            if ret_code:            
                total_solutions = [len(x[9]) for x in allthreads_list]
                total_solutions =  sum(total_solutions)
                
                stats_db_cursor.execute('INSERT OR IGNORE INTO USER_STATS (ip_address, ts, orig_logfilename, logfilename, ret_code, errors_count, errors_removed, errors_left, percentage_reduction, total_solutions, lincoln_path) VALUES (?,?,?,?,?,?,?,?,?,?,?)', (ip_address,  datetime.now(), filename, logfilename, ret_code, total_errors_count, total_errors_removed_or_eliminated, total_errors_left, percentage_reduction, total_solutions, 'None'))        
                stats_db.commit()
                stats_db.close()                
                
                return render_template('analyzed.html', fullerrorstack=fullerrorstack, total_errors_count=total_errors_count, total_errors_removed_or_eliminated=total_errors_removed_or_eliminated, total_errors_left=total_errors_left, percentage_reduction=percentage_reduction, allthreads_list=allthreads_list, difftime=difftime,  log_start_time=log_start_time, log_end_time=log_end_time, logage=logage,logfilename=logfilename, file_list_obj=None, total_solutions=total_solutions, org_filename = filename, error = None, lincoln_path=None, main_errors_stack=main_errors_stack)            
            else:
                stats_db_cursor.execute('INSERT OR IGNORE INTO USER_STATS (ip_address, ts, orig_logfilename, logfilename, ret_code) VALUES (?,?,?,?,?)', (ip_address,  datetime.now(), filename, logfilename, ret_code))        
                stats_db.commit()
                stats_db.close()
                
                logger.error('We recevied return code False for the logfile :  ' + str(logfilename))        
                return render_template('analyzed.html', error = 'Log file contains insufficient information to decide what is going on !!!')   
    else:
        return redirect(url_for('newparser'))                

#@app.route('/download_ref_logs/<myfile>')
@app.route('/download_ref_logs//<dirname>/<folder>/<filename>')
def download_ref_logs(dirname, folder, filename): #NEW PARSER DOWNLOAD LOG
    logger.info('Request file download : /' + str(dirname)+'/'+str(folder)+'/'+str(filename))
    return send_file('/' + str(dirname)+'/'+str(folder)+'/'+str(filename), as_attachment=True, cache_timeout=0)
    
@app.route('/download_ref_logs_local_uploaded/<dirname>/<filename>')
def download_ref_logs_local_uploaded(dirname, filename): #NEW PARSER DOWNLOAD LOG
    logger.info('Request file download : /' + str(dirname)+'/'+str(filename))
    return send_file(str(dirname)+'/'+str(filename), as_attachment=True, cache_timeout=0)    
	  
@app.route('/admin', methods=['GET', 'POST'])
def admin_activity():
    current_time = time.time()
    deleted_file = []
    for f in os.listdir('Uploads'):
        creation_time = os.path.getctime('Uploads/'+f)
        if (current_time - creation_time) // (24 * 3600) >= 7:
            os.unlink('Uploads/'+f)
            logger.info(('{} removed'.format('Uploads/'+f)))
            deleted_file.append('Uploads/'+f)
    if deleted_file:
        logger.info('Files Deleted')
    else:
        logger.info('No File to delete')
    return render_template('index.html')		

@app.route('/reviewboard', methods=['GET', 'POST'])
def reviewboard():
    if request.method == 'POST':
        list_start = request.form['list_id_start']
        list_end = request.form['list_id_end']
    else:
        list_start = 0
        list_end = 50        
    #print list_start, list_end
    master_db = sqlite3.connect('masterdb.db')
    cursor = master_db.cursor()  
    cursor.execute('select * from errorlog where is_valid="True" and id > ? and id < ?',(list_start, list_end))  
    data = cursor.fetchall() 

    cursor.execute('SELECT COUNT(*) FROM errorlog;')  
    totalerrors = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(IS_VALID) FROM errorlog;')  
    totalvaliderrors = cursor.fetchone()[0]    

    yesterday = datetime.now() - timedelta(1)
    yesterday = datetime.strftime(yesterday, '%Y-%m-%d')

    cursor.execute('SELECT COUNT(IS_VALID) FROM errorlog WHERE UPDATED_TS > "' + str(yesterday) + '"')  
    errors_updated_last_24hrs = cursor.fetchone()[0]    

    cursor.execute('SELECT COUNT(IS_VALID) FROM errorlog WHERE ADDED_TS > "' + str(yesterday) + '"')  
    errors_added_last_24hrs = cursor.fetchone()[0]    

    master_db.close()

    if int(list_start) > int(list_end):
        flash('ERROR : Really? Do you think that range looks valid?')    
    return render_template('reviewboard.html', data=data, totalerrors=totalerrors, totalvaliderrors=totalvaliderrors, errors_added_last_24hrs=errors_added_last_24hrs, errors_updated_last_24hrs=errors_updated_last_24hrs, list_start=list_start, list_end=list_end)	

@app.route('/edit_db/<id>/<is_valid>/<bug_ref>/<kb_ref>/<sc>/<ec>/<username>')
def edit_db(id, is_valid, bug_ref, kb_ref, sc, ec, username): #NEW PARSER EDIT DB
    master_db = sqlite3.connect('masterdb.db')
    cursor = master_db.cursor()
    logger.info('[DB_UPDATE] Request made to update Database by ' + str(username) + ' with : is_valid: ' + str(is_valid) + ' Bug_Ref: ' + str(bug_ref) + ' Kb_Ref: ' + str(kb_ref) + ' Support_Comment: ' + str(sc) + ' Engg.Comment: ' + str(ec) + ' for error id: ' + str(id))    
    date = datetime.now()    
    cursor.execute('UPDATE ERRORLOG SET UPDATED_TS=?, UPDATED_BY=?, IS_VALID=?, BUG_REF=?, KB_REF=?, SUPPORT_COMMENT=?, ENGG_COMMENT=? WHERE ID= ?', (date, username, str(is_valid), str(bug_ref), str(kb_ref), str(sc), str(ec), str(id)))

    master_db.commit()
    master_db.close()
    flash('Information updated successfully for ID : ' + str(id))
    return redirect(url_for('reviewboard'))
    
@app.route('/backup_db')
def backup_db(): #NEW PARSER EDIT DB
    ext = str(strftime("%Y_%m_%d_%H_%M_%S",time.localtime()))
    os.system('cp masterdb.db Backups/' + ext + '.db')
    flash('Database Backed up successfully. Thank you for being so deligent about it.')
    return redirect(url_for('reviewboard'))    

if __name__ == '__main__':
    logger.info('Server Started : Navigate to : http://' + get_ip())   
    app.run(host='0.0.0.0', port='80', debug=True, threaded=True)