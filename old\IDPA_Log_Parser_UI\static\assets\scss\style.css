/* This css file is to over write bootstarp css
--------------------------------------------------------- /
* Theme Name: Sufee-Admin Admin Template
* Theme URI: http://demos.jeweltheme.com/Sufee-Admin/
* Author: jewel_theme
* Author URI: http://themeforest.net/user/jewel_theme/portfolio
* Description:
* Version: 1.0.0
* License: GNU General Public License v2 or later
* License URI: http://www.gnu.org/licenses/gpl-2.0.html
* Tags: html, themplate, Sufee-Admin
--------------------------------------------------------- */
/* Bootstrap */
@import url(../css/animate.css);
.gaugejs-wrap {
  position: relative;
  margin: 0 auto; }
  .gaugejs-wrap canvas.gaugejs {
    width: 100% !important;
    height: auto !important; }
  .gaugejs-wrap i, .gaugejs-wrap.sparkline .value {
    top: 50%;
    display: block;
    width: 100%;
    text-align: center; }
  .gaugejs-wrap i {
    position: absolute;
    left: 0;
    z-index: 1000;
    margin-top: -15px;
    font-size: 30px; }
  .gaugejs-wrap.type-2 .value {
    display: block;
    margin-top: -85px; }
  .gaugejs-wrap.type-2 label {
    display: block;
    margin-top: -10px;
    font-size: 10px;
    font-weight: 600;
    color: #9da0a8;
    text-transform: uppercase; }
  .gaugejs-wrap.sparkline {
    position: relative; }
    .gaugejs-wrap.sparkline .value {
      position: absolute;
      margin-top: -5px;
      font-size: 10px;
      line-height: 10px; }

.switch.switch-default {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 40px;
  height: 24px;
  background-color: transparent;
  cursor: pointer; }
  .switch.switch-default .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0; }
  .switch.switch-default .switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: opacity background .15s ease-out; }
  .switch.switch-default .switch-input:checked ~ .switch-label::before {
    opacity: 0; }
  .switch.switch-default .switch-input:checked ~ .switch-label::after {
    opacity: 1; }
  .switch.switch-default .switch-handle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 1px;
    transition: left .15s ease-out; }
  .switch.switch-default .switch-input:checked ~ .switch-handle {
    left: 18px; }
  .switch.switch-default.switch-lg {
    width: 48px;
    height: 28px; }
    .switch.switch-default.switch-lg .switch-label {
      font-size: 12px; }
    .switch.switch-default.switch-lg .switch-handle {
      width: 24px;
      height: 24px; }
    .switch.switch-default.switch-lg .switch-input:checked ~ .switch-handle {
      left: 22px; }
  .switch.switch-default.switch-sm {
    width: 32px;
    height: 20px; }
    .switch.switch-default.switch-sm .switch-label {
      font-size: 8px; }
    .switch.switch-default.switch-sm .switch-handle {
      width: 16px;
      height: 16px; }
    .switch.switch-default.switch-sm .switch-input:checked ~ .switch-handle {
      left: 14px; }
  .switch.switch-default.switch-xs {
    width: 24px;
    height: 16px; }
    .switch.switch-default.switch-xs .switch-label {
      font-size: 7px; }
    .switch.switch-default.switch-xs .switch-handle {
      width: 12px;
      height: 12px; }
    .switch.switch-default.switch-xs .switch-input:checked ~ .switch-handle {
      left: 10px; }

.switch.switch-text {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 48px;
  height: 24px;
  background-color: transparent;
  cursor: pointer; }
  .switch.switch-text .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0; }
  .switch.switch-text .switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: opacity background .15s ease-out; }
  .switch.switch-text .switch-label::before,
  .switch.switch-text .switch-label::after {
    position: absolute;
    top: 50%;
    width: 50%;
    margin-top: -.5em;
    line-height: 1;
    text-align: center;
    transition: inherit; }
  .switch.switch-text .switch-label::before {
    right: 1px;
    color: #e9ecef;
    content: attr(data-off); }
  .switch.switch-text .switch-label::after {
    left: 1px;
    color: #fff;
    content: attr(data-on);
    opacity: 0; }
  .switch.switch-text .switch-input:checked ~ .switch-label::before {
    opacity: 0; }
  .switch.switch-text .switch-input:checked ~ .switch-label::after {
    opacity: 1; }
  .switch.switch-text .switch-handle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 1px;
    transition: left .15s ease-out; }
  .switch.switch-text .switch-input:checked ~ .switch-handle {
    left: 26px; }
  .switch.switch-text.switch-lg {
    width: 56px;
    height: 28px; }
    .switch.switch-text.switch-lg .switch-label {
      font-size: 12px; }
    .switch.switch-text.switch-lg .switch-handle {
      width: 24px;
      height: 24px; }
    .switch.switch-text.switch-lg .switch-input:checked ~ .switch-handle {
      left: 30px; }
  .switch.switch-text.switch-sm {
    width: 40px;
    height: 20px; }
    .switch.switch-text.switch-sm .switch-label {
      font-size: 8px; }
    .switch.switch-text.switch-sm .switch-handle {
      width: 16px;
      height: 16px; }
    .switch.switch-text.switch-sm .switch-input:checked ~ .switch-handle {
      left: 22px; }
  .switch.switch-text.switch-xs {
    width: 32px;
    height: 16px; }
    .switch.switch-text.switch-xs .switch-label {
      font-size: 7px; }
    .switch.switch-text.switch-xs .switch-handle {
      width: 12px;
      height: 12px; }
    .switch.switch-text.switch-xs .switch-input:checked ~ .switch-handle {
      left: 18px; }

.switch.switch-icon {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 48px;
  height: 24px;
  background-color: transparent;
  cursor: pointer; }
  .switch.switch-icon .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0; }
  .switch.switch-icon .switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-family: FontAwesome;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: opacity background .15s ease-out; }
  .switch.switch-icon .switch-label::before,
  .switch.switch-icon .switch-label::after {
    position: absolute;
    top: 50%;
    width: 50%;
    margin-top: -.5em;
    line-height: 1;
    text-align: center;
    transition: inherit; }
  .switch.switch-icon .switch-label::before {
    right: 1px;
    color: #e9ecef;
    content: attr(data-off); }
  .switch.switch-icon .switch-label::after {
    left: 1px;
    color: #fff;
    content: attr(data-on);
    opacity: 0; }
  .switch.switch-icon .switch-input:checked ~ .switch-label::before {
    opacity: 0; }
  .switch.switch-icon .switch-input:checked ~ .switch-label::after {
    opacity: 1; }
  .switch.switch-icon .switch-handle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 1px;
    transition: left .15s ease-out; }
  .switch.switch-icon .switch-input:checked ~ .switch-handle {
    left: 26px; }
  .switch.switch-icon.switch-lg {
    width: 56px;
    height: 28px; }
    .switch.switch-icon.switch-lg .switch-label {
      font-size: 12px; }
    .switch.switch-icon.switch-lg .switch-handle {
      width: 24px;
      height: 24px; }
    .switch.switch-icon.switch-lg .switch-input:checked ~ .switch-handle {
      left: 30px; }
  .switch.switch-icon.switch-sm {
    width: 40px;
    height: 20px; }
    .switch.switch-icon.switch-sm .switch-label {
      font-size: 8px; }
    .switch.switch-icon.switch-sm .switch-handle {
      width: 16px;
      height: 16px; }
    .switch.switch-icon.switch-sm .switch-input:checked ~ .switch-handle {
      left: 22px; }
  .switch.switch-icon.switch-xs {
    width: 32px;
    height: 16px; }
    .switch.switch-icon.switch-xs .switch-label {
      font-size: 7px; }
    .switch.switch-icon.switch-xs .switch-handle {
      width: 12px;
      height: 12px; }
    .switch.switch-icon.switch-xs .switch-input:checked ~ .switch-handle {
      left: 18px; }

.switch.switch-3d {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 40px;
  height: 24px;
  background-color: transparent;
  cursor: pointer; }
  .switch.switch-3d .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0; }
  .switch.switch-3d .switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: opacity background .15s ease-out; }
  .switch.switch-3d .switch-input:checked ~ .switch-label::before {
    opacity: 0; }
  .switch.switch-3d .switch-input:checked ~ .switch-label::after {
    opacity: 1; }
  .switch.switch-3d .switch-handle {
    position: absolute;
    top: 0;
    left: 0;
    width: 24px;
    height: 24px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 1px;
    transition: left .15s ease-out;
    border: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); }
  .switch.switch-3d .switch-input:checked ~ .switch-handle {
    left: 16px; }
  .switch.switch-3d.switch-lg {
    width: 48px;
    height: 28px; }
    .switch.switch-3d.switch-lg .switch-label {
      font-size: 12px; }
    .switch.switch-3d.switch-lg .switch-handle {
      width: 28px;
      height: 28px; }
    .switch.switch-3d.switch-lg .switch-input:checked ~ .switch-handle {
      left: 20px; }
  .switch.switch-3d.switch-sm {
    width: 32px;
    height: 20px; }
    .switch.switch-3d.switch-sm .switch-label {
      font-size: 8px; }
    .switch.switch-3d.switch-sm .switch-handle {
      width: 20px;
      height: 20px; }
    .switch.switch-3d.switch-sm .switch-input:checked ~ .switch-handle {
      left: 12px; }
  .switch.switch-3d.switch-xs {
    width: 24px;
    height: 16px; }
    .switch.switch-3d.switch-xs .switch-label {
      font-size: 7px; }
    .switch.switch-3d.switch-xs .switch-handle {
      width: 16px;
      height: 16px; }
    .switch.switch-3d.switch-xs .switch-input:checked ~ .switch-handle {
      left: 8px; }

.switch-pill .switch-label, .switch.switch-3d .switch-label,
.switch-pill .switch-handle,
.switch.switch-3d .switch-handle {
  border-radius: 50em !important; }
.switch-pill .switch-label::before, .switch.switch-3d .switch-label::before {
  right: 2px !important; }
.switch-pill .switch-label::after, .switch.switch-3d .switch-label::after {
  left: 2px !important; }

.switch-primary > .switch-input:checked ~ .switch-label {
  background: #007bff !important;
  border-color: #0062cc; }
.switch-primary > .switch-input:checked ~ .switch-handle {
  border-color: #0062cc; }

.switch-primary-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #007bff; }
  .switch-primary-outline > .switch-input:checked ~ .switch-label::after {
    color: #007bff; }
.switch-primary-outline > .switch-input:checked ~ .switch-handle {
  border-color: #007bff; }

.switch-primary-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #007bff; }
  .switch-primary-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #007bff; }
.switch-primary-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #007bff !important;
  border-color: #007bff; }

.switch-secondary > .switch-input:checked ~ .switch-label {
  background: #868e96 !important;
  border-color: #6c757d; }
.switch-secondary > .switch-input:checked ~ .switch-handle {
  border-color: #6c757d; }

.switch-secondary-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #868e96; }
  .switch-secondary-outline > .switch-input:checked ~ .switch-label::after {
    color: #868e96; }
.switch-secondary-outline > .switch-input:checked ~ .switch-handle {
  border-color: #868e96; }

.switch-secondary-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #868e96; }
  .switch-secondary-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #868e96; }
.switch-secondary-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #868e96 !important;
  border-color: #868e96; }

.switch-success > .switch-input:checked ~ .switch-label {
  background: #28a745 !important;
  border-color: #1e7e34; }
.switch-success > .switch-input:checked ~ .switch-handle {
  border-color: #1e7e34; }

.switch-success-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #28a745; }
  .switch-success-outline > .switch-input:checked ~ .switch-label::after {
    color: #28a745; }
.switch-success-outline > .switch-input:checked ~ .switch-handle {
  border-color: #28a745; }

.switch-success-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #28a745; }
  .switch-success-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #28a745; }
.switch-success-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #28a745 !important;
  border-color: #28a745; }

.switch-info > .switch-input:checked ~ .switch-label {
  background: #17a2b8 !important;
  border-color: #117a8b; }
.switch-info > .switch-input:checked ~ .switch-handle {
  border-color: #117a8b; }

.switch-info-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #17a2b8; }
  .switch-info-outline > .switch-input:checked ~ .switch-label::after {
    color: #17a2b8; }
.switch-info-outline > .switch-input:checked ~ .switch-handle {
  border-color: #17a2b8; }

.switch-info-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #17a2b8; }
  .switch-info-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #17a2b8; }
.switch-info-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #17a2b8 !important;
  border-color: #17a2b8; }

.switch-warning > .switch-input:checked ~ .switch-label {
  background: #ffc107 !important;
  border-color: #d39e00; }
.switch-warning > .switch-input:checked ~ .switch-handle {
  border-color: #d39e00; }

.switch-warning-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #ffc107; }
  .switch-warning-outline > .switch-input:checked ~ .switch-label::after {
    color: #ffc107; }
.switch-warning-outline > .switch-input:checked ~ .switch-handle {
  border-color: #ffc107; }

.switch-warning-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #ffc107; }
  .switch-warning-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #ffc107; }
.switch-warning-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #ffc107 !important;
  border-color: #ffc107; }

.switch-danger > .switch-input:checked ~ .switch-label {
  background: #dc3545 !important;
  border-color: #bd2130; }
.switch-danger > .switch-input:checked ~ .switch-handle {
  border-color: #bd2130; }

.switch-danger-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #dc3545; }
  .switch-danger-outline > .switch-input:checked ~ .switch-label::after {
    color: #dc3545; }
.switch-danger-outline > .switch-input:checked ~ .switch-handle {
  border-color: #dc3545; }

.switch-danger-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #dc3545; }
  .switch-danger-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #dc3545; }
.switch-danger-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #dc3545 !important;
  border-color: #dc3545; }

.switch-light > .switch-input:checked ~ .switch-label {
  background: #f8f9fa !important;
  border-color: #dae0e5; }
.switch-light > .switch-input:checked ~ .switch-handle {
  border-color: #dae0e5; }

.switch-light-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #f8f9fa; }
  .switch-light-outline > .switch-input:checked ~ .switch-label::after {
    color: #f8f9fa; }
.switch-light-outline > .switch-input:checked ~ .switch-handle {
  border-color: #f8f9fa; }

.switch-light-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #f8f9fa; }
  .switch-light-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #f8f9fa; }
.switch-light-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #f8f9fa !important;
  border-color: #f8f9fa; }

.switch-dark > .switch-input:checked ~ .switch-label {
  background: #343a40 !important;
  border-color: #1d2124; }
.switch-dark > .switch-input:checked ~ .switch-handle {
  border-color: #1d2124; }

.switch-dark-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #343a40; }
  .switch-dark-outline > .switch-input:checked ~ .switch-label::after {
    color: #343a40; }
.switch-dark-outline > .switch-input:checked ~ .switch-handle {
  border-color: #343a40; }

.switch-dark-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #343a40; }
  .switch-dark-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #343a40; }
.switch-dark-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #343a40 !important;
  border-color: #343a40; }

.social-box {
  min-height: 160px;
  margin-bottom: 1.5rem;
  text-align: center;
  background: #fff; }
  .social-box i {
    display: block;
    margin: -1px -1px 0;
    font-size: 40px;
    line-height: 90px;
    background: #e9ecef; }
  .social-box .chart-wrapper {
    height: 90px;
    margin: -90px 0 0; }
    .social-box .chart-wrapper canvas {
      width: 100% !important;
      height: 90px !important; }
  .social-box ul {
    padding: 10px 0;
    list-style: none; }
    .social-box ul li {
      display: block;
      float: left;
      width: 50%;
      padding-top: 10px;
      font-size: 18px; }
      .social-box ul li:first-child {
        border-right: 1px solid #c2cfd6; }
      .social-box ul li strong {
        display: block;
        font-size: 20px; }
      .social-box ul li span {
        font-size: 18px;
        font-weight: 500;
        color: #949CA0;
        text-transform: uppercase; }
  .social-box.facebook i {
    color: #fff;
    background: #3b5998; }
  .social-box.twitter i {
    color: #fff;
    background: #00aced; }
  .social-box.linkedin i {
    color: #fff;
    background: #4875b4; }
  .social-box.google-plus i {
    color: #fff;
    background: #d34836; }

.horizontal-bars {
  padding: 0;
  margin: 0;
  list-style: none; }
  .horizontal-bars li {
    position: relative;
    height: 40px;
    line-height: 40px;
    vertical-align: middle; }
    .horizontal-bars li .title {
      width: 100px;
      font-size: 12px;
      font-weight: 600;
      color: #868e96;
      vertical-align: middle; }
    .horizontal-bars li .bars {
      position: absolute;
      top: 15px;
      width: 100%;
      padding-left: 100px; }
      .horizontal-bars li .bars .progress:first-child {
        margin-bottom: 2px; }
    .horizontal-bars li.legend {
      text-align: center; }
      .horizontal-bars li.legend .badge {
        display: inline-block;
        width: 8px;
        height: 8px;
        padding: 0; }
    .horizontal-bars li.divider {
      height: 40px; }
      .horizontal-bars li.divider i {
        margin: 0 !important; }
  .horizontal-bars.type-2 li {
    overflow: hidden; }
    .horizontal-bars.type-2 li i {
      display: inline-block;
      margin-right: 1rem;
      margin-left: 5px;
      font-size: 18px;
      line-height: 40px; }
    .horizontal-bars.type-2 li .title {
      display: inline-block;
      width: auto;
      margin-top: -9px;
      font-size: 1rem;
      font-weight: normal;
      line-height: 40px;
      color: #212529; }
    .horizontal-bars.type-2 li .value {
      float: right;
      font-weight: 600; }
    .horizontal-bars.type-2 li .bars {
      position: absolute;
      top: auto;
      bottom: 0;
      padding: 0; }

.icons-list {
  padding: 0;
  margin: 0;
  list-style: none; }
  .icons-list li {
    position: relative;
    height: 40px;
    vertical-align: middle; }
    .icons-list li i {
      display: block;
      float: left;
      width: 35px !important;
      height: 35px !important;
      margin: 2px;
      line-height: 35px !important;
      text-align: center; }
    .icons-list li .desc {
      height: 40px;
      margin-left: 50px;
      border-bottom: 1px solid #e9ecef; }
      .icons-list li .desc .title {
        padding: 2px 0 0;
        margin: 0; }
      .icons-list li .desc small {
        display: block;
        margin-top: -4px;
        color: #868e96; }
    .icons-list li .value {
      position: absolute;
      top: 2px;
      right: 45px;
      text-align: right; }
      .icons-list li .value strong {
        display: block;
        margin-top: -3px; }
    .icons-list li .actions {
      position: absolute;
      top: -4px;
      right: 10px;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center; }
      .icons-list li .actions i {
        float: none;
        width: auto;
        height: auto;
        padding: 0;
        margin: 0;
        line-height: normal; }
    .icons-list li.divider {
      height: 40px; }
      .icons-list li.divider i {
        width: auto;
        height: auto;
        margin: 2px 0 0;
        font-size: 18px; }

.bg-flat-color-1 {
  background: #20a8d8; }

.bg-flat-color-2 {
  background: #63c2de; }

.bg-flat-color-3 {
  background: #ffc107; }

.bg-flat-color-4 {
  background: #f86c6b; }

.bg-flat-color-5 {
  background: #4dbd74; }

.transition {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease; }

body {
  background: #f1f2f7;
  display: table;
  font-family: 'Open Sans' sans-serif !important;
  font-size: 16px;
  width: 100%; }

div[class*="col-"] {
  float: left; }

p {
  font-size: 16px;
  font-family: 'Open Sans' sans-serif;
  font-weight: 400;
  line-height: 24px;
  color: #878787; }

p:focus {
  border: none;
  outline: 0; }

a, button {
  text-decoration: none;
  outline: none !important;
  color: #878787;
  -webkit-transition: all 0.25s ease;
  -moz-transition: all 0.25s ease;
  -ms-transition: all 0.25s ease;
  -o-transition: all 0.25s ease;
  transition: all 0.25s ease; }

a:hover,
a:focus {
  text-decoration: none;
  color: #000; }

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0; }

ul,
ol {
  padding-left: 0; }

.btn:focus,
button:focus {
  box-shadow: none !important;
  outline: 0; }

img {
  max-width: 100%; }

.btn,
button,
input,
textarea {
  box-shadow: none;
  outline: 0 !important; }

.no-padding {
  padding: 0 !important; }

/* Global Styles */
/* Main Styles */
.basix-container {
  display: table;
  min-height: 100vh;
  position: relative;
  width: 100%; }

aside.left-panel {
  background: #272c33;
  display: table-cell;
  height: 100vh;
  min-height: 100%;
  padding: 0 25px;
  vertical-align: top;
  width: 280px;
  transition: width 0.3s ease; }

.navbar {
  background: #272c33;
  border-radius: 0;
  border: none;
  display: block;
  margin: 0;
  margin-bottom: 100px;
  padding: 0; }
  .navbar .navbar-header {
    float: none;
    text-align: center;
    width: 100%; }
  .navbar .navbar-brand {
    border-bottom: 1px solid #4e4e52;
    color: #f1f2f7 !important;
    font-family: 'Open Sans';
    font-size: 22px;
    float: none;
    line-height: 50px;
    margin: 0;
    text-align: left;
    text-transform: capitalize;
    display: block;
    min-height: 69px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative; }
    .navbar .navbar-brand span {
      font-weight: 600; }
    .navbar .navbar-brand img {
      max-width: 160px; }
    .navbar .navbar-brand.hidden {
      display: none; }
  .navbar .menu-title {
    border-bottom: 1px solid #4e4e52;
    color: #9496a1;
    clear: both;
    display: block;
    font-family: 'Open Sans';
    font-size: 14px;
    font-weight: 700;
    line-height: 50px;
    padding: 15px 0 0 0;
    text-transform: uppercase;
    width: 100%; }
  .navbar .navbar-nav {
    float: none;
    position: relative; }
    .navbar .navbar-nav li {
      width: 100%; }
      .navbar .navbar-nav li.active .menu-icon, .navbar .navbar-nav li:hover .toggle_nav_button:before,
      .navbar .navbar-nav li .toggle_nav_button.nav-open:before {
        color: #fff !important; }
      .navbar .navbar-nav li .dropdown-toggle:after {
        display: none; }
      .navbar .navbar-nav li > a {
        background: none !important;
        color: #c8c9ce !important;
        display: inline-block;
        font-family: 'Open Sans';
        font-size: 14px;
        line-height: 30px;
        padding: 10px 0;
        position: relative;
        width: 100%; }
        .navbar .navbar-nav li > a:hover, .navbar .navbar-nav li > a:hover .menu-icon {
          color: #fff !important; }
        .navbar .navbar-nav li > a .menu-icon {
          color: #8b939b;
          float: left;
          margin-top: 8px;
          width: 55px;
          text-align: left;
          z-index: 9; }
        .navbar .navbar-nav li > a .menu-title-text {
          font-size: 14px; }
        .navbar .navbar-nav li > a .badge {
          border-radius: 0;
          font-family: 'Open Sans';
          font-weight: 600;
          float: right;
          margin: 6px 0 0 0;
          padding: 0.4em 0.5em; }
      .navbar .navbar-nav li.menu-item-has-children {
        position: relative; }
        .navbar .navbar-nav li.menu-item-has-children a {
          line-height: 30px; }
          .navbar .navbar-nav li.menu-item-has-children a:before {
            content: "\f105";
            color: #c8c9ce;
            font-family: 'Fontawesome';
            font-size: 16px;
            position: absolute;
            top: 10px;
            right: 0;
            text-align: right;
            -webkit-transition: all .25s ease;
            -moz-transition: all .25s ease;
            -ms-transition: all .25s ease;
            -o-transition: all .25s ease;
            transition: all .25s ease; }
          .navbar .navbar-nav li.menu-item-has-children a:hover:before {
            color: #fff; }
        .navbar .navbar-nav li.menu-item-has-children .sub-menu {
          background: #272c33;
          border: none;
          box-shadow: none;
          overflow-y: hidden;
          padding: 0 0 0 35px; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu li {
            position: relative; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu i {
            color: #c8c9ce;
            float: left;
            padding: 0;
            position: absolute;
            left: 0;
            font-size: 14px;
            top: 9px; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu a {
            padding: 2px 0 2px 30px; }
            .navbar .navbar-nav li.menu-item-has-children .sub-menu a:before {
              content: '';
              display: none; }
            .navbar .navbar-nav li.menu-item-has-children .sub-menu a .menu-icon {
              top: 13px;
              text-align: left;
              width: 25px; }
        .navbar .navbar-nav li.menu-item-has-children.show a:before {
          content: "\f107"; }
        .navbar .navbar-nav li.menu-item-has-children.show .sub-menu {
          max-height: 1000px;
          opacity: 1;
          position: static !important; }

.navbar .navbar-nav > .active > a,
.navbar .navbar-nav > .active > a:focus,
.navbar .navbar-nav > .active > a:hover {
  color: #d7d9e3 !important; }

.navbar-nav li span.count {
  background: #a9d86e;
  border-radius: 50%;
  color: #fff;
  font-family: 'Open Sans';
  font-size: 9px;
  font-weight: 700;
  float: right;
  height: 20px;
  width: 20px;
  line-height: 20px;
  margin-right: 15px;
  text-align: center; }

body.open .navbar .navbar-brand.hidden {
  display: block; }

.open aside.left-panel {
  max-width: 70px;
  width: 70px; }
  .open aside.left-panel .navbar .navbar-brand {
    display: none; }
    .open aside.left-panel .navbar .navbar-brand.hidden {
      display: flex !important;
      justify-content: center;
      align-items: center;
      padding-left: 0;
      padding-right: 0;
      text-align: center; }
      .open aside.left-panel .navbar .navbar-brand.hidden img {
        max-width: 30px;
        margin: 0 auto; }
    .open aside.left-panel .navbar .navbar-brand.d-md-none {
      display: block !important;
      margin: 13px 0 0;
      min-height: 67px;
      padding: 0;
      text-align: center; }
  .open aside.left-panel .navbar .navbar-nav:before {
    display: none !important; }
  .open aside.left-panel .navbar .navbar-nav li {
    position: relative; }
    .open aside.left-panel .navbar .navbar-nav li a {
      font-size: 0;
      z-index: 0;
      transition: none; }
      .open aside.left-panel .navbar .navbar-nav li a .menu-icon {
        font-size: 20px;
        z-index: -1;
        width: inherit; }
      .open aside.left-panel .navbar .navbar-nav li a .menu-title-text {
        font-size: 0; }
      .open aside.left-panel .navbar .navbar-nav li a .badge {
        display: none; }
    .open aside.left-panel .navbar .navbar-nav li > a {
      max-width: 60px;
      padding-left: 0; }
    .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children {
      overflow: hidden; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children a:before {
        content: '';
        display: none; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children ul {
        padding-left: 0; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu {
        display: block;
        left: inherit;
        right: -180px;
        top: 0; }
        .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu li a {
          display: block;
          font-size: 14px;
          max-width: inherit;
          padding: 2px 15px 2px 25px;
          width: 100%; }
          .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu li a .menu-icon {
            text-align: center; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children.show {
        overflow: visible; }
        .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children.show .sub-menu {
          position: absolute !important; }
    .open aside.left-panel .navbar .navbar-nav li span.count {
      display: none;
      margin-right: 5px;
      z-index: 1; }
    .open aside.left-panel .navbar .navbar-nav li.active a:after {
      content: '';
      display: none; }
  .open aside.left-panel .navbar .navbar-nav .menu-title {
    font-size: 0;
    line-height: 0;
    opacity: 0;
    padding: 0; }

/* Right panel */
.right-panel {
  display: table-cell;
  padding-left: 0 !important;
  -webkit-transition: all .35s ease;
  -moz-transition: all .35s ease;
  -ms-transition: all .35s ease;
  -o-transition: all .35s ease;
  transition: all .35s ease; }
  .right-panel .breadcrumbs {
    background-color: #fff;
    display: inline-block;
    margin-top: 0;
    padding: 0 5px;
    width: 100%; }
    .right-panel .breadcrumbs .col-lg-8 .page-header {
      float: left; }
  .right-panel .page-header {
    min-height: 50px;
    margin: 0px;
    padding: 0px 15px;
    background: #ffffff;
    border-bottom: 0px; }
    .right-panel .page-header h1 {
      font-size: 18px;
      padding: 15px 0; }
    .right-panel .page-header .breadcrumb {
      margin: 0px;
      padding: 13.5px 0;
      background: #fff;
      text-transform: capitalize; }
    .right-panel .page-header .breadcrumb > li + li:before {
      padding: 0 5px;
      color: #ccc;
      content: "/\00a0"; }

.right-panel header.header {
  background: #fff;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
  clear: both;
  display: inline-block;
  padding: 15px 20px 13px 20px;
  width: 100%; }

.open .right-panel {
  margin-left: -210px; }

header.fixed-top {
  background: #fff;
  padding: 20px; }

.header-menu .col-sm-7 {
  position: inherit; }

.menutoggle {
  background: #e74c3c;
  border-radius: 50%;
  color: #fff !important;
  cursor: pointer;
  font-size: 18px;
  height: 43px;
  line-height: 44px;
  margin: -2px 20px 0 -57px;
  text-align: center;
  width: 43px; }

.open .menutoggle i:before {
  content: "\f0a4"; }

.search-trigger {
  background: transparent;
  border: none;
  color: #272c33;
  cursor: pointer;
  font-size: 16px;
  height: 41px;
  width: 43px;
  line-height: 38px; }

header .form-inline {
  background: #263238;
  display: none;
  height: 70px;
  margin: 0;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 9999; }
  header .form-inline .search-form {
    height: 100%;
    max-width: 1025px;
    margin: 0 auto;
    position: relative; }
    header .form-inline .search-form input[type="text"] {
      background: #263238;
      border: none;
      border-radius: 0;
      box-shadow: none;
      color: #d3d3d3;
      font-size: 16px;
      height: inherit;
      margin-right: 0 !important;
      padding: 10px 36px 10px 15px;
      width: 100%; }
    header .form-inline .search-form input[type="text"].active,
    header .form-inline .search-form input[type="text"]:focus {
      border-color: rgba(0, 0, 0, 0.125);
      outline: 0; }
    header .form-inline .search-form button {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 16px;
      position: absolute;
      right: 15px;
      top: 50%;
      margin-top: -14px !important; }
    header .form-inline .search-form button:active,
    header .form-inline .search-form button:focus,
    header .form-inline .search-form button:visited,
    header .form-inline .search-form .btn-outline-success:hover {
      background: transparent;
      border: none !important;
      box-shadow: none;
      outline: 0 !important; }
    header .form-inline .search-form.close {
      display: none; }

.header-left.open .form-inline {
  display: block; }

.header-left .dropdown {
  display: inline-block; }
  .header-left .dropdown .dropdown-toggle {
    background: transparent;
    border: none;
    color: #272c33;
    font-size: 16px; }
    .header-left .dropdown .dropdown-toggle:after {
      display: none; }
    .header-left .dropdown .dropdown-toggle .count {
      border-radius: 50%;
      color: #fff;
      font-size: 11px;
      height: 15px;
      width: 15px;
      line-height: 15px;
      right: 0;
      top: 0;
      position: absolute; }
    .header-left .dropdown .dropdown-toggle:active, .header-left .dropdown .dropdown-toggle:focus, .header-left .dropdown .dropdown-toggle:visited {
      background: none !important;
      border-color: transparent !important;
      color: #272c33 !important; }
  .header-left .dropdown .dropdown-menu {
    background: #fff;
    border: none;
    border-radius: 0;
    box-shadow: none;
    top: 49px !important; }
    .header-left .dropdown .dropdown-menu p {
      font-size: 15px;
      margin: 0;
      padding: 5px 15px; }
    .header-left .dropdown .dropdown-menu .dropdown-item {
      color: #272c33;
      font-size: 13px;
      padding: 10px 15px 3px;
      text-overflow: ellipsis; }
      .header-left .dropdown .dropdown-menu .dropdown-item .photo {
        float: left;
        margin-right: 15px;
        width: 25px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message .name {
        margin-top: -5px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message .time {
        font-size: 11px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message p {
        clear: both;
        font-size: 14px;
        margin: 0;
        padding: 0;
        text-overflow: ellipsis; }
      .header-left .dropdown .dropdown-menu .dropdown-item:hover {
        background: transparent; }

.dropdown-menu {
  border-radius: 0;
  transform: none !important; }

.for-notification .dropdown-menu .dropdown-item {
  padding: 5px 15px !important;
  text-overflow: ellipsis; }
  .for-notification .dropdown-menu .dropdown-item i {
    float: left;
    font-size: 14px;
    margin: 5px 5px 0 0;
    text-align: left;
    width: 20px; }
  .for-notification .dropdown-menu .dropdown-item p {
    padding: 0 !important;
    text-overflow: ellipsis; }

.user-area {
  float: right;
  padding-right: 0;
  position: relative; }
  .user-area .user-menu {
    background: #fff;
    border: none;
    font-family: 'Open Sans';
    left: inherit !important;
    right: 0;
    top: 55px !important;
    margin: 0;
    max-width: 150px;
    padding: 5px 10px;
    position: absolute;
    width: 100%;
    z-index: 999;
    min-width: 150px; }
    .user-area .user-menu .nav-link {
      color: #272c33;
      display: block;
      font-size: 14px;
      line-height: 22px;
      padding: 5px 0; }
  .user-area .user-avatar {
    float: right;
    margin-top: 4px;
    width: 32px; }
  .user-area .user-info .name {
    color: #8c8c8c;
    font-size: 14px;
    position: relative;
    text-transform: uppercase; }
  .user-area .count {
    background: #d9534f;
    border-radius: 50%;
    color: #fff;
    font-family: 'Open Sans';
    font-size: 9px;
    font-weight: 700;
    float: right;
    height: 20px;
    width: 20px;
    line-height: 20px;
    text-align: center; }
  .user-area .dropdown-toggle:after {
    display: none; }

#menuToggle2 {
  padding-left: 25px; }

#language-select {
  color: #f1f2f7;
  float: right;
  margin: 7px 20px 0 0;
  max-width: 80px; }
  #language-select:focus, #language-select:visited {
    border: none;
    outline: 0; }
  #language-select .dropdown-toggle::after {
    display: none; }
  #language-select .dropdown-menu {
    background: #fff;
    border: none;
    border-radius: 0;
    left: -8px !important;
    min-width: inherit;
    padding: 0 5px;
    top: 46px !important; }
    #language-select .dropdown-menu .dropdown-item {
      margin-right: 0;
      max-width: 25px;
      padding: 0; }
      #language-select .dropdown-menu .dropdown-item:hover {
        background: #fff; }
      #language-select .dropdown-menu .dropdown-item .flag-icon {
        margin-right: 0;
        width: 25px; }

.notification-show + .dropdown-menu,
.message-show + .dropdown-menu,
.language-show + .dropdown-menu {
  display: block; }

.content {
  float: left;
  padding: 0 20px;
  width: 100%; }

.card {
  margin-bottom: 1.5rem;
  border-radius: 0; }
  .card h4 {
    font-size: 1.1rem; }
  .card .user-header .media img {
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    -webkit-border-radius: 50%; }
  .card .card-header .card-actions button {
    display: block;
    float: left;
    width: 50px;
    padding: .75rem 0;
    margin: 0 !important;
    color: #fff;
    outline: 0;
    text-align: center;
    background: transparent;
    border: 0;
    border-left: 1px solid rgba(120, 130, 140, 0.4); }
  .card .card-footer {
    padding: 0.65rem 1.25rem;
    background-color: #f0f3f5;
    border-top: 1px solid #c2cfd6; }
    .card .card-footer ul li {
      display: table-cell;
      padding: 0 1rem;
      text-align: center; }

.breadcrumbs {
  margin-top: 0; }

/* Tabs */
.nav-tabs a.active {
  color: #555;
  cursor: default;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent; }
.nav-tabs .dropdown .dropdown-menu {
  top: 100% !important; }

.custom-tab .nav-tabs > a.active, .custom-tab .nav-tabs > .active > a:focus, .custom-tab .nav-tabs > li.active > a:hover {
  border-color: transparent transparent;
  color: #ff2e44;
  position: relative; }

.custom-tab .nav-tabs > a.active > a:after, .custom-tab .nav-tabs > li.active > a:focus:after, .custom-tab .nav-tabs > li.active > a:hover:after {
  background: #ff2e44;
  bottom: -1px;
  content: "";
  height: 2px;
  left: 0;
  position: absolute;
  right: 0;
  width: 100%;
  z-index: 999; }

.card .card-header .card-actions {
  float: right; }
  .card .card-header .card-actions [class*="btn"] {
    border-left: 1px solid rgba(120, 130, 140, 0.4);
    color: #878787;
    display: inline-block;
    font-size: 16px;
    float: left;
    padding: 0 7px;
    width: inherit;
    text-align: center; }

.social-buttons .card-body p button {
  padding-top: 0;
  padding-left: 0;
  padding-bottom: 0; }
.social-buttons .only-icon .card-body p button {
  padding: 0; }
.social-buttons .social i {
  padding: 0 10px;
  width: inherit !important; }
.social-buttons .only-text p button {
  padding: 0 .5rem; }

.buttons button {
  margin: 2px 0; }

/* Ribons */
.corner-ribon {
  text-align: center;
  width: 71px;
  height: 71px;
  position: absolute;
  right: 0;
  top: 0;
  font-size: 20px; }

.corner-ribon i {
  padding: 10px 0 0 35px;
  color: #fff; }

.black-ribon {
  background: url("../../images/twitter_corner_black.png") no-repeat; }

.blue-ribon {
  background: url("../../images/twitter_corner_blue.png") no-repeat; }

.twt-feed .wtt-mark {
  color: rgba(255, 255, 255, 0.15);
  font-size: 160px;
  position: absolute;
  top: 10px;
  left: 40%; }

.twt-feed {
  -webkit-border-radius: 4px 4px 0 0;
  color: #FFFFFF;
  padding: 40px 10px 10px;
  position: relative;
  min-height: 170px; }

.weather-category {
  padding: 15px 0;
  color: #74829C; }
  .weather-category ul li {
    width: 32%;
    text-align: center;
    border-right: 1px solid #e6e6e6;
    display: inline-block; }

.twt-feed.blue-bg {
  background: #58C9F3; }

.twt-category {
  display: inline-block;
  margin-bottom: 11px;
  margin-top: 10px;
  width: 100%; }
  .twt-category ul li {
    color: #bdbdbd;
    font-size: 13px; }

.twt-footer {
  padding: 12px 15px; }

.twt-footer, .twt-footer a {
  color: #d2d2d2; }

/* Button Reset */
.btn, .button {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  transition: all .15s ease-in-out;
  border-radius: 0;
  cursor: pointer; }

/* Icons */
.icon-section {
  margin: 0 0 3em;
  clear: both;
  overflow: hidden; }

.icon-container {
  width: 240px;
  padding: .7em 0;
  float: left;
  position: relative;
  text-align: left; }

.icon-container [class^="ti-"],
.icon-container [class*=" ti-"] {
  color: #000;
  position: absolute;
  margin-top: 3px;
  transition: .3s; }

.icon-container:hover [class^="ti-"],
.icon-container:hover [class*=" ti-"] {
  font-size: 2.2em;
  margin-top: -5px; }

.icon-container:hover .icon-name {
  color: #000; }

.icon-name {
  color: #aaa;
  margin-left: 35px;
  font-size: 14px;
  transition: .3s; }

.icon-container:hover .icon-name {
  margin-left: 45px; }

.fontawesome-icon-list .page-header {
  border-bottom: 1px solid #C9CDD7;
  padding-bottom: 9px;
  margin: 30px 0px 27px 0px; }
.fontawesome-icon-list h2 {
  margin-top: 0;
  font-size: 20px;
  font-weight: 300; }
.fontawesome-icon-list i {
  font-style: 16px;
  padding-right: 10px; }

.social-box i {
  line-height: 110px; }
.social-box ul {
  display: inline-block;
  margin: 7px 0 0;
  padding: 10px;
  width: 100%; }
  .social-box ul li {
    color: #949CA0;
    font-size: 14px;
    font-weight: 700;
    padding: 0 10px 0 0;
    text-align: right; }
    .social-box ul li:last-child {
      padding-left: 10px;
      padding-right: 0;
      text-align: left; }
    .social-box ul li span {
      font-size: 14px; }

.login-logo {
  text-align: center;
  margin-bottom: 15px; }
  .login-logo span {
    color: #ffffff;
    font-size: 24px; }

.login-content {
  max-width: 540px;
  margin: 8vh auto; }

.login-form {
  background: #ffffff;
  padding: 30px 30px 20px;
  border-radius: 2px; }

.login-form h4 {
  color: #878787;
  text-align: center;
  margin-bottom: 50px; }

.login-form .checkbox {
  color: #878787; }

.login-form .checkbox label {
  text-transform: none; }

.login-form .btn {
  width: 100%;
  text-transform: uppercase;
  font-size: 14px;
  padding: 15px;
  border: 0px; }

.login-form label {
  color: #878787;
  text-transform: uppercase; }

.login-form label a {
  color: #ff2e44; }

.social-login-content {
  margin: 0px -30px;
  border-top: 1px solid #e7e7e7;
  border-bottom: 1px solid #e7e7e7;
  padding: 30px 0px;
  background: #fcfcfc; }

.social-button {
  padding: 0 30px; }
  .social-button .facebook {
    background: #3b5998;
    color: #fff; }
    .social-button .facebook:hover {
      background: #344e86; }
  .social-button .twitter {
    background: #00aced;
    color: #fff; }
    .social-button .twitter:hover {
      background: #0099d4; }

.social-button i {
  padding: 19px; }

.register-link a {
  color: #ff2e44; }

.cpu-load {
  width: 100%;
  height: 272px;
  font-size: 14px;
  line-height: 1.2em; }

.cpu-load-data-content {
  font-size: 18px;
  font-weight: 400;
  line-height: 40px; }

.cpu-load-data {
  margin-bottom: 30px; }

.cpu-load-data li {
  display: inline-block;
  width: 32.5%;
  text-align: center;
  border-right: 1px solid #e7e7e7; }

.cpu-load-data li:last-child {
  border-right: 0px; }

.nestable-cart {
  overflow: hidden; }

/* Forms */
.input-group-addon {
  background-color: transparent;
  border-left: 0; }

.input-group-addon, .input-group-btn {
  white-space: nowrap;
  vertical-align: middle; }

.input-group-addon {
  padding: .5rem .75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.25;
  color: #495057;
  text-align: center;
  background-color: #e9ecef;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: .25rem; }

.flotTip {
  background: #252525;
  border: 1px solid #252525;
  padding: 5px 15px;
  color: #ffffff; }

.flot-container {
  box-sizing: border-box;
  width: 100%;
  height: 275px;
  padding: 20px 15px 15px;
  margin: 15px auto 30px;
  background: transparent; }

.flot-pie-container {
  height: 275px; }

.flotBar-container {
  height: 275px; }

.flot-line {
  width: 100%;
  height: 100%;
  font-size: 14px;
  line-height: 1.2em; }

.legend table {
  border-spacing: 5px; }

#chart1,
#flotBar,
#flotCurve {
  width: 100%;
  height: 275px; }

.morris-hover {
  position: absolute;
  z-index: 1; }

.morris-hover.morris-default-style .morris-hover-row-label {
  font-weight: bold;
  margin: 0.25em 0; }

.morris-hover.morris-default-style .morris-hover-point {
  white-space: nowrap;
  margin: 0.1em 0; }

.morris-hover.morris-default-style {
  border-radius: 2px;
  padding: 10px 12px;
  color: #666;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff !important; }

.morris-hover-point {
  color: rgba(255, 255, 255, 0.8) !important; }

#morris-bar-chart {
  height: 285px; }

.map, .vmap {
  width: 100%;
  height: 400px; }

.btn-toolbar {
  float: left !important; }
  .btn-toolbar .btn-outline-secondary:not([disabled]):not(.disabled):active,
  .btn-toolbar .btn-outline-secondary:not([disabled]):not(.disabled).active,
  .btn-toolbar .show > .btn-outline-secondary.dropdown-toggle {
    background-color: #212529;
    border-color: #212529;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #fff; }
  .btn-toolbar .btn-outline-secondary:hover {
    background-color: #212529;
    border-color: #212529;
    color: #fff; }

/*    Widget One
---------------------------*/
.dib {
  display: inline-block; }

.stat-widget-one .stat-icon {
  vertical-align: top; }

.stat-widget-one .stat-icon i {
  font-size: 30px;
  border-width: 3px;
  border-style: solid;
  border-radius: 100px;
  padding: 15px;
  font-weight: 900;
  display: inline-block; }

.stat-widget-one .stat-content {
  margin-left: 30px;
  margin-top: 7px; }

.stat-widget-one .stat-text {
  font-size: 14px;
  color: #868e96; }

.stat-widget-one .stat-digit {
  font-size: 24px;
  color: #373757; }

/*    Widget Two
---------------------------*/
.stat-widget-two {
  text-align: center; }

.stat-widget-two .stat-digit {
  font-size: 1.75rem;
  font-weight: 500;
  color: #373757; }

.stat-widget-two .stat-digit i {
  font-size: 18px;
  margin-right: 5px; }

.stat-widget-two .stat-text {
  font-size: 16px;
  margin-bottom: 5px;
  color: #868e96; }

.stat-widget-two .progress {
  height: 8px;
  margin-bottom: 0;
  margin-top: 20px;
  box-shadow: none; }

.stat-widget-two .progress-bar {
  box-shadow: none; }

/*    Widget Three
---------------------------*/
.stat-widget-three .stat-icon {
  display: inline-block;
  padding: 33px;
  position: absolute;
  line-height: 21px; }

.stat-widget-three .stat-icon i {
  font-size: 30px;
  color: #ffffff; }

.stat-widget-three .stat-content {
  text-align: center;
  padding: 15px;
  margin-left: 90px; }

.stat-widget-three .stat-digit {
  font-size: 30px; }

.stat-widget-three .stat-text {
  padding-top: 4px; }

.home-widget-three .stat-icon {
  line-height: 19px;
  padding: 27px; }

.home-widget-three .stat-digit {
  font-size: 24px;
  font-weight: 300;
  color: #373757; }

.home-widget-three .stat-content {
  text-align: center;
  margin-left: 60px;
  padding: 13px; }

.stat-widget-four {
  position: relative; }

.stat-widget-four .stat-icon {
  display: inline-block;
  position: absolute;
  top: 5px; }

.stat-widget-four i {
  display: block;
  font-size: 36px; }

.stat-widget-four .stat-content {
  margin-left: 40px;
  text-align: center; }

.stat-widget-four .stat-heading {
  font-size: 20px; }

.stat-widget-five .stat-icon {
  border-radius: 100px;
  display: inline-block;
  position: absolute; }

.stat-widget-five i {
  border-radius: 100px;
  display: block;
  font-size: 36px;
  padding: 30px; }

.stat-widget-five .stat-content {
  margin-left: 100px;
  padding: 24px 0;
  position: relative;
  text-align: right;
  vertical-align: middle; }

.stat-widget-five .stat-heading {
  text-align: right;
  padding-left: 80px;
  font-size: 20px;
  font-weight: 200; }

.stat-widget-six {
  position: relative; }

.stat-widget-six .stat-icon {
  display: inline-block;
  position: absolute;
  top: 5px; }

.stat-widget-six i {
  display: block;
  font-size: 36px; }

.stat-widget-six .stat-content {
  margin-left: 40px;
  text-align: center; }

.stat-widget-six .stat-heading {
  font-size: 16px;
  font-weight: 300; }

.stat-widget-six .stat-text {
  font-size: 12px;
  padding-top: 4px; }

.stat-widget-seven .stat-heading {
  text-align: center; }

.stat-widget-seven .gradient-circle {
  text-align: center;
  position: relative;
  margin: 30px auto;
  display: inline-block;
  width: 100%; }

.stat-widget-seven .gradient-circle i {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  top: 35px;
  font-size: 30px; }

.stat-widget-seven .stat-footer {
  text-align: center;
  margin-top: 30px; }

.stat-widget-seven .stat-footer .stat-count {
  padding-left: 5px; }

.stat-widget-seven .count-header {
  color: #252525;
  font-size: 12px;
  font-weight: 400;
  line-height: 30px; }

.stat-widget-seven .stat-count {
  font-size: 18px;
  font-weight: 400;
  color: #252525; }

.stat-widget-seven .analytic-arrow {
  position: relative; }

.stat-widget-seven .analytic-arrow i {
  font-size: 12px; }

/* Stat widget Eight
--------------------------- */
.stat-widget-eight {
  padding: 15px; }

.stat-widget-eight .header-title {
  font-size: 20px;
  font-weight: 300; }

.stat-widget-eight .ti-more-alt {
  color: #878787;
  cursor: pointer;
  left: -5px;
  position: absolute;
  transform: rotate(90deg); }

.stat-widget-eight .stat-content {
  margin-top: 50px; }

.stat-widget-eight .stat-content .ti-arrow-up {
  font-size: 30px;
  color: #28a745; }

.stat-widget-eight .stat-content .stat-digit {
  font-size: 24px;
  font-weight: 300;
  margin-left: 15px; }

.stat-widget-eight .stat-content .progress-stats {
  color: #aaadb2;
  font-weight: 400;
  position: relative;
  top: 10px; }

.stat-widget-eight .progress {
  margin-bottom: 0;
  margin-top: 30px;
  height: 7px;
  background: #EAEAEA;
  box-shadow: none; }

.stat-widget-nine .all-like {
  float: right; }

.stat-widget-nine .stat-icon i {
  font-size: 22px; }

.stat-widget-nine .stat-text {
  font-size: 14px; }

.stat-widget-nine .stat-digit {
  font-size: 14px; }

.stat-widget-nine .like-count {
  font-size: 30px; }

.horizontal {
  position: relative; }

.horizontal:before {
  background: #ffffff;
  bottom: 0;
  content: "";
  height: 38px;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  width: 1px; }

.widget-ten span i {
  color: #ffffff;
  opacity: 0.5; }

.widget-ten h5 {
  color: #ffffff; }

.widget-ten p {
  color: #ffffff !important;
  opacity: 0.75; }

/* Mixed Styles */
.badges h1, .badges h2, .badges h3, .badges h4, .badges h5, .badges h6 {
  margin: 5px 0; }

.vue-lists ul, .vue-lists ol {
  padding-left: 30px; }

.card .dropdown.float-right .dropdown-menu {
  left: inherit !important;
  right: 0 !important;
  top: 93% !important; }

.dataTables_paginate .pagination {
  border-radius: 0; }
  .dataTables_paginate .pagination li {
    border-radius: 0 !important; }
    .dataTables_paginate .pagination li a {
      border-radius: 0 !important;
      color: #272c33; }
    .dataTables_paginate .pagination li.active a {
      background: #272c33;
      border-color: #272c33;
      color: #fff; }
    .dataTables_paginate .pagination li:hover a {
      background: #272c33;
      border-color: #272c33;
      color: #fff; }

@media (max-width: 1368px) {
  .content {
    padding: 0 15px; }

  .twt-category {
    margin-bottom: 0; }

  .twt-feed {
    max-height: 155px; }
    .twt-feed img {
      height: 75px;
      width: 75px; }

  .stat-widget-one .stat-content {
    margin-left: 15px; }

  .card-body {
    padding: 15px; }

  .badges button {
    margin: 2px 0; } }
@media (max-width: 1024px) {
  aside.left-panel {
    padding: 0 20px;
    width: 200px; }

  .navbar .navbar-nav li > a .menu-icon {
    width: 30px; }

  .navbar .navbar-nav li.menu-item-has-children .sub-menu {
    padding: 0 0 0 30px; }

  .navbar .navbar-nav li.menu-item-has-children .sub-menu a {
    padding: 2px 0 2px 25px; }

  .card .card-header {
    position: relative; }
    .card .card-header strong {
      display: block; }
    .card .card-header small {
      float: left; }
    .card .card-header .card-actions {
      right: 0;
      top: .75rem;
      position: absolute; } }
@media (max-width: 992px) {
  [class*="col"].no-padding {
    flex: none; } }
@media (max-width: 575.99px) {
  body {
    display: block; }

  aside.left-panel {
    display: block;
    height: auto;
    min-height: inherit;
    padding: 0 15px;
    width: 100%; }
    aside.left-panel .navbar {
      margin-bottom: 0; }
      aside.left-panel .navbar .navbar-header {
        height: 50px; }
      aside.left-panel .navbar .navbar-brand {
        border-bottom: none;
        display: inline-block;
        float: left;
        line-height: 1;
        margin-top: 11px;
        min-height: inherit; }
        aside.left-panel .navbar .navbar-brand.hidden {
          display: none; }
      aside.left-panel .navbar .navbar-toggler {
        float: right;
        margin-top: 8px; }
      aside.left-panel .navbar .navbar-nav li > a {
        padding: 5px 0; }
      aside.left-panel .navbar .navbar-nav li.menu-item-has-children a:before {
        top: 5px; }
      aside.left-panel .navbar .menu-title {
        line-height: 30px;
        padding: 0; }

  .menutoggle {
    display: none; }

  .right-panel {
    display: block; }
    .right-panel header.header {
      padding: 5px 10px 1px 5px; }
      .right-panel header.header div[class*="col"] {
        padding: 0;
        width: initial; }
      .right-panel header.header .col-sm-7 {
        float: left; }
      .right-panel header.header .col-sm-5 {
        float: right; }
    .right-panel .breadcrumbs {
      padding: 10px 15px; }
      .right-panel .breadcrumbs div[class*="col"] {
        padding: 0; }
      .right-panel .breadcrumbs .page-header {
        min-height: inherit;
        padding: 0; }
        .right-panel .breadcrumbs .page-header h1 {
          padding: 5px 0; }
        .right-panel .breadcrumbs .page-header.float-right {
          float: left;
          text-align: left;
          width: 100%; }
          .right-panel .breadcrumbs .page-header.float-right .breadcrumb {
            padding: 0; }
    .right-panel .content {
      padding: 0 10px; }
      .right-panel .content .card .card-title {
        margin-bottom: 0; }
      .right-panel .content .card .card-footer {
        padding: 15px 5px; }
        .right-panel .content .card .card-footer ul {
          margin: 0; }
          .right-panel .content .card .card-footer ul li {
            float: left;
            margin: 5px 0;
            padding: 0 10px;
            width: 33.33%; }
      .right-panel .content div[class*="col"] {
        padding: 0; }
      .right-panel .content .row div[class*="col"] {
        padding: 0 10px; }
      .right-panel .content .nav-tabs .nav-link,
      .right-panel .content .nav-pills .nav-link {
        padding: .5rem; }
      .right-panel .content .tab-content.pl-3 {
        padding-left: 0 !important; }
      .right-panel .content #bootstrap-data-table_wrapper {
        font-size: 14px;
        padding: 0; }
        .right-panel .content #bootstrap-data-table_wrapper div[class*="col"] {
          padding: 0; }
        .right-panel .content #bootstrap-data-table_wrapper .table td, .right-panel .content #bootstrap-data-table_wrapper .table th {
          padding: 5px; } }

/*# sourceMappingURL=style.css.map */
