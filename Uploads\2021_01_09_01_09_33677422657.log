2018-10-31 15:21:05,981 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:21:05,981 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:21:08,226 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:21:08,227 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:21:08,227 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:21:08,228 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:21:08,228 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:21:18,223 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:21:18,223 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:21:18,223 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:21:18,224 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:21:18,224 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:21:28,224 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:21:28,225 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:21:28,225 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:21:28,225 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:21:28,225 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:21:38,227 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:21:38,227 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:21:38,227 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:21:38,228 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:21:38,228 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:21:48,228 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:21:48,229 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:21:48,229 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:21:48,229 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:21:48,229 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:21:58,225 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:21:58,226 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:21:58,226 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:21:58,226 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:21:58,226 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:22:05,981 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: Successfully recreated DataStore.
2018-10-31 15:22:05,981 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_DATASTORE:0:IN_PROGRESS:7:70%:0:0
2018-10-31 15:22:05,982 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:22:05,982 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:22:06,011 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:22:06,011 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:22:06,011 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:22:06,040 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:07,040 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:08,040 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:08,229 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:22:08,229 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:22:08,230 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:22:08,230 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:22:08,230 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:22:09,040 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:10,041 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:11,041 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:12,041 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:13,041 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:14,042 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:15,042 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:16,042 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:17,042 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:22:17,043 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:22:17,043 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh datastore start]	STATUS   : [0]
2018-10-31 15:22:17,043 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    Start command for the datastore service completed successfully
		    
		    Command completed successfully.
		    
		    Completed in : 10.1secs
]
2018-10-31 15:22:17,043 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:22:17,043 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:22:17,043 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: Successfully executed command : /opt/emc/dpa/services/bin/dpa.sh datastore start
2018-10-31 15:22:17,043 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_DATASTORE:0:IN_PROGRESS:8:76%:0:0
2018-10-31 15:22:17,043 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:22:17,043 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:22:17,074 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:22:17,074 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:22:17,074 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:22:17,080 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:18,080 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:22:18,080 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:22:18,080 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh datastore createts]	STATUS   : [0]
2018-10-31 15:22:18,081 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    [INFO] Tablespace config created
		    [INFO] Tablespace dpa_datamine created
		    [INFO] Tablespace dpa_datamine_large created
		    [INFO] Tablespace dpa_datamine_indices created
		    [INFO] Tablespace dpa_ra created
		    
		    Command completed successfully.
		    
		    Completed in : 312ms
]
2018-10-31 15:22:18,081 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:22:18,081 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:22:18,081 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: Successfully created DataStore Tables.
2018-10-31 15:22:18,081 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_DATASTORE:0:IN_PROGRESS:9:80%:0:0
2018-10-31 15:22:18,081 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: setDatastoreDatabasePassword Setting the Datastore Database Password in Datastore Server
2018-10-31 15:22:18,081 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: getDsPasswordInput Setting the Interactive input responses for Datastore Server.
2018-10-31 15:22:18,081 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:22:18,141 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:22:18,141 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: getDsPasswordInput Successfully set the Interactive input responses for Datastore Server.
2018-10-31 15:22:18,141 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:22:18,141 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:22:18,170 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:22:18,170 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:22:18,171 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:22:18,178 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: Enter new password for the datastore connection from the application node
2018-10-31 15:22:18,228 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:22:18,228 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:22:18,229 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:22:18,229 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:22:18,229 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:22:18,905 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: Retype new password for the datastore connection from the application node
2018-10-31 15:22:18,987 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:19,987 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:22:19,988 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:22:19,989 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh datastore dspassword]	STATUS   : [0]
2018-10-31 15:22:19,990 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    Enter new password for the datastore connection from the application node.
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    .
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    
		    Retype new password for the datastore connection from the application node : 
		    [INFO] New password has been applied to the datastore connection from the application node.
		    
		    Command completed successfully.
		    
		    Completed in : 173ms
]
2018-10-31 15:22:19,990 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:22:19,990 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:22:19,990 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: validateSetDSPassword Validated that Datastore Database Password has been set in Datastore Server.
2018-10-31 15:22:19,990 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: Successfully set Datastore Database Password on Datastore Server.
2018-10-31 15:22:19,990 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: setDatastoreDatabasePassword Successfully set the Datastore Database Password in Datastore Server
2018-10-31 15:22:19,990 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_DATASTORE:0:IN_PROGRESS:10:84%:0:0
2018-10-31 15:22:19,990 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: setDatastoreSuperuserPassword Setting superuser owning the database in Datastore Server.
2018-10-31 15:22:19,991 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:22:20,037 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:22:20,037 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:22:20,037 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:22:20,066 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:22:20,066 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:22:20,066 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:22:20,072 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: new password for the superuser owning the database
2018-10-31 15:22:20,759 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: new password for the superuser owning the database
2018-10-31 15:22:20,792 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:21,792 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:22:21,792 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:22:21,793 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh datastore superpassword]	STATUS   : [0]
2018-10-31 15:22:21,793 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    Enter new password for the superuser owning the database.
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    .
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    
		    Retype new password for the superuser owning the database : 
		    [INFO] New password has been applied to the superuser owning the database.
		    
		    Command completed successfully.
		    
		    Completed in : 92ms
]
2018-10-31 15:22:21,793 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:22:21,793 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:22:21,793 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: setDatastoreSuperuserPasswordSuccessfully set superuser password.
2018-10-31 15:22:21,793 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: setDatastoreSuperuserPassword Successfully set superuser password owning the Datastore Server database.
2018-10-31 15:22:21,793 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_DATASTORE:0:IN_PROGRESS:11:88%:0:0
2018-10-31 15:22:21,793 INFO  [Thread-971]-dpaadapter.DPAUtil: setInternalAgentPassword Setting Agent Password on *************
2018-10-31 15:22:21,793 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:22:21,838 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:22:21,838 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:22:21,838 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:22:21,867 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:22:21,867 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:22:21,867 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:22:21,874 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: assword:
2018-10-31 15:22:21,958 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: assword:
2018-10-31 15:22:21,959 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:22,959 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:22:22,959 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:22:22,960 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/agent/bin/dpaagent --set-credentials]	STATUS   : [0]
2018-10-31 15:22:22,960 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [EMC Data Protection Advisor
		    Enter registration password for DPA Agent.
		    The password must have:
		     - at least 9 characters
		     - at least 1 uppercase letter
		     - at least 1 lowercase letter
		     - at least 1 special character
		     - at least 1 digit
		    
		    Password:  **********
		    Retype password: **********
		    
		    New password has been set.
		    You must restart DPA Agent for this new password to be used.
		    Command completed successfully.]
2018-10-31 15:22:22,960 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:22:22,960 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:22:22,960 INFO  [Thread-971]-dpaadapter.DPAUtil: setInternalAgentPassword Successfully set Internal Agent password on *************
2018-10-31 15:22:22,960 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_DATASTORE:0:IN_PROGRESS:12:92%:0:0
2018-10-31 15:22:22,960 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:22:22,960 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:22:22,989 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:22:22,989 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:22:22,989 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:22:22,995 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:23,995 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:24,995 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:25,996 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:26,996 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:27,996 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:28,230 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:22:28,230 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:22:28,230 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:22:28,231 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:22:28,231 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:22:28,997 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:29,997 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:30,997 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:31,997 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:32,998 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:33,998 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:34,998 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:35,999 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:36,999 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:37,999 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:38,231 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:22:38,231 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:22:38,231 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:22:38,231 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:22:38,232 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:22:38,999 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:40,000 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:41,000 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:42,000 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:43,000 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:44,001 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:45,001 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:46,001 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:47,001 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:48,002 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:48,230 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:22:48,231 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:22:48,231 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:22:48,231 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:22:48,231 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:22:49,002 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:50,002 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:51,002 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:52,003 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:53,003 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:54,003 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:55,003 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:56,004 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:57,004 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:22:57,004 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:22:57,004 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service restart]	STATUS   : [0]
2018-10-31 15:22:57,004 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    [ERROR] Cannot execute command while service is stopped. Service must be started.
		    Stopped the datastore service successfully
		    Start command for the datastore service completed successfully
		    Start command for the agent service completed successfully
		    
		    Command completed successfully.
		    
		    Completed in : 32.6secs
]
2018-10-31 15:22:57,004 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:22:57,004 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:22:57,004 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: Successfully restarted DataStore Services.
2018-10-31 15:22:57,004 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting common configuration.
2018-10-31 15:22:57,004 INFO  [Thread-971]-dao.CommonConfigDAOImpl: Retrieving common configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/commonconfig.xml
2018-10-31 15:22:57,024 INFO  [Thread-971]-dao.CommonConfigDAOImpl: Successfully retrieved common configuration
2018-10-31 15:22:57,024 ERROR [Thread-971]-dpaadapter.DPAUtil: updateNTPSettingsOnDatastoreServer->Start
2018-10-31 15:22:57,024 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting Data Protection Advisor configuration.
2018-10-31 15:22:57,024 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Retrieving DPA configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/dpaconfig.xml
2018-10-31 15:22:57,025 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Successfully retrieved DPA configuration
2018-10-31 15:22:57,025 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:22:57,069 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:22:57,069 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:22:57,069 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:22:57,099 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:22:57,099 INFO  [Thread-971]-util.SSHUtil: Successfully tested connection
2018-10-31 15:22:57,099 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:22:57,099 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:22:57,128 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:22:57,128 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:22:57,128 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:22:57,134 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:58,134 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:22:58,240 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:22:58,240 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:22:58,241 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:22:58,241 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:22:58,241 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:22:59,134 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:00,135 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:01,135 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:02,135 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:03,135 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:04,136 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:04,136 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:04,136 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [echo "server *********** iburst" > /etc/ntp.conf ; ntpdate -u ***********]	STATUS   : [0]
2018-10-31 15:23:04,136 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [31 Oct 15:23:03 ntpdate[7060]: adjust time server *********** offset -0.341944 sec
]
2018-10-31 15:23:04,136 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:23:04,136 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:04,136 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully executed command - echo "server *********** iburst" > /etc/ntp.conf ; ntpdate -u ***********
2018-10-31 15:23:04,136 ERROR [Thread-971]-dpaadapter.DPAUtil: updateNTPSettingsOnDatastoreServer->End
2018-10-31 15:23:04,136 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_DATASTORE:0:COMPLETED:13:100%:0:0
2018-10-31 15:23:04,136 INFO  [Thread-971]-dpaadapter.ConfigDPADataStoreTask: Execution of DPA DataStore config task completed.
2018-10-31 15:23:04,136 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting Data Protection Advisor configuration.
2018-10-31 15:23:04,136 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Retrieving DPA configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/dpaconfig.xml
2018-10-31 15:23:04,150 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Successfully retrieved DPA configuration
2018-10-31 15:23:04,150 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:1:5%:0:0
2018-10-31 15:23:04,150 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting common configuration.
2018-10-31 15:23:04,150 INFO  [Thread-971]-dao.CommonConfigDAOImpl: Retrieving common configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/commonconfig.xml
2018-10-31 15:23:04,156 INFO  [Thread-971]-dao.CommonConfigDAOImpl: Successfully retrieved common configuration
2018-10-31 15:23:04,156 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:2:10%:0:0
2018-10-31 15:23:04,156 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:04,200 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:04,200 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:04,200 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:04,231 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:04,231 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:04,232 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:04,258 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:05,258 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:05,259 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:05,259 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [rm /etc/localtime; ln -s /usr/share/zoneinfo/Asia/Singapore /etc/localtime]	STATUS   : [0]
2018-10-31 15:23:05,259 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:23:05,259 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:23:05,259 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:05,259 INFO  [Thread-971]-dpaadapter.DpaPlugin: Successfully changed timezone on DPA Node.
2018-10-31 15:23:05,259 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: Successfully set timezone on DPA Application Server :*************
2018-10-31 15:23:05,259 INFO  [Thread-971]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:23:05,291 INFO  [Thread-971]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:23:05,292 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:3:20%:0:0
2018-10-31 15:23:05,292 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:05,336 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:05,336 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:05,336 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:05,364 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:05,364 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:05,364 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:05,365 INFO  [Thread-971]-util.SSHUtil: Connecting to the communication channel.
2018-10-31 15:23:05,392 INFO  [Thread-971]-util.SSHUtil: Connected to the communication channel.
2018-10-31 15:23:08,231 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:23:08,231 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:23:08,232 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:23:08,232 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:23:08,232 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:23:17,256 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:18,233 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:23:18,233 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:23:18,233 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:23:18,234 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:23:18,234 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:23:18,256 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:19,257 INFO  [Thread-971]-util.SSHUtil: Closing the channel for communication.
2018-10-31 15:23:19,257 INFO  [Thread-971]-util.SSHUtil: Disconnecting session for the host [*************] using provided credentials.
2018-10-31 15:23:19,257 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service stop]	STATUS   : [0]
2018-10-31 15:23:19,257 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    Stopped the agent service successfully
		    Command completed successfully.
		    Completed in : 11.3secs]
2018-10-31 15:23:19,257 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : [[ERROR] Cannot execute command while service is stopped. Service must be started.]
2018-10-31 15:23:19,257 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:19,257 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: Successfully executed command : /opt/emc/dpa/services/bin/dpa.sh service stop
2018-10-31 15:23:19,257 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:19,314 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:19,314 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:19,314 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:19,344 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:19,344 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:19,344 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:19,344 INFO  [Thread-971]-util.SSHUtil: Connecting to the communication channel.
2018-10-31 15:23:19,370 INFO  [Thread-971]-util.SSHUtil: Connected to the communication channel.
2018-10-31 15:23:20,139 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:21,139 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:22,139 INFO  [Thread-971]-util.SSHUtil: Closing the channel for communication.
2018-10-31 15:23:22,139 INFO  [Thread-971]-util.SSHUtil: Disconnecting session for the host [*************] using provided credentials.
2018-10-31 15:23:22,139 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh application configure --master *************]	STATUS   : [0]
2018-10-31 15:23:22,139 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    [INFO] Bind Address      : 0.0.0.0
		    [INFO] Datastore Service : *************
		    [INFO] Operation Mode    : STANDALONE
		    Command completed successfully.
		    Completed in : 224ms]
2018-10-31 15:23:22,139 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:23:22,139 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:22,140 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: Successfully verified as output of DPA application configure master command from line:[INFO] Datastore Service : *************
2018-10-31 15:23:22,140 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:22,184 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:22,184 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:22,184 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:22,213 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:22,213 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:22,213 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:22,213 INFO  [Thread-971]-util.SSHUtil: Connecting to the communication channel.
2018-10-31 15:23:22,239 INFO  [Thread-971]-util.SSHUtil: Connected to the communication channel.
2018-10-31 15:23:22,287 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:23,287 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:24,287 INFO  [Thread-971]-util.SSHUtil: Closing the channel for communication.
2018-10-31 15:23:24,287 INFO  [Thread-971]-util.SSHUtil: Disconnecting session for the host [*************] using provided credentials.
2018-10-31 15:23:24,287 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cat /opt/emc/dpa/agent/etc/dpaagent_config.xml]	STATUS   : [0]
2018-10-31 15:23:24,287 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [<AGENTCONFIG>
		      <VERSION>600</VERSION>
		      <SERVERNAME>************</SERVERNAME>
		      <SERVERPORT>9002</SERVERPORT>
		      <SERVERSSL>true</SERVERSSL>
		      <LOGLEVEL>Info</LOGLEVEL>
		      <LOGFILE>/opt/emc/dpa/agent/logs/dpaagent.log</LOGFILE>
		      <JREDIR>/opt/emc/dpa/services/_jre</JREDIR>
		    </AGENTCONFIG>]
2018-10-31 15:23:24,287 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:23:24,287 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:24,289 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: The output to be parsed : <AGENTCONFIG>   <VERSION>600</VERSION>   <SERVERNAME>************</SERVERNAME>   <SERVERPORT>9002</SERVERPORT>   <SERVERSSL>true</SERVERSSL>   <LOGLEVEL>Info</LOGLEVEL>   <LOGFILE>/opt/emc/dpa/agent/logs/dpaagent.log</LOGFILE>   <JREDIR>/opt/emc/dpa/services/_jre</JREDIR> </AGENTCONFIG>
2018-10-31 15:23:24,291 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:24,337 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:24,337 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:24,337 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:24,366 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:24,366 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:24,367 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:24,367 INFO  [Thread-971]-util.SSHUtil: Connecting to the communication channel.
2018-10-31 15:23:24,393 INFO  [Thread-971]-util.SSHUtil: Connected to the communication channel.
2018-10-31 15:23:24,440 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:25,440 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:26,440 INFO  [Thread-971]-util.SSHUtil: Closing the channel for communication.
2018-10-31 15:23:26,440 INFO  [Thread-971]-util.SSHUtil: Disconnecting session for the host [*************] using provided credentials.
2018-10-31 15:23:26,440 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [mv /opt/emc/dpa/agent/etc/dpaagent_config.xml /opt/emc/dpa/agent/etc/dpaagent_config.xml.old]	STATUS   : [0]
2018-10-31 15:23:26,440 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:23:26,440 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:23:26,441 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:26,441 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: Successfully executed command : mv /opt/emc/dpa/agent/etc/dpaagent_config.xml /opt/emc/dpa/agent/etc/dpaagent_config.xml.old
2018-10-31 15:23:26,441 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:26,486 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:26,486 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:26,486 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:26,515 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:26,515 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:26,515 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:26,515 INFO  [Thread-971]-util.SSHUtil: Connecting to the communication channel.
2018-10-31 15:23:26,540 INFO  [Thread-971]-util.SSHUtil: Connected to the communication channel.
2018-10-31 15:23:26,587 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:27,587 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:28,235 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:23:28,235 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:23:28,236 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:23:28,236 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:23:28,236 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:23:28,587 INFO  [Thread-971]-util.SSHUtil: Closing the channel for communication.
2018-10-31 15:23:28,588 INFO  [Thread-971]-util.SSHUtil: Disconnecting session for the host [*************] using provided credentials.
2018-10-31 15:23:28,588 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [sed -e 's/************/*************/g'  <  /opt/emc/dpa/agent/etc/dpaagent_config.xml.old  >  /opt/emc/dpa/agent/etc/dpaagent_config.xml]	STATUS   : [0]
2018-10-31 15:23:28,588 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:23:28,588 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:23:28,588 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:28,588 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: Successfully executed command : sed -e 's/************/*************/g'  <  /opt/emc/dpa/agent/etc/dpaagent_config.xml.old  >  /opt/emc/dpa/agent/etc/dpaagent_config.xml
2018-10-31 15:23:28,588 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:4:50%:0:0
2018-10-31 15:23:28,588 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: setDsPassword Setting the Datastore Database Password in Application Server
2018-10-31 15:23:28,589 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:28,633 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:28,634 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: getDsPasswordInputs Setting the Interactive input responses for Application Server.
2018-10-31 15:23:28,634 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:28,678 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:28,678 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: getDsPasswordInputs Successfully set the Interactive input responses for Application Server.
2018-10-31 15:23:28,678 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:28,678 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:28,708 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:28,708 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:28,708 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:28,739 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: Enter new password for the datastore connection
2018-10-31 15:23:29,395 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: Retype new password for the datastore connection
2018-10-31 15:23:29,462 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:30,463 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:30,463 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:30,463 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh application dspassword]	STATUS   : [0]
2018-10-31 15:23:30,464 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    Enter new password for the datastore connection.
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    .
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    
		    Retype new password for the datastore connection : 
		    [INFO] New password has been applied to the configuration.
		    [INFO] For this new password to be used you must ensure that all datastore nodes use the same new password value.
		    
		    Command completed successfully.
		    
		    Completed in : 798ms
]
2018-10-31 15:23:30,464 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:23:30,464 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:30,464 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: validateSetDsPassword Validated that Datastore Database Password has been set in Application Server.
2018-10-31 15:23:30,464 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: Successfully executed command : /opt/emc/dpa/services/bin/dpa.sh application dspassword
2018-10-31 15:23:30,464 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: setDsPassword Successfully set the Datastore Database Password in Application Server
2018-10-31 15:23:30,464 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:5:60%:0:0
2018-10-31 15:23:30,464 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: tuneRamApplication Setting the Application Service RAM in Application Server
2018-10-31 15:23:30,465 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:30,523 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:30,523 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:30,523 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:30,552 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:30,553 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:30,553 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:30,582 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: [Y|N]
2018-10-31 15:23:31,249 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:32,250 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:23:32,250 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:23:32,250 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh application tune 16GB]	STATUS   : [0]
2018-10-31 15:23:32,251 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    Set Application service memory usage to 11468MB : Confirm [Y|N]
		    Y
		    Application service successfully tuned.
		    
		    Command completed successfully.
		    
		    Completed in : 162ms
]
2018-10-31 15:23:32,251 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:23:32,251 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:23:32,251 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: tuneRamApplication Successfully tuned Application Service RAM.
2018-10-31 15:23:32,251 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: tuneRamApplication Successfully tuned RAM for Application Service in Application Server
2018-10-31 15:23:32,251 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:6:65%:0:0
2018-10-31 15:23:32,251 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting Data Protection Advisor configuration.
2018-10-31 15:23:32,252 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Retrieving DPA configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/dpaconfig.xml
2018-10-31 15:23:32,253 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Successfully retrieved DPA configuration
2018-10-31 15:23:32,253 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:23:32,298 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:23:32,298 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:23:32,298 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:23:32,327 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:23:32,327 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:23:32,327 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:23:32,355 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:33,356 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:34,356 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:35,356 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:36,357 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:37,357 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:38,234 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:23:38,234 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:23:38,235 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:23:38,235 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:23:38,235 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:23:38,357 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:39,358 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:40,358 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:41,358 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:42,359 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:43,359 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:44,360 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:45,360 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:46,360 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:47,361 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:48,257 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:23:48,258 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:23:48,258 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:23:48,258 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:23:48,258 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:23:48,361 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:49,361 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:50,362 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:51,362 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:52,362 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:53,363 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:54,363 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:55,364 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:56,364 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:57,364 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:58,240 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:23:58,240 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:23:58,240 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:23:58,241 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:23:58,241 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:23:58,365 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:23:59,365 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:00,365 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:01,366 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:02,366 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:03,367 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:04,367 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:05,367 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:06,368 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:07,368 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:08,245 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:24:08,245 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:24:08,245 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:24:08,245 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:24:08,246 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:24:08,368 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:24:08,369 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:24:08,369 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service restart]	STATUS   : [0]
2018-10-31 15:24:08,369 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    [ERROR] Cannot execute command while service is stopped. Service must be started.
		    [ERROR] Cannot execute command while service is stopped. Service must be started.
		    Start command for the application service completed successfully
		    Start command for the agent service completed successfully
		    
		    Command completed successfully.
		    
		    Completed in : 35.2secs
]
2018-10-31 15:24:08,369 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:24:08,369 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:24:08,369 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully executed command : /opt/emc/dpa/services/bin/dpa.sh service restart
2018-10-31 15:24:08,370 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:24:08,428 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:24:08,428 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:24:08,428 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:24:08,458 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:24:08,458 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:24:08,458 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:24:08,464 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:09,464 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:10,464 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:24:10,464 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:24:10,465 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:24:10,465 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is STARTING...
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 299ms
]
2018-10-31 15:24:10,465 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:24:10,465 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:24:10,465 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application or Agent service is starting
2018-10-31 15:24:18,236 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:24:18,237 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:24:18,237 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:24:18,238 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:24:18,238 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:24:28,237 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:24:28,237 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:24:28,237 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:24:28,238 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:24:28,238 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:24:38,240 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:24:38,240 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:24:38,240 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:24:38,240 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:24:38,241 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:24:40,465 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:24:40,466 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:24:40,496 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:24:40,497 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:24:40,497 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:24:40,503 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:24:41,504 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:24:41,504 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:24:41,504 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:24:41,504 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is STARTING...
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 209ms
]
2018-10-31 15:24:41,505 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:24:41,505 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:24:41,505 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application or Agent service is starting
2018-10-31 15:24:48,234 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:24:48,234 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:24:48,235 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:24:48,235 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:24:48,235 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:24:58,238 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:24:58,238 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:24:58,238 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:24:58,239 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:24:58,239 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:25:08,271 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:25:08,271 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:25:08,271 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:25:08,272 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:25:08,272 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:25:11,505 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:25:11,505 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:25:11,537 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:25:11,537 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:25:11,537 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:25:11,544 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:25:12,544 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:25:12,545 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:25:12,545 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:25:12,545 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is STARTING...
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 204ms
]
2018-10-31 15:25:12,545 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:25:12,545 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:25:12,545 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application or Agent service is starting
2018-10-31 15:25:18,244 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:25:18,244 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:25:18,245 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:25:18,245 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:25:18,245 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:25:28,243 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:25:28,243 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:25:28,243 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:25:28,243 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:25:28,244 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:25:38,258 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:25:38,258 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:25:38,258 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:25:38,258 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:25:38,259 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:25:42,546 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:25:42,546 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:25:42,577 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:25:42,577 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:25:42,577 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:25:42,584 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:25:43,585 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:25:43,585 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:25:43,585 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:25:43,585 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is STARTING...
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 227ms
]
2018-10-31 15:25:43,585 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:25:43,585 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:25:43,586 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application or Agent service is starting
2018-10-31 15:25:48,242 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:25:48,242 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:25:48,243 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:25:48,243 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:25:48,243 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:25:58,242 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:25:58,242 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:25:58,242 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:25:58,243 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:25:58,243 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:26:08,241 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:26:08,241 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:26:08,241 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:26:08,242 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:26:08,242 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:26:13,586 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:26:13,586 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:26:13,618 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:26:13,618 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:26:13,618 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:26:13,628 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:26:14,628 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:26:14,628 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:26:14,629 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:26:14,629 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is STARTING...
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 283ms
]
2018-10-31 15:26:14,629 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:26:14,629 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:26:14,629 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application or Agent service is starting
2018-10-31 15:26:18,249 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:26:18,250 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:26:18,250 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:26:18,250 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:26:18,250 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:26:28,248 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:26:28,249 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:26:28,249 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:26:28,249 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:26:28,249 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:26:38,246 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:26:38,247 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:26:38,247 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:26:38,247 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:26:38,247 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:26:44,629 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:26:44,630 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:26:44,674 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:26:44,674 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:26:44,674 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:26:44,682 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:26:45,682 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:26:45,682 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:26:45,683 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:26:45,683 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is STARTING...
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 212ms
]
2018-10-31 15:26:45,683 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:26:45,683 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:26:45,683 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application or Agent service is starting
2018-10-31 15:26:48,249 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:26:48,250 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:26:48,250 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:26:48,250 INFO  [http-nio-8543-exec-14]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:26:48,250 INFO  [http-nio-8543-exec-14]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:26:58,251 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:26:58,251 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:26:58,252 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:26:58,252 INFO  [http-nio-8543-exec-16]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:26:58,252 INFO  [http-nio-8543-exec-16]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:27:03,882 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:03,882 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:03,883 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:03,883 INFO  [http-nio-8543-exec-14]-restadapter.DashboardInfoService: Received request for retrieving dashboard info for DataprotectionCentral.
2018-10-31 15:27:03,883 INFO  [http-nio-8543-exec-14]-dashboard.DashboardResponse: dataprotectionCentralDashboardResponse: com.emc.vcedpa.common.model.dashboard.DataprotectionCentralDashboardResponse@5d4edb7a
2018-10-31 15:27:04,242 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:04,243 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:04,243 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:04,243 INFO  [http-nio-8543-exec-16]-restadapter.DashboardInfoService: Received request for retrieving dashboard info for avamar.
2018-10-31 15:27:04,519 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:04,519 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:04,520 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:04,520 INFO  [http-nio-8543-exec-14]-restadapter.DashboardInfoService: Received request for retrieving dashboard info for data domain .
2018-10-31 15:27:04,850 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:04,850 INFO  [http-nio-8543-exec-16]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:04,850 INFO  [http-nio-8543-exec-16]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:04,851 INFO  [http-nio-8543-exec-16]-restadapter.SKUSelection: Received request for retrieving selected sku configuration.
2018-10-31 15:27:04,851 INFO  [http-nio-8543-exec-16]-skuadapter.SKUSelectionService: Getting selected sku configuration.
2018-10-31 15:27:04,851 INFO  [http-nio-8543-exec-16]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:27:04,867 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:04,867 INFO  [http-nio-8543-exec-14]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:04,868 INFO  [http-nio-8543-exec-14]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:04,868 INFO  [http-nio-8543-exec-14]-restadapter.SKUSelection: Received request for retrieving selected sku configuration.
2018-10-31 15:27:04,868 INFO  [http-nio-8543-exec-14]-skuadapter.SKUSelectionService: Getting selected sku configuration.
2018-10-31 15:27:04,898 INFO  [http-nio-8543-exec-16]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:27:04,898 INFO  [http-nio-8543-exec-14]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:27:04,930 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:04,930 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:04,930 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:04,930 INFO  [http-nio-8543-exec-15]-restadapter.ApplianceStatusService: Received request for retrieving appliance status.
2018-10-31 15:27:04,931 INFO  [http-nio-8543-exec-15]-dao.ApplianceStatusDAOImpl: Getting appliance Status from file at: /usr/local/dataprotection/var/configmgr/server_data/status/applianceStatus.xml
2018-10-31 15:27:04,935 INFO  [http-nio-8543-exec-14]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:27:04,937 INFO  [http-nio-8543-exec-15]-dao.ApplianceStatusDAOImpl: Successfully retrieved appliance status
2018-10-31 15:27:04,973 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:04,973 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:04,973 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:04,973 INFO  [http-nio-8543-exec-20]-restadapter.ApplianceStatusService: Received request for retrieving appliance status.
2018-10-31 15:27:04,973 INFO  [http-nio-8543-exec-20]-dao.ApplianceStatusDAOImpl: Getting appliance Status from file at: /usr/local/dataprotection/var/configmgr/server_data/status/applianceStatus.xml
2018-10-31 15:27:04,975 INFO  [http-nio-8543-exec-20]-dao.ApplianceStatusDAOImpl: Successfully retrieved appliance status
2018-10-31 15:27:04,994 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:04,994 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:04,995 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:04,995 INFO  [http-nio-8543-exec-15]-restadapter.DashboardInfoService: Received request for retrieving dashboard info for DPS.
2018-10-31 15:27:04,995 INFO  [http-nio-8543-exec-15]-dashboard.DashboardResponse: dpsDashboardResponseException com.emc.vcedpa.common.exception.ApplianceException: Data Protection Search is not configured. 
2018-10-31 15:27:05,082 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:05,082 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:05,082 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:05,083 INFO  [http-nio-8543-exec-20]-restadapter.DashboardInfoService: Received request for retrieving dashboard info for DPS.
2018-10-31 15:27:05,083 INFO  [http-nio-8543-exec-20]-dashboard.DashboardResponse: dpsDashboardResponseException com.emc.vcedpa.common.exception.ApplianceException: Data Protection Search is not configured. 
2018-10-31 15:27:05,368 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:05,368 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:05,368 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:05,369 INFO  [http-nio-8543-exec-15]-restadapter.SKUSelection: Received request for retrieving selected sku configuration.
2018-10-31 15:27:05,369 INFO  [http-nio-8543-exec-15]-skuadapter.SKUSelectionService: Getting selected sku configuration.
2018-10-31 15:27:05,369 INFO  [http-nio-8543-exec-15]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:27:05,403 INFO  [http-nio-8543-exec-15]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:27:05,443 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:05,443 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:05,444 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:05,444 INFO  [http-nio-8543-exec-20]-restadapter.ApplianceStatusService: Received request for retrieving appliance status.
2018-10-31 15:27:05,444 INFO  [http-nio-8543-exec-20]-dao.ApplianceStatusDAOImpl: Getting appliance Status from file at: /usr/local/dataprotection/var/configmgr/server_data/status/applianceStatus.xml
2018-10-31 15:27:05,445 INFO  [http-nio-8543-exec-20]-dao.ApplianceStatusDAOImpl: Successfully retrieved appliance status
2018-10-31 15:27:05,530 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:05,530 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:05,530 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:05,530 INFO  [http-nio-8543-exec-15]-restadapter.DashboardInfoService: getVmwareComponentDashboardInfo--> Received request for retrieving dashboard info for VMWare components.
2018-10-31 15:27:05,668 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:05,668 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:05,669 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:05,669 INFO  [http-nio-8543-exec-20]-restadapter.DashboardInfoService: Received request for retrieving dashboard info for support config.
2018-10-31 15:27:05,978 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:05,978 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:05,979 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:05,980 INFO  [http-nio-8543-exec-15]-restadapter.DashboardInfoService: Received request for retrieving dashboard info for common settings.
2018-10-31 15:27:05,980 INFO  [http-nio-8543-exec-15]-util.AcmTimeUtil: JSON file is read and will be iterated to find time
2018-10-31 15:27:05,981 INFO  [http-nio-8543-exec-15]-restadapter.DashboardInfoService: time set on dashboard UTC_PLUS_0800_ASI_SIN
2018-10-31 15:27:08,249 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:08,249 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:08,249 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:08,250 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:27:08,250 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:27:15,686 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:27:15,686 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:27:15,739 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:27:15,739 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:27:15,739 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:27:15,746 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:27:16,747 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:27:17,747 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:27:17,747 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:27:17,747 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:27:17,747 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is STARTING...
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 696ms
]
2018-10-31 15:27:17,747 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:27:17,747 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:27:17,747 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application or Agent service is starting
2018-10-31 15:27:18,248 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:18,249 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:18,249 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:18,250 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:27:18,252 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:27:28,245 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:28,245 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:28,245 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:28,246 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:27:28,246 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:27:38,251 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:38,251 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:38,251 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:38,252 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:27:38,252 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:27:47,748 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:27:47,748 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:27:47,778 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:27:47,778 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:27:47,778 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:27:47,785 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:27:48,245 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:48,245 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:48,246 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:48,246 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:27:48,247 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:27:48,785 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:27:48,785 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:27:48,785 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:27:48,785 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is RUNNING
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 201ms
]
2018-10-31 15:27:48,786 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:27:48,786 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:27:48,786 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application and Agent services are running
2018-10-31 15:27:48,786 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: Successfully executed command : /opt/emc/dpa/services/bin/dpa.sh service status
2018-10-31 15:27:48,786 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:7:75%:0:0
2018-10-31 15:27:48,786 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:27:48,845 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:27:48,845 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting Data Protection Advisor configuration.
2018-10-31 15:27:48,845 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Retrieving DPA configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/dpaconfig.xml
2018-10-31 15:27:48,846 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Successfully retrieved DPA configuration
2018-10-31 15:27:48,846 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:27:48,890 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:27:48,890 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:27:48,890 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:27:48,920 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:27:48,920 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:27:48,920 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:27:48,926 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: Enter new administrator password
2018-10-31 15:27:54,793 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: Retype new administrator password
2018-10-31 15:27:54,827 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:27:55,827 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:27:56,828 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:27:57,828 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:27:58,246 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:27:58,246 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:27:58,246 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:27:58,247 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:27:58,247 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:27:58,828 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:27:59,828 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:00,828 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:01,829 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:02,829 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:03,829 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:04,829 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:05,830 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:06,830 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:07,830 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:08,248 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:28:08,248 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:28:08,249 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:28:08,249 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:28:08,249 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:28:08,830 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:09,830 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:10,831 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:11,831 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:28:11,831 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:28:11,832 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh application adminpassword]	STATUS   : [0]
2018-10-31 15:28:11,832 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    Enter new administrator password.
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    .
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    
		    Retype new administrator password : 
		    [INFO] Your new password has been set.
		    [INFO] You must restart all DPA application nodes for this new password to be used.
		    
		    Command completed successfully.
		    
		    Completed in : 22.3secs
]
2018-10-31 15:28:11,832 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:28:11,832 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:28:11,832 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully executed command : /opt/emc/dpa/services/bin/dpa.sh application adminpassword
2018-10-31 15:28:11,832 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:8:80%:0:0
2018-10-31 15:28:11,832 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: setAppServerAgentPassword Setting AgentPassword for Application Server service.
2018-10-31 15:28:11,832 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:28:11,890 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:28:11,890 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:28:11,935 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:28:11,935 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:28:11,935 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:28:11,965 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:28:11,965 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:28:11,965 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:28:11,973 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: new agent password
2018-10-31 15:28:17,843 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: new agent password
2018-10-31 15:28:17,879 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:18,146 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:28:18,146 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:28:18,147 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:28:18,147 INFO  [http-nio-8543-exec-15]-restadapter.SKUSelection: Received request for retrieving selected sku configuration.
2018-10-31 15:28:18,147 INFO  [http-nio-8543-exec-15]-skuadapter.SKUSelectionService: Getting selected sku configuration.
2018-10-31 15:28:18,147 INFO  [http-nio-8543-exec-15]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:28:18,182 INFO  [http-nio-8543-exec-15]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:28:18,209 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:28:18,209 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:28:18,209 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:28:18,210 INFO  [http-nio-8543-exec-20]-restadapter.ApplianceStatusService: Received request for retrieving appliance status.
2018-10-31 15:28:18,210 INFO  [http-nio-8543-exec-20]-dao.ApplianceStatusDAOImpl: Getting appliance Status from file at: /usr/local/dataprotection/var/configmgr/server_data/status/applianceStatus.xml
2018-10-31 15:28:18,223 INFO  [http-nio-8543-exec-20]-dao.ApplianceStatusDAOImpl: Successfully retrieved appliance status
2018-10-31 15:28:18,284 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:28:18,284 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:28:18,284 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:28:18,285 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:28:18,285 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:28:18,879 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:19,879 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:20,880 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:21,880 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:22,881 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:23,881 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:24,881 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:25,882 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:26,882 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:27,882 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:28,290 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:28:28,290 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:28:28,291 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:28:28,291 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:28:28,291 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:28:28,883 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:29,883 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:30,884 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:31,884 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:32,884 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:33,885 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:34,885 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:35,885 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:36,886 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:37,886 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:38,290 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:28:38,291 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:28:38,291 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:28:38,291 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:28:38,292 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:28:38,887 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:39,887 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:40,887 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:41,888 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:42,888 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:43,888 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:44,889 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:45,889 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:46,890 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:47,890 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:48,288 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:28:48,288 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:28:48,289 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:28:48,289 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:28:48,289 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:28:48,890 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:49,891 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:50,891 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:51,891 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:52,892 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:53,892 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:54,892 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:55,893 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:56,893 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:57,894 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:58,286 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:28:58,286 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:28:58,287 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:28:58,287 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:28:58,287 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:28:58,894 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:28:59,894 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:00,895 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:01,895 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:02,895 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:03,895 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:04,895 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:05,896 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:06,896 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:07,896 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:08,284 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:29:08,284 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:29:08,284 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:29:08,285 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:29:08,285 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:29:08,896 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:09,897 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:10,897 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:11,897 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:12,897 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:13,898 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:14,898 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:15,898 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:29:15,898 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:29:15,899 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh application agentpassword]	STATUS   : [0]
2018-10-31 15:29:15,899 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    Enter new agent password.
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    .
		    The password must have:
		        - at least 9 characters 
		        - at least 1 uppercase letter 
		        - at least 1 lowercase letter
		        - at least 1 special character
		        - at least 1 digit 
		    
		    Retype new agent password : 
		    [INFO] Your new password has been set.
		    [INFO] You must restart all DPA application nodes for this new password to be used.
		    
		    Command completed successfully.
		    
		    Completed in : 1min 2secs
]
2018-10-31 15:29:15,899 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:29:15,899 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:29:15,899 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:29:15,957 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:29:15,957 INFO  [Thread-971]-dpaadapter.DPAUtil: setInternalAgentPassword Setting Agent Password on *************
2018-10-31 15:29:15,957 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:29:16,002 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:29:16,002 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:29:16,002 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:29:16,032 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:29:16,032 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:29:16,032 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:29:16,039 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: assword:
2018-10-31 15:29:16,097 INFO  [Thread-971]-util.SSHUtil:  Expected Message:: assword:
2018-10-31 15:29:16,097 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:17,098 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:29:17,098 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:29:17,099 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/agent/bin/dpaagent --set-credentials]	STATUS   : [0]
2018-10-31 15:29:17,099 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [EMC Data Protection Advisor
		    Enter registration password for DPA Agent.
		    The password must have:
		     - at least 9 characters
		     - at least 1 uppercase letter
		     - at least 1 lowercase letter
		     - at least 1 special character
		     - at least 1 digit
		    
		    Password:  **********
		    Retype password: **********
		    
		    New password has been set.
		    You must restart DPA Agent for this new password to be used.
		    Command completed successfully.]
2018-10-31 15:29:17,099 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:29:17,099 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:29:17,099 INFO  [Thread-971]-dpaadapter.DPAUtil: setInternalAgentPassword Successfully set Internal Agent password on *************
2018-10-31 15:29:17,099 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting Data Protection Advisor configuration.
2018-10-31 15:29:17,099 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Retrieving DPA configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/dpaconfig.xml
2018-10-31 15:29:17,100 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Successfully retrieved DPA configuration
2018-10-31 15:29:17,100 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:29:17,145 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:29:17,145 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:29:17,145 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:29:17,174 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:29:17,174 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:29:17,174 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:29:17,181 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:18,181 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:18,292 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:29:18,293 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:29:18,293 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:29:18,294 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:29:18,294 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:29:19,181 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:20,181 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:21,182 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:22,182 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:23,182 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:24,182 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:25,183 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:26,183 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:27,183 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:28,183 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:28,288 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:29:28,288 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:29:28,289 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:29:28,289 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:29:28,289 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:29:29,184 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:30,184 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:31,184 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:32,184 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:33,185 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:34,185 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:35,185 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:36,185 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:37,186 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:38,186 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:38,295 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:29:38,295 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:29:38,295 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:29:38,296 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:29:38,296 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:29:39,186 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:40,186 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:41,187 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:42,187 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:43,187 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:44,187 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:45,188 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:46,188 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:47,188 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:48,188 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:48,286 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:29:48,287 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:29:48,287 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:29:48,287 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:29:48,287 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:29:49,189 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:50,189 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:51,189 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:52,189 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:53,190 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:54,190 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:55,190 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:56,190 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:57,191 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:58,191 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:29:58,284 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:29:58,284 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:29:58,285 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:29:58,285 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:29:58,285 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:29:59,191 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:00,192 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:01,192 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:02,192 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:03,192 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:04,193 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:05,193 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:06,193 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:07,193 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:08,194 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:08,291 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:30:08,291 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:30:08,292 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:30:08,292 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:30:08,292 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:30:09,194 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:10,194 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:11,194 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:12,195 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:13,195 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:14,195 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:15,195 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:16,196 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:17,196 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:18,196 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:18,291 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:30:18,291 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:30:18,291 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:30:18,292 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:30:18,292 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:30:19,196 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:20,197 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:21,197 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:22,197 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:23,197 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:24,198 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:25,198 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:26,198 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:27,199 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:28,199 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:28,292 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:30:28,292 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:30:28,292 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:30:28,293 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:30:28,293 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:30:29,199 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:30,199 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:31,200 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:32,200 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:33,200 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:30:33,200 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:30:33,200 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service restart]	STATUS   : [0]
2018-10-31 15:30:33,200 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    Stopped the agent service successfully
		    Stopped the application service successfully
		    Start command for the application service completed successfully
		    Start command for the agent service completed successfully
		    
		    Command completed successfully.
		    
		    Completed in : 1min 15secs
]
2018-10-31 15:30:33,201 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:30:33,201 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:30:33,201 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully executed command : /opt/emc/dpa/services/bin/dpa.sh service restart
2018-10-31 15:30:33,201 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:30:33,259 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:30:33,259 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:30:33,259 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:30:33,291 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:30:33,291 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:30:33,292 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:30:33,298 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:34,298 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:30:35,299 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:30:35,299 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:30:35,299 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:30:35,299 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is STARTING...
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 284ms
]
2018-10-31 15:30:35,299 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:30:35,299 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:30:35,300 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application or Agent service is starting
2018-10-31 15:30:38,286 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:30:38,286 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:30:38,287 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:30:38,287 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:30:38,287 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:30:48,289 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:30:48,289 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:30:48,289 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:30:48,289 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:30:48,290 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:30:58,290 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:30:58,290 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:30:58,290 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:30:58,290 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:30:58,291 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:31:05,300 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:31:05,300 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:31:05,331 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:31:05,331 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:31:05,331 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:31:05,337 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:31:06,338 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:31:06,338 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:31:06,338 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [/opt/emc/dpa/services/bin/dpa.sh service status]	STATUS   : [0]
2018-10-31 15:31:06,338 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [
		    EMC Data Protection Advisor
		    
		    The status of the Application Service is RUNNING
		    The status of the Agent Service is RUNNING
		    
		    Command completed successfully.
		    
		    Completed in : 203ms
]
2018-10-31 15:31:06,338 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:31:06,339 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:31:06,339 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: DPA Application Config : Application and Agent services are running
2018-10-31 15:31:06,339 INFO  [Thread-971]-dpaadapter.ConfigDPAApplicationTask: Successfully executed command : /opt/emc/dpa/services/bin/dpa.sh service status
2018-10-31 15:31:06,339 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:IN_PROGRESS:9:90%:0:0
2018-10-31 15:31:06,339 INFO  [Thread-971]-licenseadapter.LicenseManager: Retrieving license configuration from file /usr/local/dataprotection/var/configmgr/server_data/license/licenseconfig.xml
2018-10-31 15:31:06,361 INFO  [Thread-971]-licenseadapter.LicenseManager: Successfully retrieved license configuration
2018-10-31 15:31:06,361 INFO  [Thread-971]-licenseadapter.LicenseManager: Retrieving license configuration from file /usr/local/dataprotection/var/configmgr/server_data/license/licenseconfig.xml
2018-10-31 15:31:06,369 INFO  [Thread-971]-licenseadapter.LicenseManager: Successfully retrieved license configuration
2018-10-31 15:31:06,369 INFO  [Thread-971]-licenseadapter.LicenseManager: Retrieving license configuration from file /usr/local/dataprotection/var/configmgr/server_data/license/licenseconfig.xml
2018-10-31 15:31:06,376 INFO  [Thread-971]-licenseadapter.LicenseManager: Successfully retrieved license configuration
2018-10-31 15:31:06,376 INFO  [Thread-971]-licenseadapter.LicenseManager: Retrieving license configuration from file /usr/local/dataprotection/var/configmgr/server_data/license/licenseconfig.xml
2018-10-31 15:31:06,384 INFO  [Thread-971]-licenseadapter.LicenseManager: Successfully retrieved license configuration
2018-10-31 15:31:06,385 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:31:06,429 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:31:06,431 INFO  [Thread-971]-util.RestUtil: Making REST call: https://*************:9002/dpa-api/license ,method: POST Meditype application/vnd.emc.dpa-v6.2.0+xml
2018-10-31 15:31:06,952 INFO  [Thread-971]-util.RestUtil:  REST request return code: 201, expected code: Created
2018-10-31 15:31:06,952 INFO  [Thread-971]-util.RestUtil:  REST request Successgully executed
2018-10-31 15:31:06,952 INFO  [Thread-971]-util.RestUtil:  REST request return code: 201, expected code: Created
2018-10-31 15:31:06,952 INFO  [Thread-971]-util.RestUtil:  REST request Successgully executed
2018-10-31 15:31:06,982 INFO  [Thread-971]-dpaadapter.DpaPlugin:  Rest response for apply license API 
<addLicensesResult>
   <license lastModified="2018-10-31T15:31:06.132+08:00">
      <id>264dbaab-2440-467c-91bc-292c3e43a195</id>
      <name>Enterprise Applications</name>
      <code>DPA_APPD_1TB</code>
      <instances>1</instances>
      <expiry>0</expiry>
      <featureSet>0</featureSet>
   </license>
   <license lastModified="2018-10-31T15:31:06.168+08:00">
      <id>a74240e8-2566-4a6c-95bc-fd4c4e75eaf5</id>
      <name>Avamar Backend Capacity</name>
      <code>DPA_BACKUP_AVAMAR</code>
      <instances>1</instances>
      <expiry>0</expiry>
      <featureSet>0</featureSet>
   </license>
   <license lastModified="2018-10-31T15:31:06.180+08:00">
      <id>dc7becc6-c419-49ff-a345-813b83a51149</id>
      <name>NetWorker Frontend Capacity</name>
      <code>DPA_BACKUP_NETWORKER</code>
      <instances>1</instances>
      <expiry>0</expiry>
      <featureSet>0</featureSet>
   </license>
   <license lastModified="2018-10-31T15:31:06.191+08:00">
      <id>604520e9-ac8f-444f-97a6-d0a39b71cc94</id>
      <name>Hypervisor Server Count</name>
      <code>DPA_HYPERVISOR</code>
      <instances>1</instances>
      <expiry>0</expiry>
      <featureSet>0</featureSet>
   </license>
   <license lastModified="2018-10-31T15:31:06.201+08:00">
      <id>be70ad9d-d44d-4d09-b98c-c38a8dff0561</id>
      <name>Federated Reporting</name>
      <code>FEDERATED_REPORTING</code>
      <instances>1</instances>
      <expiry>0</expiry>
      <featureSet>0</featureSet>
   </license>
</addLicensesResult>
2018-10-31 15:31:06,983 INFO  [Thread-971]-util.RestUtil: Making REST call: https://*************:9002/dpa-api/license ,method: GET Meditype application/vnd.emc.apollo-v1+xml
2018-10-31 15:31:07,130 INFO  [Thread-971]-util.RestUtil:  REST request return code: 200, expected code: OK
2018-10-31 15:31:07,130 INFO  [Thread-971]-util.RestUtil:  REST request Successgully executed
2018-10-31 15:31:07,130 INFO  [Thread-971]-util.RestUtil:  REST request return code: 200, expected code: OK
2018-10-31 15:31:07,130 INFO  [Thread-971]-util.RestUtil:  REST request Successgully executed
2018-10-31 15:31:07,139 INFO  [Thread-971]-dpaadapter.DpaPlugin:  Found code from validate license API response. value: DPA_APPD_1TB
2018-10-31 15:31:07,139 ERROR [Thread-971]-dpaadapter.DpaPlugin:  Lincese validation failed. License code mismatche. License Code Input: , Output: DPA_APPD_1TB
2018-10-31 15:31:07,139 WARN  [Thread-971]-dpaadapter.DpaPlugin:  License validated failed due to Failed to apply DPA license.
2018-10-31 15:31:07,139 ERROR [Thread-971]-dpaadapter.ConfigDPAApplicationTask: ApplianceException occured while configuring DPA Application server 
com.emc.vcedpa.common.exception.ApplianceException: Failed to apply DPA license.
	at com.emc.vcedpa.dpaadapter.DpaPlugin.applyLicenseAndValidate(DpaPlugin.java:280)
	at com.emc.vcedpa.dpaadapter.ConfigDPAApplicationTask.run(ConfigDPAApplicationTask.java:117)
	at com.emc.vcedpa.configure.DashboardWorkflowManager$1.run(DashboardWorkflowManager.java:93)
2018-10-31 15:31:07,139 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:FAILED:10:90%:1:0
2018-10-31 15:31:07,140 ERROR [Thread-971]-configure.DashboardWorkflowManager$1: Dashboard Workflow tasks failed for product DATA_PROTECTION_ADVISOR
2018-10-31 15:31:07,140 INFO  [Thread-971]-configure.DashboardWorkflowManager: Updating log bundle status: idpavapp.creating_log_bundle
2018-10-31 15:31:07,140 INFO  [Thread-971]-configure.DashboardWorkflowManager: Failed task parameters. productId: DATA_PROTECTION_ADVISOR actionId: CONFIG componentId: DPA_APPLICATION_SERVER componentInstanceId: 0
2018-10-31 15:31:07,140 INFO  [Thread-971]-configure.DashboardWorkflowManager: notifyStatus Received notifyStatus from Dashboard Workflow task : DATA_PROTECTION_ADVISOR:CONFIG:DPA_APPLICATION_SERVER:0:FAILED:11:90%:1:0
2018-10-31 15:31:07,140 INFO  [Thread-971]-configure.LogCollectorManager: Creating log bundle for: DATA_PROTECTION_ADVISOR
2018-10-31 15:31:07,140 INFO  [Thread-971]-skuadapter.SKUSelectionService: Getting selected sku configuration.
2018-10-31 15:31:07,140 INFO  [Thread-971]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:31:07,172 INFO  [Thread-971]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:31:07,172 INFO  [Thread-971]-configure.LogCollectorManager: Created directory: IDPA_MODEL_4400_31_10_2018_03_31_07
2018-10-31 15:31:07,172 INFO  [Thread-971]-configure.LogCollectorManager: Collecting log bundle for DPA.
2018-10-31 15:31:07,172 INFO  [Thread-971]-skuadapter.SKUSelectionService: Getting selected sku configuration.
2018-10-31 15:31:07,172 INFO  [Thread-971]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:31:07,204 INFO  [Thread-971]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:31:07,204 INFO  [Thread-971]-dpaadapter.DPAUtil: Collecting DPA Server logs
2018-10-31 15:31:07,205 INFO  [Thread-971]-dpaadapter.DPAUtil: Created directory: ApplicationServer
2018-10-31 15:31:07,205 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting Data Protection Advisor configuration.
2018-10-31 15:31:07,205 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Retrieving DPA configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/dpaconfig.xml
2018-10-31 15:31:07,206 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Successfully retrieved DPA configuration
2018-10-31 15:31:07,206 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:31:07,250 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:31:07,250 INFO  [Thread-971]-util.CommandExecutor: Executing command : ping -c 2 *************
2018-10-31 15:31:08,253 INFO  [Thread-971]-util.CommandExecutor: Execution of command ping -c 2 ************* completed. Exit Value = 0
2018-10-31 15:31:08,253 INFO  [Thread-971]-util.CommandExecutor: Output: 
2018-10-31 15:31:08,253 INFO  [Thread-971]-util.CommandExecutor: PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.105 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.124 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.105/0.114/0.124/0.014 ms

2018-10-31 15:31:08,253 INFO  [Thread-971]-util.CommandExecutor: Error: 
2018-10-31 15:31:08,253 INFO  [Thread-971]-util.CommandExecutor: 
2018-10-31 15:31:08,253 INFO  [Thread-971]-util.IPAddressUtility: ping result:PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.105 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.124 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.105/0.114/0.124/0.014 ms

2018-10-31 15:31:08,292 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:31:08,293 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:31:08,293 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:31:08,294 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:31:08,294 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:31:08,294 WARN  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:31:18,284 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:31:18,284 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:31:18,285 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:31:18,285 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:31:18,285 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:31:18,285 WARN  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:31:19,875 INFO  [Timer-3]-dao.InfrastructureComponentsDAOImpl: getInfrastructureInfo Retrieving infrastructure components details from file /usr/local/dataprotection/var/configmgr/server_data/config/InfrastructureComponents.xml
2018-10-31 15:31:19,890 INFO  [Timer-3]-dao.InfrastructureComponentsDAOImpl: getInfrastructureInfo Successfully retrieved infrastructure components details
2018-10-31 15:31:19,890 INFO  [Timer-3]-skuadapter.SKUSelectionService: Getting sku modelVersion.
2018-10-31 15:31:19,890 INFO  [Timer-3]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:31:19,922 INFO  [Timer-3]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:31:19,922 INFO  [Timer-3]-dao.ApplianceStatusDAOImpl: Getting appliance Status from file at: /usr/local/dataprotection/var/configmgr/server_data/status/applianceStatus.xml
2018-10-31 15:31:19,923 INFO  [Timer-3]-dao.ApplianceStatusDAOImpl: Successfully retrieved appliance status
2018-10-31 15:31:19,923 INFO  [Timer-3]-skuadapter.SKUSelectionService: Getting sku modelVersion.
2018-10-31 15:31:19,923 INFO  [Timer-3]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml
2018-10-31 15:31:19,955 INFO  [Timer-3]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration
2018-10-31 15:31:19,956 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:19,956 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:19,956 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:19,957 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:19,957 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:19,958 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:19,958 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:19,959 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:19,959 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:19,960 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:19,960 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:19,961 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:19,961 INFO  [Timer-3]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:31:20,005 INFO  [Timer-3]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:31:20,005 INFO  [Timer-3]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:31:20,049 INFO  [Timer-3]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:31:20,049 INFO  [Timer-3]-vi.ViJavaServiceInstanceProvider: Creating service instance for host : *************
2018-10-31 15:31:20,049 INFO  [Timer-3]-vi.ViJavaServiceInstanceProvider: Getting VI Java instance for *************
2018-10-31 15:31:20,049 INFO  [Timer-3]-vi.ViJavaServiceInstanceProvider: ViSDK URL: https://*************:443/sdk
2018-10-31 15:31:20,101 INFO  [Timer-3]-vsphere.VCenterEventTask: getvSphereEvents Got event manager 
2018-10-31 15:31:20,120 INFO  [Timer-3]-vsphere.VCenterEventTask: getBeginTimeForEvents --> Read calender object from file Wed Oct 31 15:11:20 SGT 2018
2018-10-31 15:31:20,120 INFO  [Timer-3]-vsphere.VCenterEventTask: getvSphereEvents Set begin time for event filter: Wed Oct 31 15:11:20 SGT 2018
2018-10-31 15:31:20,289 INFO  [Timer-3]-vsphere.VCenterEventTask: persistEventTime --> Wrote calender object in file Wed Oct 31 15:31:20 SGT 2018
2018-10-31 15:31:20,289 INFO  [Timer-3]-vsphere.VCenterEventTask: getvSphereEvents Number of events retrieved: 91
2018-10-31 15:31:20,289 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:20,289 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,290 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,290 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,290 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,297 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,298 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,299 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,304 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,304 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,304 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,304 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,306 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:20,370 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,376 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,376 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,381 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,381 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,386 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,386 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:20,388 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:20,451 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,456 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,457 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,462 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,462 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,467 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,467 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,467 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,468 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,468 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,468 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,473 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,473 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,473 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,479 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,479 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,479 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,479 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,479 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,480 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,480 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,480 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,485 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,486 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,486 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,490 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,490 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,490 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,490 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:20,491 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,491 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,492 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,492 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,497 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,497 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,497 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,502 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,502 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,502 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,502 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmBeingCreatedEvent
2018-10-31 15:31:20,502 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,502 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,507 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,507 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmInstanceUuidAssignedEvent
2018-10-31 15:31:20,507 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,507 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,512 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,512 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmUuidAssignedEvent
2018-10-31 15:31:20,512 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,512 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,517 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,517 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmMacAssignedEvent
2018-10-31 15:31:20,517 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,517 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,522 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,522 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,524 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:20,585 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,591 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,591 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,596 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,596 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,601 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,601 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:20,603 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:20,666 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,672 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,672 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,677 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,677 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,683 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,683 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,688 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:20,755 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,761 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,761 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,766 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,766 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,771 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,772 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:20,773 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:20,836 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,842 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,842 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,847 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,847 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,852 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,852 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmCreatedEvent
2018-10-31 15:31:20,852 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,852 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,857 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,857 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:20,857 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,858 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,858 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,858 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,863 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,863 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,864 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,868 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,868 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,868 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,868 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,868 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,869 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,869 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,869 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,875 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,875 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,875 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,880 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,880 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,880 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,880 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:20,880 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,880 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,885 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,885 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmReconfiguredEvent
2018-10-31 15:31:20,885 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,885 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,890 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,890 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:20,890 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,891 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,891 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,891 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,896 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,896 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,896 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,901 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,901 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,901 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,901 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:20,901 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,901 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,906 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,906 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmReconfiguredEvent
2018-10-31 15:31:20,906 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:20,906 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:20,911 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,911 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:20,911 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,912 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,912 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,912 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,917 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,917 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,917 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,922 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,922 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,922 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,922 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,922 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,923 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,923 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,923 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,928 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,928 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,928 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,933 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,933 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,933 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,933 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,933 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,934 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,934 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,934 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,939 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,939 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,939 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,944 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,944 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,944 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,944 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:20,944 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:20,945 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:20,945 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:20,945 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:20,950 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,950 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:20,950 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:20,955 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,955 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:20,955 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:20,955 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmBeingCreatedEvent
2018-10-31 15:31:20,955 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:20,955 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:20,960 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,960 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmInstanceUuidAssignedEvent
2018-10-31 15:31:20,960 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:20,960 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:20,965 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,965 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmUuidAssignedEvent
2018-10-31 15:31:20,965 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:20,965 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:20,970 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,970 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmMacAssignedEvent
2018-10-31 15:31:20,970 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:20,970 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:20,975 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,975 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmCreatedEvent
2018-10-31 15:31:20,975 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:20,975 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:20,980 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:20,980 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:20,982 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:21,048 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,054 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,054 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,059 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,059 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,064 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,064 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,066 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:21,129 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,135 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,135 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,140 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,140 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,145 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,146 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:21,148 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:21,211 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,217 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,217 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,222 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,223 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,228 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,228 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,230 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:21,290 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,295 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,295 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,301 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,301 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,306 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,306 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,306 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,307 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,307 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,307 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,312 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,312 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,312 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,317 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,317 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,317 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,317 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:21,317 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,318 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,318 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,318 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,323 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,324 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,324 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,328 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,329 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,329 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,329 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,329 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,329 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,334 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,334 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmReconfiguredEvent
2018-10-31 15:31:21,334 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,334 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,339 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,339 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,339 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,340 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,340 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,340 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,345 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,345 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,346 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,350 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,350 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,350 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,350 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,350 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,350 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,355 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,355 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmReconfiguredEvent
2018-10-31 15:31:21,355 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,355 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,360 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,360 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,360 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,361 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,361 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,362 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,366 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,366 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,367 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,371 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,371 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,371 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,371 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:21,371 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,372 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,372 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,372 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,377 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,378 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,378 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,382 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,382 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,382 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,383 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,383 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,384 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,384 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,384 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,389 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,389 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,389 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,394 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,394 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,394 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,394 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: ResourcePoolCreatedEvent
2018-10-31 15:31:21,394 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,395 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,395 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,395 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,400 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,400 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,400 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,405 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,405 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,410 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,410 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: saveEventInDb --> Set device id from database by getting device data by IP
2018-10-31 15:31:21,417 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,417 INFO  [Timer-3]-dao.EventDao: createEvent -->  row inserted into Event table. Generated Event ID: 4
2018-10-31 15:31:21,417 INFO  [Timer-3]-dao.EventDao: createEventWithDetails --> New event created with ID: 4
2018-10-31 15:31:21,436 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,436 INFO  [Timer-3]-dao.EventDao: createEventDetails -->  row inserted into Event Details table with Event id: 4
2018-10-31 15:31:21,436 INFO  [Timer-3]-dao.EventDao: createEventWithDetails --> Details created for Event ID: 4
2018-10-31 15:31:21,436 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,436 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,436 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,436 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,437 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,437 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,437 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,442 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,442 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,443 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,447 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,447 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,447 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,447 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmResourcePoolMovedEvent
2018-10-31 15:31:21,447 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:21,447 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:21,453 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,453 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,453 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,454 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,454 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,454 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,459 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,459 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,459 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,464 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,464 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,464 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,464 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmResourcePoolMovedEvent
2018-10-31 15:31:21,464 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,464 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,469 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,469 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,469 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,470 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,470 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,470 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,475 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,475 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,476 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,480 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,480 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,480 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,480 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,480 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,481 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,481 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,481 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,486 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,486 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,487 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,491 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,491 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,491 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,491 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,492 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:21,492 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:21,496 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,497 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmStartingEvent
2018-10-31 15:31:21,497 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:21,497 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:21,502 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,502 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmPoweredOnEvent
2018-10-31 15:31:21,502 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:21,502 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:21,507 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,507 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: AlarmStatusChangedEvent
2018-10-31 15:31:21,507 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:21,507 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:21,513 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,513 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: AlarmStatusChangedEvent
2018-10-31 15:31:21,514 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:21,514 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:21,519 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,519 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: AlarmStatusChangedEvent
2018-10-31 15:31:21,519 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPADatastoreServer
2018-10-31 15:31:21,519 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPADatastoreServer
2018-10-31 15:31:21,524 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,524 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,524 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,524 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,529 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,529 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmStartingEvent
2018-10-31 15:31:21,529 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,529 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,534 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,534 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:21,536 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:21,599 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,605 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,605 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,611 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,611 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,617 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,617 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,619 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:21,681 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,698 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,698 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,703 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,704 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,709 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,709 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: VmPoweredOnEvent
2018-10-31 15:31:21,709 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,709 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,714 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,715 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: AlarmStatusChangedEvent
2018-10-31 15:31:21,715 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,715 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,720 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,720 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: AlarmStatusChangedEvent
2018-10-31 15:31:21,720 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,720 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,725 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,725 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: AlarmStatusChangedEvent
2018-10-31 15:31:21,725 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for VM DPAApplicationServer
2018-10-31 15:31:21,725 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByName --> Executing query to get device data by devicename DPAApplicationServer
2018-10-31 15:31:21,730 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,730 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:21,732 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:21,795 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,813 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,813 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,818 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,818 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,824 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,824 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,827 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:21,889 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,895 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,895 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,901 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,901 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,906 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,906 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:21,906 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,908 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,908 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,908 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,913 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,913 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,913 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,918 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,918 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,918 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,918 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:21,918 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,919 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,919 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,919 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,924 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,924 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,924 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,929 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,929 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,929 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,929 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: ResourcePoolMovedEvent
2018-10-31 15:31:21,929 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,930 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,930 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,930 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,936 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,936 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,936 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,941 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,941 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,947 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,947 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: saveEventInDb --> Set device id from database by getting device data by IP
2018-10-31 15:31:21,953 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,953 INFO  [Timer-3]-dao.EventDao: createEvent -->  row inserted into Event table. Generated Event ID: 5
2018-10-31 15:31:21,953 INFO  [Timer-3]-dao.EventDao: createEventWithDetails --> New event created with ID: 5
2018-10-31 15:31:21,959 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,959 INFO  [Timer-3]-dao.EventDao: createEventDetails -->  row inserted into Event Details table with Event id: 5
2018-10-31 15:31:21,959 INFO  [Timer-3]-dao.EventDao: createEventWithDetails --> Details created for Event ID: 5
2018-10-31 15:31:21,959 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,959 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,959 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,959 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,960 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,960 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,960 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,965 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,965 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,966 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,970 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,970 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,970 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,970 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:21,970 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:21,971 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:21,971 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:21,971 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:21,977 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,977 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:21,977 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:21,982 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:21,982 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:21,982 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:21,982 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:21,984 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,046 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,052 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,052 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,057 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,057 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,062 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,062 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:22,064 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,126 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,133 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,133 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,138 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,139 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,144 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,144 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: TaskEvent
2018-10-31 15:31:22,144 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:22,145 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:22,145 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:22,145 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,150 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,150 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:22,150 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:22,155 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,155 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:22,155 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:22,155 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: ScheduledTaskStartedEvent
2018-10-31 15:31:22,155 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:22,156 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:22,156 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:22,156 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,161 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,162 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:22,162 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:22,166 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,166 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:22,166 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:22,167 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: ScheduledTaskCompletedEvent
2018-10-31 15:31:22,167 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:22,167 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:22,168 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:22,168 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,172 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,173 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:22,173 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:22,177 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,178 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:22,178 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:22,178 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:22,180 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,294 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,299 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,299 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,305 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,305 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,315 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,315 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:22,317 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,381 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,386 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,387 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,392 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,392 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,397 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,397 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:22,397 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:22,398 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:22,398 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:22,398 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,403 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,403 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:22,404 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:22,408 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,408 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:22,408 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:22,408 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:22,408 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:22,409 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:22,409 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:22,409 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,414 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,415 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:22,415 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:22,419 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,419 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:22,419 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:22,420 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:22,422 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,485 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,491 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,491 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,496 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,496 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,501 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,501 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:22,504 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,565 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,571 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,571 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,576 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,576 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,581 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,581 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:22,584 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,646 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,652 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,652 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,657 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,657 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,662 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,662 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:22,665 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,727 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,732 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,732 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,737 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,737 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,742 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,743 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:22,745 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,806 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,811 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,811 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,817 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,817 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,822 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,822 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:22,824 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,887 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,893 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,893 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,898 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,898 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,903 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,903 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:22,905 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:22,968 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,974 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,974 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,979 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,979 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:22,984 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:22,984 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLogoutSessionEvent
2018-10-31 15:31:22,987 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for Host esxdp44.ptclab.com
2018-10-31 15:31:23,050 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:23,056 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:23,056 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:23,061 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:23,061 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:23,067 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:23,067 INFO  [Timer-3]-vsphere.VCenterEventTask: Event class name: UserLoginSessionEvent
2018-10-31 15:31:23,067 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml
2018-10-31 15:31:23,068 INFO  [Timer-3]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration
2018-10-31 15:31:23,068 INFO  [Timer-3]-consumer.VcenterEventProcessor: getDeviceDataList --> Getting devicedata for vCenter *************
2018-10-31 15:31:23,068 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getDeviceDataByIp --> Executing query to get device data by IP
2018-10-31 15:31:23,073 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:23,073 INFO  [Timer-3]-healthmonitor.HealthMonitorUtil: createEventMessageAndSaveInDB --> Converting variable message to json
2018-10-31 15:31:23,073 INFO  [Timer-3]-dao.HealthMonitorUtilityDao: getEventDefinitionData --> Executing query to get event definition data
2018-10-31 15:31:23,078 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setPostgressServiceFlag Set flag for postgresfalse
2018-10-31 15:31:23,078 INFO  [Timer-3]-vsphere.VCenterEventTask: publishEvent -->  Event is not Critical/Fatal so not publishing to consumer for process.
2018-10-31 15:31:23,078 INFO  [Timer-3]-healthmonitor.HealthMonitorProcessStatus:  setRabbitMqServiceFlag Set flag for rabbitmq false
2018-10-31 15:31:28,288 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:31:28,289 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:31:28,289 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:31:28,289 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:31:28,290 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:31:28,290 WARN  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:31:38,254 INFO  [Thread-971]-util.CommandExecutor: Executing command : ping -c 2 *************
2018-10-31 15:31:38,292 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:31:38,292 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:31:38,293 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:31:38,293 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:31:38,293 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:31:38,293 WARN  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:31:39,256 INFO  [Thread-971]-util.CommandExecutor: Execution of command ping -c 2 ************* completed. Exit Value = 0
2018-10-31 15:31:39,257 INFO  [Thread-971]-util.CommandExecutor: Output: 
2018-10-31 15:31:39,257 INFO  [Thread-971]-util.CommandExecutor: PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.107 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.139 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 1000ms
rtt min/avg/max/mdev = 0.107/0.123/0.139/0.016 ms

2018-10-31 15:31:39,257 INFO  [Thread-971]-util.CommandExecutor: Error: 
2018-10-31 15:31:39,257 INFO  [Thread-971]-util.CommandExecutor: 
2018-10-31 15:31:39,257 INFO  [Thread-971]-util.IPAddressUtility: ping result:PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.107 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.139 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 1000ms
rtt min/avg/max/mdev = 0.107/0.123/0.139/0.016 ms

2018-10-31 15:31:48,291 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:31:48,291 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:31:48,292 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:31:48,292 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:31:48,292 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:31:48,293 WARN  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:31:58,290 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:31:58,291 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:31:58,291 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:31:58,291 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:31:58,292 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:31:58,292 WARN  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:32:08,299 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:32:08,300 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:32:08,300 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:32:08,300 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:32:08,300 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:32:08,301 WARN  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:32:09,257 INFO  [Thread-971]-util.CommandExecutor: Executing command : ping -c 2 *************
2018-10-31 15:32:10,258 INFO  [Thread-971]-util.CommandExecutor: Execution of command ping -c 2 ************* completed. Exit Value = 0
2018-10-31 15:32:10,258 INFO  [Thread-971]-util.CommandExecutor: Output: 
2018-10-31 15:32:10,258 INFO  [Thread-971]-util.CommandExecutor: PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.137 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.128 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.128/0.132/0.137/0.012 ms

2018-10-31 15:32:10,258 INFO  [Thread-971]-util.CommandExecutor: Error: 
2018-10-31 15:32:10,258 INFO  [Thread-971]-util.CommandExecutor: 
2018-10-31 15:32:10,258 INFO  [Thread-971]-util.IPAddressUtility: ping result:PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.137 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.128 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.128/0.132/0.137/0.012 ms

2018-10-31 15:32:10,259 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:32:10,259 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:32:10,287 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:32:10,288 INFO  [Thread-971]-util.SSHUtil: Successfully tested connection
2018-10-31 15:32:10,288 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /home/<USER>/services && mkdir -p dpa_all_logs/agent && mkdir -p dpa_all_logs/install
2018-10-31 15:32:10,288 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:32:10,288 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:32:10,316 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:32:10,316 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:32:10,316 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:32:10,322 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:32:11,322 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:32:11,322 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:32:11,322 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /home/<USER>/services && mkdir -p dpa_all_logs/agent && mkdir -p dpa_all_logs/install]	STATUS   : [0]
2018-10-31 15:32:11,322 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:32:11,322 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:32:11,322 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:32:11,322 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully created temporary log location on DPA application server.
2018-10-31 15:32:11,322 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /opt/emc/dpa/services/logs && cp -R *.log* audit* /home/<USER>/services
2018-10-31 15:32:11,322 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:32:11,322 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:32:11,352 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:32:11,352 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:32:11,352 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:32:11,358 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:32:12,358 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:32:12,358 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:32:12,358 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /opt/emc/dpa/services/logs && cp -R *.log* audit* /home/<USER>/services]	STATUS   : [0]
2018-10-31 15:32:12,358 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:32:12,358 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:32:12,358 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:32:12,358 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully copied DPA application server services logs.
2018-10-31 15:32:12,358 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /opt/emc/dpa/agent/logs && cp -R *.log* /home/<USER>/agent
2018-10-31 15:32:12,358 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:32:12,358 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:32:12,387 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:32:12,387 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:32:12,387 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:32:12,394 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:32:13,394 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:32:13,394 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:32:13,394 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /opt/emc/dpa/agent/logs && cp -R *.log* /home/<USER>/agent]	STATUS   : [0]
2018-10-31 15:32:13,394 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:32:13,394 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:32:13,394 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:32:13,394 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully copied DPA application server agent logs.
2018-10-31 15:32:13,394 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /opt/emc/dpa/_install && cp -R *.log* *.properties /home/<USER>/install
2018-10-31 15:32:13,394 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:32:13,395 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:32:13,424 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:32:13,425 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:32:13,425 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:32:13,432 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:32:14,432 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:32:14,432 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:32:14,432 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /opt/emc/dpa/_install && cp -R *.log* *.properties /home/<USER>/install]	STATUS   : [1]
2018-10-31 15:32:14,432 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [cp: cannot stat ‘*.properties’: No such file or directory
]
2018-10-31 15:32:14,432 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:32:14,432 ERROR [Thread-971]-util.SSHUtil: Failed to executed remote command using SSH.
2018-10-31 15:32:14,432 INFO  [Thread-971]-dpaadapter.DPAUtil: Failed to copy DPA application server install logs.
2018-10-31 15:32:14,432 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /home/<USER>
2018-10-31 15:32:14,432 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:32:14,432 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:32:14,461 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:32:14,461 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:32:14,461 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:32:14,467 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:32:15,468 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:32:15,468 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:32:15,468 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /home/<USER>
2018-10-31 15:32:15,468 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [./
		    ./services/
		    ./services/actions.log
		    ./services/boot.log
		    ./services/commands.log.0
		    ./services/executive.log
		    ./services/listener.log
		    ./services/perf.log
		    ./services/reportengine.log
		    ./services/server.log
		    ./services/upgrade.log
		    ./services/audit.csv
		    ./agent/
		    ./agent/dpaagent.log
		    ./agent/symapi.log
		    ./install/
		    ./install/Data_Protection_Advisor_Install_07_03_2018_07_30_38.log
		    ./install/versions.log
		    tar: ./Idpa_dpa_app_31_10_2018_03_32_14.tgz: file is the archive; not dumped
]
2018-10-31 15:32:15,468 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:32:15,468 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:32:15,468 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully created tar bundle on DPA appliction server.
2018-10-31 15:32:15,468 INFO  [Thread-971]-dpaadapter.DPAUtil: Coping logs on ACM. tar name: Idpa_dpa_app_31_10_2018_03_32_14.tgz
2018-10-31 15:32:15,468 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:32:15,468 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:32:15,501 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:32:15,573 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully copied logs to ACM from DPA application server.
2018-10-31 15:32:15,573 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /home/<USER>
2018-10-31 15:32:15,574 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:32:15,574 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:32:15,604 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:32:15,604 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:32:15,604 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:32:15,610 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:32:16,610 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:32:16,610 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:32:16,610 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /home/<USER>
2018-10-31 15:32:16,610 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:32:16,610 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:32:16,610 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:32:16,610 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully removed temporary log location from DPA application server.
2018-10-31 15:32:16,610 INFO  [Thread-971]-dpaadapter.DPAUtil: Created directory: DatastoreServer
2018-10-31 15:32:16,610 INFO  [Thread-971]-configure.ConfigInfoHandler: Getting Data Protection Advisor configuration.
2018-10-31 15:32:16,610 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Retrieving DPA configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/dpaconfig.xml
2018-10-31 15:32:16,626 INFO  [Thread-971]-dao.DPAConfigDAOImpl: Successfully retrieved DPA configuration
2018-10-31 15:32:16,626 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml
2018-10-31 15:32:16,670 INFO  [Thread-971]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials
2018-10-31 15:32:16,670 INFO  [Thread-971]-util.CommandExecutor: Executing command : ping -c 2 *************
2018-10-31 15:32:17,673 INFO  [Thread-971]-util.CommandExecutor: Execution of command ping -c 2 ************* completed. Exit Value = 0
2018-10-31 15:32:17,673 INFO  [Thread-971]-util.CommandExecutor: Output: 
2018-10-31 15:32:17,673 INFO  [Thread-971]-util.CommandExecutor: PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.217 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.112 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.112/0.164/0.217/0.054 ms

2018-10-31 15:32:17,673 INFO  [Thread-971]-util.CommandExecutor: Error: 
2018-10-31 15:32:17,673 INFO  [Thread-971]-util.CommandExecutor: 
2018-10-31 15:32:17,673 INFO  [Thread-971]-util.IPAddressUtility: ping result:PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.217 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.112 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.112/0.164/0.217/0.054 ms

2018-10-31 15:32:18,287 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:32:18,288 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:32:18,289 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:32:18,289 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:32:18,289 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:32:18,290 WARN  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:32:28,288 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:32:28,288 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:32:28,289 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:32:28,289 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:32:28,289 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:32:28,289 WARN  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:32:38,292 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:32:38,293 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:32:38,293 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:32:38,293 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:32:38,293 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:32:38,294 WARN  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:32:47,673 INFO  [Thread-971]-util.CommandExecutor: Executing command : ping -c 2 *************
2018-10-31 15:32:48,294 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:32:48,295 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:32:48,295 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:32:48,295 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:32:48,296 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:32:48,296 WARN  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:32:48,675 INFO  [Thread-971]-util.CommandExecutor: Execution of command ping -c 2 ************* completed. Exit Value = 0
2018-10-31 15:32:48,675 INFO  [Thread-971]-util.CommandExecutor: Output: 
2018-10-31 15:32:48,675 INFO  [Thread-971]-util.CommandExecutor: PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.127 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.106 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.106/0.116/0.127/0.015 ms

2018-10-31 15:32:48,675 INFO  [Thread-971]-util.CommandExecutor: Error: 
2018-10-31 15:32:48,675 INFO  [Thread-971]-util.CommandExecutor: 
2018-10-31 15:32:48,675 INFO  [Thread-971]-util.IPAddressUtility: ping result:PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.127 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.106 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.106/0.116/0.127/0.015 ms

2018-10-31 15:32:58,289 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:32:58,289 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:32:58,289 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:32:58,290 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:32:58,290 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:32:58,290 WARN  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:33:08,287 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:33:08,287 INFO  [http-nio-8543-exec-20]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:33:08,287 INFO  [http-nio-8543-exec-20]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:33:08,288 INFO  [http-nio-8543-exec-20]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:33:08,288 INFO  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:33:08,288 WARN  [http-nio-8543-exec-20]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:33:18,289 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Client IP:*************
2018-10-31 15:33:18,289 INFO  [http-nio-8543-exec-15]-filters.BasicAuthFilter: Validating token ... 
2018-10-31 15:33:18,289 INFO  [http-nio-8543-exec-15]-authentication.Tokenization: Token:*************:20181031_124508is present in map
2018-10-31 15:33:18,290 INFO  [http-nio-8543-exec-15]-restadapter.DashboardConfigService: getStatus Received request for retrieving status of Submitted config
2018-10-31 15:33:18,290 INFO  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: getStatus Getting config status.
2018-10-31 15:33:18,290 WARN  [http-nio-8543-exec-15]-configure.DashboardWorkflowManager: Log collection is in progress due to failed configuration.
2018-10-31 15:33:18,675 INFO  [Thread-971]-util.CommandExecutor: Executing command : ping -c 2 *************
2018-10-31 15:33:19,676 INFO  [Thread-971]-util.CommandExecutor: Execution of command ping -c 2 ************* completed. Exit Value = 0
2018-10-31 15:33:19,677 INFO  [Thread-971]-util.CommandExecutor: Output: 
2018-10-31 15:33:19,677 INFO  [Thread-971]-util.CommandExecutor: PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.137 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.119 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.119/0.128/0.137/0.009 ms

2018-10-31 15:33:19,677 INFO  [Thread-971]-util.CommandExecutor: Error: 
2018-10-31 15:33:19,677 INFO  [Thread-971]-util.CommandExecutor: 
2018-10-31 15:33:19,677 INFO  [Thread-971]-util.IPAddressUtility: ping result:PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.137 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.119 ms

--- ************* ping statistics ---
2 packets transmitted, 2 received, 0% packet loss, time 999ms
rtt min/avg/max/mdev = 0.119/0.128/0.137/0.009 ms

2018-10-31 15:33:19,677 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:33:19,677 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:33:19,706 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:33:19,706 INFO  [Thread-971]-util.SSHUtil: Successfully tested connection
2018-10-31 15:33:19,706 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /home/<USER>/services && mkdir -p dpa_all_logs/agent && mkdir -p dpa_all_logs/install
2018-10-31 15:33:19,706 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:33:19,706 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:33:19,737 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:33:19,737 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:33:19,738 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:33:19,743 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:33:20,743 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:33:20,743 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:33:20,743 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /home/<USER>/services && mkdir -p dpa_all_logs/agent && mkdir -p dpa_all_logs/install]	STATUS   : [0]
2018-10-31 15:33:20,743 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:33:20,743 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:33:20,744 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:33:20,744 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully created temporary log location on DPA Datastore server.
2018-10-31 15:33:20,744 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /opt/emc/dpa/services/logs && cp -R *.log* audit* /home/<USER>/services
2018-10-31 15:33:20,744 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:33:20,744 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:33:20,773 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:33:20,773 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:33:20,773 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:33:20,779 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:33:21,780 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:33:21,780 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:33:21,780 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /opt/emc/dpa/services/logs && cp -R *.log* audit* /home/<USER>/services]	STATUS   : [1]
2018-10-31 15:33:21,780 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [cp: cannot stat ‘audit*’: No such file or directory
]
2018-10-31 15:33:21,780 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:33:21,780 ERROR [Thread-971]-util.SSHUtil: Failed to executed remote command using SSH.
2018-10-31 15:33:21,780 INFO  [Thread-971]-dpaadapter.DPAUtil: Failed to copy DPA Datastore server services logs.
2018-10-31 15:33:21,780 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /opt/emc/dpa/agent/logs && cp -R *.log* /home/<USER>/agent
2018-10-31 15:33:21,780 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:33:21,780 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:33:21,810 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:33:21,811 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:33:21,811 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:33:21,816 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:33:22,817 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:33:22,817 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:33:22,817 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /opt/emc/dpa/agent/logs && cp -R *.log* /home/<USER>/agent]	STATUS   : [0]
2018-10-31 15:33:22,817 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:33:22,817 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:33:22,817 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:33:22,817 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully copied DPA Datastore server agent logs.
2018-10-31 15:33:22,817 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /opt/emc/dpa/_install && cp -R *.log* *.properties /home/<USER>/install
2018-10-31 15:33:22,817 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:33:22,817 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:33:22,847 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:33:22,847 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:33:22,847 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:33:22,853 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:33:23,854 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:33:23,854 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:33:23,854 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /opt/emc/dpa/_install && cp -R *.log* *.properties /home/<USER>/install]	STATUS   : [1]
2018-10-31 15:33:23,854 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [cp: cannot stat ‘*.properties’: No such file or directory
]
2018-10-31 15:33:23,854 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:33:23,854 ERROR [Thread-971]-util.SSHUtil: Failed to executed remote command using SSH.
2018-10-31 15:33:23,854 INFO  [Thread-971]-dpaadapter.DPAUtil: Failed to copy DPA Datastore server install logs.
2018-10-31 15:33:23,854 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /home/<USER>
2018-10-31 15:33:23,854 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:33:23,854 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:33:23,884 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:33:23,884 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:33:23,884 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:33:23,889 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:33:24,890 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:33:24,890 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:33:24,890 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /home/<USER>
2018-10-31 15:33:24,890 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : [./
		    ./services/
		    ./services/commands.log.0
		    ./services/datastore-2018-10-31_032207.log
		    ./services/datastore-2018-10-31_032235.log
		    ./services/pg_ctl.log
		    ./agent/
		    ./agent/dpaagent.log
		    ./install/
		    ./install/Data_Protection_Advisor_Install_07_03_2018_05_47_41.log
		    tar: ./Idpa_dpa_ds_31_10_2018_03_33_23.tgz: file is the archive; not dumped
]
2018-10-31 15:33:24,890 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:33:24,890 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:33:24,890 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully created tar bundle on DPA Datastore server.
2018-10-31 15:33:24,890 INFO  [Thread-971]-dpaadapter.DPAUtil: Coping logs on ACM. tar name: Idpa_dpa_ds_31_10_2018_03_33_23.tgz
2018-10-31 15:33:24,890 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:33:24,890 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:33:24,919 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:33:24,991 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully copied logs to ACM from DPA Datastore server.
2018-10-31 15:33:24,991 INFO  [Thread-971]-dpaadapter.DPAUtil: Executing command: cd /home/<USER>
2018-10-31 15:33:24,991 INFO  [Thread-971]-util.SSHUtil: Creating session using SSH parameters: 	 Host     : [*************]	 User     : [root]	 Password : [**********]
2018-10-31 15:33:24,991 INFO  [Thread-971]-util.SSHUtil: Connecting to host [*************] using provided credentials.
2018-10-31 15:33:25,022 INFO  [Thread-971]-util.SSHUtil: Connected to host [*************] using provided credentials.
2018-10-31 15:33:25,022 INFO  [Thread-971]-util.SSHUtil: Opening a channel for communication.
2018-10-31 15:33:25,022 INFO  [Thread-971]-util.SSHUtil: Channel for communication opened successfully.
2018-10-31 15:33:25,028 INFO  [Thread-971]-util.SSHUtil: Waiting for channel close
2018-10-31 15:33:26,028 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution results standard output.
2018-10-31 15:33:26,028 INFO  [Thread-971]-util.SSHUtil: Processing the ssh command execution standard error.
2018-10-31 15:33:26,028 INFO  [Thread-971]-util.SSHUtil: Remote command using SSH execution status:	Host     : [*************]	User     : [root]	Password : [**********]	Command  : [cd /home/<USER>
2018-10-31 15:33:26,028 INFO  [Thread-971]-util.SSHUtil: 	STDOUT   : []
2018-10-31 15:33:26,028 INFO  [Thread-971]-util.SSHUtil: 	STDERR   : []
2018-10-31 15:33:26,028 INFO  [Thread-971]-util.SSHUtil: Successfully executed remote command using SSH.
2018-10-31 15:33:26,028 INFO  [Thread-971]-dpaadapter.DPAUtil: Successfully removed temporary log location from DPA Datastore server.
2018-10-31 15:33:26,028 INFO  [Thread-971]-configure.LogCollectorManager: Successfully collected log bundle for DPA.
2018-10-31 15:33:26,028 INFO  [Thread-971]-configure.LogCollectorManager: Collecting log bundle for ACM.
2018-10-31 15:33:26,028 INFO  [Thread-971]-idpavapp.IdpaVappUtil: Collecting ACM logs...
2018-10-31 15:33:26,029 INFO  [Thread-971]-idpavapp.IdpaVappUtil: Created directory: logs
2018-10-31 15:33:26,029 INFO  [Thread-971]-idpavapp.IdpaVappUtil: Coping ACM logs from /usr/local/dataprotection/var/configmgr/server_data/logs to /data01/log_bundle/DPA/IDPA_MODEL_4400_31_10_2018_03_31_07/ACM/logs
