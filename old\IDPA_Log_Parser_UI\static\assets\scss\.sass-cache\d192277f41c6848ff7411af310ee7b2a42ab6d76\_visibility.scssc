3.5.4 (Bleeding Edge)
c2e008b6d93c47ba7593660b942aea51945f6467
o:Sass::Tree::RootNode:@children[o:Sass::Tree::CommentNode:@value[I"%/*
 * Visibility utilities
 * */:ET:
@type:silent;[ :@filename0:
@options{ :
@linei:@source_rangeo:Sass::Source::Range	:@start_poso:Sass::Source::Position;i:@offseti:
@end_poso;;i;i:
@fileI"q/var/www/html/products/html/admintemplates/sufee-admin-5-18/assets/scss/bootstrap/utilities/_visibility.scss;	T:@importero: Sass::Importers::Filesystem:
@rootI"L/var/www/html/products/html/admintemplates/sufee-admin-5-18/assets/scss;	T:@real_rootI"L/var/www/html/products/html/admintemplates/sufee-admin-5-18/assets/scss;	T:@same_name_warningso:Set:
@hash} Fo:Sass::Tree::RuleNode:
@rule[I"
.visible;	T:@parsed_rules0:@selector_source_rangeo;	;o;;i
;i;o;;i
;i;@;@:
@tabsi ;[o:Sass::Tree::MixinNode:
@nameI"invisible;	T:
@args[o: Sass::Script::Tree::Literal	;o: Sass::Script::Value::String	;I"visible;	T;
@;
:identifier:"@deprecated_interp_equivalent0;i;o;	;o;;i;i;o;;i;i";@;@;@:@keywordso:Sass::Util::NormalizedMap:@key_strings{ :	@map{ :@splat0:@kwarg_splat0;[ ;0;
@;i;o;	;o;;i;i;o;;i;i#;@;@;0;
@;i
;o;	;@;o;;i
;i;@;@:@has_childrenTo;;[I".invisible;	T;0; o;	;o;;i;i;o;;i;i;@;@;!i ;[o;";#I"invisible;	T;$[o;%	;o;&	;I"hidden;	T;
@;
;';(0;i;o;	;o;;i;i;o;;i;i!;@;@;@;)o;*;+{ ;,{ ;-0;.0;[ ;0;
@;i;o;	;o;;i;i;o;;i;i";@;@;0;
@;i;o;	;@2;o;;i;i;@;@;/T;0;
@:@templateI"~//
// Visibility utilities
//

.visible {
  @include invisible(visible);
}

.invisible {
  @include invisible(hidden);
}
;	T;i;o;	;o;;i;i;o;;i;i;@;@;/T