<!doctype html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7" lang=""> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8" lang=""> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9" lang=""> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang=""> <!--<![endif]-->
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Display Table</title>
    <meta name="description" content="Sufee Admin - HTML5 Admin Template">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="apple-touch-icon" href="apple-icon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/normalize.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/bootstrap.min.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/font-awesome.min.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/themify-icons.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/flag-icon.min.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/cs-skin-elastic.css')}}">
    <!-- <link rel="stylesheet" href="assets/css/bootstrap-select.less"> -->
    <link rel="stylesheet" href="{{url_for('static', filename='assets/scss/style.css')}}">
    <link href="{{url_for('static', filename='assets/css/lib/vector-map/jqvmap.min.css')}}" rel="stylesheet">

    <link href='https://fonts.googleapis.com/css?family=Open+Sans:400,600,700,800' rel='stylesheet' type='text/css'>
    
	
	
    <!-- <script type="text/javascript" src="https://cdn.jsdelivr.net/html5shiv/3.7.3/html5shiv.min.js"></script> -->

</head>
<body>


        <!-- Left Panel -->

    <aside id="left-panel" class="left-panel">
        <nav class="navbar navbar-expand-sm navbar-default">

            <div class="navbar-header">
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#main-menu" aria-controls="main-menu" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fa fa-bars"></i>
                </button>
                <a class="navbar-brand" href="./"><img src="{{url_for('static', filename='images/logo.png')}}" alt="Logo"></a>
                <a class="navbar-brand hidden" href="./"><img src="{{url_for('static', filename='images/logo2.png')}}" alt="Logo"></a>
            </div>

            <div id="main-menu" class="main-menu collapse navbar-collapse">
                <ul class="nav navbar-nav">
                    <li class="active">
                        <a href="/"> <i class="menu-icon fa fa-dashboard"></i>Dashboard </a>
                    </li>
                    <h3 class="menu-title">Catalog</h3><!-- /.menu-title -->
                    <li>
                        <a href="#" > <i class="menu-icon fa fa-th"></i>Create</a>
                      
                    </li>
					
					<h3 class="menu-title">Search</h3><!-- /.menu-title -->
					<li>
                        <a href="#" > <i class="menu-icon fa fa-laptop"></i>Search Catalog</a>
                      
                    </li>
					<li>
                        <a href="#" > <i class="menu-icon fa fa-laptop"></i>Live Search (GSAN)</a>
                      
                    </li>

                    <h3 class="menu-title">Display</h3><!-- /.menu-title -->

					
					                    <li>
                        <a href="#" > <i class="menu-icon fa fa-tasks"></i>Table Information</a>
                      
                    </li>
					<h3 class="menu-title">Run Query</h3><!-- /.menu-title -->

					
					                    <li>
                        <a href="#" > <i class="menu-icon fa fa-tasks"></i>Database</a>
                      
                    </li>

					
					
					
					
					
                </ul>
            </div><!-- /.navbar-collapse -->
        </nav>
    </aside><!-- /#left-panel -->

    <!-- Left Panel -->

    <!-- Right Panel -->

    <div id="right-panel" class="right-panel">

        <!-- Header-->
        <header id="header" class="header">

            <div class="header-menu">

                <div class="col-sm-7">
                    <a id="menuToggle" class="menutoggle pull-left"><i class="fa fa fa-tasks"></i></a>
                    <div class="header-left">
                        

                 
                     
                    </div>
                </div>

               
            </div>

        </header><!-- /header -->
        <!-- Header-->

        <div class="breadcrumbs">
            <div class="col-sm-4">
                <div class="page-header float-left">
                    <div class="page-title">
                        <h1>Search Catalog</h1>
                    </div>
                </div>
            </div>
         
        </div>

        <div class="content mt-3">
		<div class="card-footer">
			 <form method="POST" action="/search-catalog-results" onsubmit="ShowLoading()">
				  <h5>Database Name</h5> <br/><input type="text" id="input1-group2"  placeholder="Enter the Database Name without '.db' extension" class="form-control" name="db_name" value="VMFiles"><br/>
				
			<h5>Search Query</h5> <br/><input type="text" id="input1-group2"  placeholder="Search Query eg: mydoc.xml" class="form-control" name="what_to_search" value="/usr/local/dataprotection/var/configmgr/server_data/logs/server.log"><br/>
			 <div class="card-header">
                <strong class="card-title">Select the type of Client : </strong>
                             
  
				<br/><br/>
				<input type="radio" name="fs_or_vmware" value="1">    FS Client<br/>
				<input type="radio" name="fs_or_vmware" value="2" checked>    VMware Client <br/>
				
				
				</div>
			    <div class="card-header">
                <strong class="card-title">Select the type of Search : </strong>
                             
  
				<br/><br/>
				<input type="radio" name="myoption" value="1" checked>    Exact Path or Word Search <br/>
				<input type="radio" name="myoption" value="2">    Contains or Like or Fuzzy Search <br/>
				
				
				
				</div>
				
				<br/><br/>
				
			    <input type="submit" class="btn btn-success btn-lg btn-block">
                                
		</form>
		</div>	
</div>
 
 
    </div><!-- /#right-panel -->

    <!-- Right Panel -->

    <script src="{{url_for('static', filename='assets/js/vendor/jquery-2.1.4.min.js')}}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js"></script>
    <script src="{{url_for('static', filename='assets/js/plugins.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/main.js')}}"></script>

	
	<script type="text/javascript">
    function ShowLoading(e) {
        var div = document.createElement('div');
        var img = document.createElement('img');
	    var imgArray = ['{{url_for('static', filename='_preloader.gif')}}']
	    var randomImage = Math.floor(Math.random()*imgArray.length);
        //img.src = 'b6a8F5G.gif';
        //img.src = imgArray[randomImage];
        
				
		
		///////////////////////////////////////
		
		div.innerHTML = "<br/><b>Searching in Catalog...<br/><br/><br/><br/>Please don't refresh or close this page &nbsp&nbsp&nbsp&nbsp&nbsp<br />";
		
		
		
		
		
        div.style.cssText = 'position: absolute;top: 30%; left: 0;  right: 0;  margin-top: 0; text-align: center;background:rgba(255,255,255,0.9); padding:0 20px;box-sizing:border-box;';
        div.appendChild(img);
        document.body.appendChild(div);
        return true;
        // These 2 lines cancel form submission, so only use if needed.
        //window.event.cancelBubble = true;
        //e.stopPropagation();
    }
	</script>
	
	
	
	
	
	
	
    <script>
        ( function ( $ ) {
            "use strict";

            jQuery( '#vmap' ).vectorMap( {
                map: 'world_en',
                backgroundColor: null,
                color: '#ffffff',
                hoverOpacity: 0.7,
                selectedColor: '#1de9b6',
                enableZoom: true,
                showTooltip: true,
                values: sample_data,
                scaleColors: [ '#1de9b6', '#03a9f5' ],
                normalizeFunction: 'polynomial'
            } );
        } )( jQuery );
    </script>

</body>
</html>
