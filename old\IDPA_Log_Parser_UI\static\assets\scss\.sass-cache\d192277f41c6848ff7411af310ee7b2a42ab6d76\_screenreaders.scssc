3.5.4 (Bleeding Edge)
55d009e79f325128c5972222cffb3d27cb821839
o:Sass::Tree::RootNode:@children[o:Sass::Tree::CommentNode:@value[I"/*
 * Screenreaders
 * */:ET:
@type:silent;[ :@filename0:
@options{ :
@linei:@source_rangeo:Sass::Source::Range	:@start_poso:Sass::Source::Position;i:@offseti:
@end_poso;;i;i:
@fileI"t/var/www/html/products/html/admintemplates/sufee-admin-5-18/assets/scss/bootstrap/utilities/_screenreaders.scss;	T:@importero: Sass::Importers::Filesystem:
@rootI"L/var/www/html/products/html/admintemplates/sufee-admin-5-18/assets/scss;	T:@real_rootI"L/var/www/html/products/html/admintemplates/sufee-admin-5-18/assets/scss;	T:@same_name_warningso:Set:
@hash} Fo:Sass::Tree::RuleNode:
@rule[I"
.sr-only;	T:@parsed_rules0:@selector_source_rangeo;	;o;;i
;i;o;;i
;i;@;@:
@tabsi ;[o:Sass::Tree::MixinNode:
@nameI"sr-only;	T:
@args[ :@keywordso:Sass::Util::NormalizedMap:@key_strings{ :	@map{ :@splat0:@kwarg_splat0;[ ;0;
@;i;o;	;o;;i;i;o;;i;i;@;@;0;
@;i
;o;	;@;o;;i
;i;@;@:@has_childrenTo;;[I".sr-only-focusable;	T;0; o;	;o;;i;i;o;;i;i;@;@;!i ;[o;";#I"sr-only-focusable;	T;$[ ;%o;&;'{ ;({ ;)0;*0;[ ;0;
@;i;o;	;o;;i;i;o;;i;i$;@;@;0;
@;i;o;	;@,;o;;i;i;@;@;+T;0;
@:@templateI"x//
// Screenreaders
//

.sr-only {
  @include sr-only();
}

.sr-only-focusable {
  @include sr-only-focusable();
}
;	T;i;o;	;o;;i;i;o;;i;i;@;@;+T