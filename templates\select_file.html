<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />

<center>
<title>IDPA Log Analyzer : Choose File</title>
</center>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<head>
<link href='https://fonts.googleapis.com/css?family=Raleway' rel='stylesheet'>
<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'>
<style type="text/css">

.alert {
    padding: 1em;
    background: yellow;
}

.form-style-6{
    font: 95% Arial, Helvetica, sans-serif;
    max-width: 900px;
    margin: 10px auto;
    padding: 16px;
    background: white;
    font-family: 'Montserrat';
	
}
.form-style-6 h1{
    background: #0dbaab;
    padding: 20px 0;
    font-size: 140%;
    font-weight: 300;
    text-align: center;
    color: #fff;
    margin: -16px -16px 16px -16px;
   font-family: 'Montserrat';
}


.form-style-6 input[type="text"],
.form-style-6 input[type="date"],
.form-style-6 input[type="datetime"],
.form-style-6 input[type="email"],
.form-style-6 input[type="number"],
.form-style-6 input[type="search"],
.form-style-6 input[type="time"],
.form-style-6 input[type="url"],
.form-style-6 input[type="password"],
.form-style-6 textarea,
.form-style-6 select 
{
    -webkit-transition: all 0.30s ease-in-out;
    -moz-transition: all 0.30s ease-in-out;
    -ms-transition: all 0.30s ease-in-out;
    -o-transition: all 0.30s ease-in-out;
    outline: none;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    background: #fff;
    margin-bottom: 4%;
    border: 1px solid #ccc;
    padding: 3%;
    color: #555;
    font: 95% Arial, Helvetica, sans-serif;
	font-family: 'Montserrat';
}
.form-style-6 input[type="text"]:focus,
.form-style-6 input[type="date"]:focus,
.form-style-6 input[type="datetime"]:focus,
.form-style-6 input[type="email"]:focus,
.form-style-6 input[type="number"]:focus,
.form-style-6 input[type="search"]:focus,
.form-style-6 input[type="time"]:focus,
.form-style-6 input[type="url"]:focus,
.form-style-6 textarea:focus,
.form-style-6 select:focus
{
    box-shadow: 0 0 5px #43D1AF;
    padding: 3%;
    border: 1px solid #43D1AF;
	font-family: 'Montserrat';
}

.form-style-6 input[type="submit"],
.form-style-6 input[type="button"]{
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    padding: 3%;
    background: #33568e;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}
.form-style-6 input[type="submit"]:hover,
.form-style-6 input[type="button"]:hover{
    background: #2EBC99;
}
body {
    font-family: 'Montserrat';font-size: 15px;
}
.hide{
display:none;
}
</style>

<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-F72SBSX0JW"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-F72SBSX0JW');
</script>
</head>
<body>
<center>
<br>
<center><h1><font color="blue">DELL</font> <font color="grey">TECHNOLOGIES</font></h1></center>

<div class="form-style-6">
<h1>Select the File</h1>


{% if target == 1 %}
<form method=POST action="/review_log" onsubmit="ShowLoading()">
{% else %}
<form method=POST action="/lincoln_process" onsubmit="ShowLoading()">
{% endif %}

<p align="left">
{%for file in file_list_obj%}
<font color="red">
{{loop.index}}.) <input type="radio" name="file_name" value="{{file}}!{{lincoln_path}}!{{file_list_obj}}" checked> {{file}}</font><br/><br/>
{% endfor %}
</p>
<br/><br/>
<input type="submit" value='Select'>
	

</form>
<br><br><font size="2">Path : {{lincoln_path}}</font><br><br>
{% with messages = get_flashed_messages() %}
  {% if messages %}
    <ul class=flashes>
    {% for message in messages %}
      <br/><br/><li><b><font color="red">ERROR : {{ message }}</font></b></li>
    {% endfor %}
    </ul>
  {% endif %}
{% endwith %}
{% block body %}{% endblock %}
<br><br>


Please report any problems/bugs or enhancement requests to <a href="mailto:<EMAIL>?subject=SR%20Analyzer%20Tool"><EMAIL></a>

<br><br>

{% if target == 1 %}
<a href="newparser"><button class="button button1">BACK</button></a>
{% else %}
<a href="oldparser"><button class="button button1">BACK</button></a>
{% endif %}
<a href="/"><button class="button button1">HOME</button></a>

</div>


<script type="text/javascript">
    function ShowLoading(e) {
        var div = document.createElement('div');
        var img = document.createElement('img');
	    var imgArray = ['{{url_for('static', filename="_preloader.gif")}}']
	    var randomImage = Math.floor(Math.random()*imgArray.length);
        //img.src = 'b6a8F5G.gif';
        img.src = imgArray[randomImage];
        div.innerHTML = "<br><b>Analyzing File...<b>&nbsp&nbsp&nbsp&nbsp&nbsp<br><br>Please don't refresh or close this page &nbsp&nbsp&nbsp&nbsp&nbsp<br />";
        div.style.cssText = 'position: absolute;top: 30%; left: 0;  right: 0;  margin-top: 0; text-align: center;background:rgba(240,240,240,0.9); padding:0 20px;box-sizing:border-box;';
        div.appendChild(img);
        document.body.appendChild(div);
        return true;
        // These 2 lines cancel form submission, so only use if needed.
        //window.event.cancelBubble = true;
        //e.stopPropagation();
    }

	
</script>

</body>
</html>