<!DOCTYPE html>
<html>
<head>
    <title>AI Dashboard - IDPA Log Analyzer</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<div class="container-fluid mt-4">
    <h1 class="text-center">🤖 AI-Powered Log Analysis Dashboard</h1>
    
    <div class="row mt-4">
        <!-- AI Quick Analysis -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5>🔍 Quick AI Analysis</h5>
                </div>
                <div class="card-body">
                    <textarea class="form-control" id="quickAnalysisText" rows="4" 
                              placeholder="Paste error message for instant AI analysis..."></textarea>
                    <button class="btn btn-primary mt-2" onclick="quickAnalyze()">Analyze with AI</button>
                    <div id="quickAnalysisResult" class="mt-3"></div>
                </div>
            </div>
        </div>
        
        <!-- AI Recommendations -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5>💡 AI Recommendations</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">🔧 Enable proactive monitoring for Avamar components</li>
                        <li class="list-group-item">📊 Set up automated error pattern detection</li>
                        <li class="list-group-item">⚡ Implement real-time log streaming analysis</li>
                        <li class="list-group-item">🎯 Focus on Data Domain connection issues</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <!-- Error Trend Analysis -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning">
                    <h5>📈 AI Error Trend Analysis</h5>
                </div>
                <div class="card-body">
                    <canvas id="errorTrendChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Component Health Score -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5>🏥 AI Health Scores</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label>Avamar</label>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 85%">85%</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Data Domain</label>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 65%">65%</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>vCenter</label>
                        <div class="progress">
                            <div class="progress-bar bg-danger" style="width: 45%">45%</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>DPA</label>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 90%">90%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <!-- AI Insights Feed -->
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5>🧠 Real-time AI Insights</h5>
                </div>
                <div class="card-body">
                    <div id="aiInsightsFeed">
                        <div class="alert alert-info">
                            <strong>🤖 AI Insight:</strong> Detected recurring pattern in vCenter authentication failures. Recommend checking SSL certificates.
                        </div>
                        <div class="alert alert-warning">
                            <strong>🤖 AI Insight:</strong> Data Domain backup jobs showing 15% increase in failures over last 24 hours.
                        </div>
                        <div class="alert alert-success">
                            <strong>🤖 AI Insight:</strong> Avamar performance improved by 23% after recent configuration changes.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="text-center mt-4">
        <a href="/newparser" class="btn btn-primary">Start New Analysis</a>
        <a href="/" class="btn btn-secondary">Home</a>
    </div>
</div>

<script>
// Error Trend Chart
const ctx = document.getElementById('errorTrendChart').getContext('2d');
const errorTrendChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
            label: 'Critical Errors',
            data: [12, 19, 8, 15, 22, 8, 14],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }, {
            label: 'Warning Errors',
            data: [25, 35, 20, 28, 40, 18, 30],
            borderColor: 'rgb(255, 205, 86)',
            backgroundColor: 'rgba(255, 205, 86, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'AI-Predicted Error Trends'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function quickAnalyze() {
    const text = document.getElementById('quickAnalysisText').value;
    const resultDiv = document.getElementById('quickAnalysisResult');
    
    if (!text.trim()) {
        alert('Please enter an error message to analyze');
        return;
    }
    
    resultDiv.innerHTML = `
        <div class="alert alert-info">
            <div class="spinner-border spinner-border-sm" role="status"></div>
            AI is analyzing your error message...
        </div>
    `;
    
    fetch('/ai_analyze_error', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            error_message: text
        })
    })
    .then(response => response.json())
    .then(data => {
        resultDiv.innerHTML = `
            <div class="alert alert-success">
                <h6>🤖 AI Analysis:</h6>
                <pre>${data.analysis.analysis || data.analysis.error}</pre>
                <h6>📚 KB Suggestions:</h6>
                <pre>${data.kb_suggestions}</pre>
            </div>
        `;
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <strong>Error:</strong> ${error.message}
            </div>
        `;
    });
}

// Simulate real-time insights
setInterval(() => {
    const insights = [
        "🤖 AI detected anomaly in backup completion rates",
        "🤖 Predictive model suggests maintenance window needed for Data Domain",
        "🤖 Pattern recognition identified potential security issue in authentication logs",
        "🤖 Performance optimization suggestion: Increase Avamar thread pool size"
    ];
    
    const randomInsight = insights[Math.floor(Math.random() * insights.length)];
    const feed = document.getElementById('aiInsightsFeed');
    
    const newAlert = document.createElement('div');
    newAlert.className = 'alert alert-primary';
    newAlert.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${randomInsight}`;
    
    feed.insertBefore(newAlert, feed.firstChild);
    
    // Keep only last 5 insights
    while (feed.children.length > 5) {
        feed.removeChild(feed.lastChild);
    }
}, 10000); // Every 10 seconds
</script>
</body>
</html>