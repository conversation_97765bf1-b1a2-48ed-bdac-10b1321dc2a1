master_error_dict = {'Unable to set timezone on vCenter server':'Failed to set timezone on vCenter Server,Follow KB525866 to resolve this issue','ddadapter.DataDomainPlugin: Failed to execute: elicense update':'Failed to Configure the DDVE License,This could be due to incorrect License File supplied or DDVE License files has issues such as "#" in the address parameter or maybe incorrect storage','-dpaadapter.DpaPlugin:  Lincese validation failed. License code':'Failed to Configure the DPA License,This could be due to incorrect License File supplied or DPA License files has issues such as "#" in the address parameter or extra space or maybe incorrect License Type','Exception org.elasticsearch.transport.BindTransportException: Failed to resolve publish address':'Failure in ElasticSearch Stack for the Publish Address,One of the DPSearch Services such as ES was not able to start. Workaround is to start the DPS Configuration from ACM Dashboard and once DPS Master VM is deployed we need to edit the elasticsearch.yml located in /etc/elasticsearch of this VM. The editing of the file had to be done as soon as the elasticsearch.yml file was created and before the system loaded the file.You need to replace this line : "network.publish_host: _site_" With : "network.publish_host: _local_" We have about 45 seconds from the time that the file is written to the master node to when the system read this file. Clearquest ID CQSDR0006253 opened against DP Search. KB 525083','com.emc.vcedpa.common.exception.ApplianceException: Data Domain data drive configuration failed':'Failed to configure data drive configuration on DDVE,This could be due to bad cleanup of the DDVE do the cleanup again for DDVE','Error occured while deploying Avamar proxy':'Failed to deploy Avamar Proxy,This could be due to network issues therefore recommended to try manual deploy of proxy using MCGUI before retrying it again.','Exception while deploying DPA Application':'Failed to deploy DPA,In one of such issues there was a connectivity issue between the vmnic and the switch you can check that using the ping and if you see high latency in time parameter.Another reason can be a password issue where password has special character such as hypen in it.','Failed to get MCLogin Info since there are no EMDB properties loaded':'Failed to configure AVE,This could be due to the company name having space in it','Search failed to update new LDAP':'Failed to update LDAP information on DPSearch,This could be due to payload issues with ACM API call to DPsearch. Try using the command from ACM and DPSearch and compare "ldapsearch -h <<LDAP server hostname or IP>> -p 389 -D <<Query Username>> -b <<Base Domain Name>> -w Idpa_1234"','ERROR: sm_cloudunit_create: cloud unit':'Failed to configure Cloud Tier,This could be due to a timeout issue with AWS. See KB 525492','Failed to get ESXi instance':'Failed to get ESXi Details,See Escalation 33273','OVF deployment failed with Exception':'Failed to deploy the inital OVF,This is most likely a DNS issue. Check the network again. See Escalation 33242'}


for key,value in master_error_dict.iteritems():
    if key in 'What a world we live in where Error occured while deploying Avamar proxy':
        print 'Really?'	
	