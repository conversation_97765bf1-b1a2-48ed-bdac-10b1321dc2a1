<!DOCTYPE html>
<html>
<head>
    <title>AI-Enhanced Log Analysis</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</head>
<body>
<div class="container mt-4">
    <h1 class="text-center">🤖 AI-Enhanced Log Analysis</h1>
    
    <!-- AI Pattern Analysis Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h4>🧠 AI Pattern Analysis</h4>
        </div>
        <div class="card-body">
            <pre>{{ pattern_analysis.get('analysis', 'Analysis not available') }}</pre>
        </div>
    </div>
    
    <!-- AI Incident Report -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h4>📋 AI-Generated Incident Report</h4>
        </div>
        <div class="card-body">
            <pre>{{ incident_report }}</pre>
        </div>
    </div>
    
    <!-- Enhanced Thread Analysis -->
    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h4>🔍 AI-Enhanced Thread Analysis</h4>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Thread Name</th>
                        <th>Error Count</th>
                        <th>Owner</th>
                        <th>AI Summary</th>
                        <th>Predicted Resolution Time</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for thread in allthreads_list %}
                    <tr>
                        <td>{{ thread[0] }}</td>
                        <td><span class="badge badge-danger">{{ thread[1] }}</span></td>
                        <td><span class="badge badge-info">{{ thread[7] }}</span></td>
                        <td>{{ thread[10] if thread|length > 10 else 'N/A' }}</td>
                        <td>{{ thread[11] if thread|length > 11 else 'N/A' }}</td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="getAIInsights('{{ thread[0] }}')">
                                AI Insights
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Smart Error Analysis -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h4>⚡ Smart Error Analysis</h4>
        </div>
        <div class="card-body">
            <div class="row">
                {% for error in main_errors_stack[:5] %}
                <div class="col-md-12 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">Error {{ loop.index }}</h6>
                            <p class="card-text"><code>{{ error }}</code></p>
                            <button class="btn btn-sm btn-success" onclick="analyzeError('{{ error }}')">
                                🤖 AI Analyze
                            </button>
                            <div id="ai-analysis-{{ loop.index }}" class="mt-2" style="display:none;">
                                <div class="alert alert-info">
                                    <div class="spinner-border spinner-border-sm" role="status"></div>
                                    Analyzing with AI...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Statistics with AI Insights -->
    <div class="row">
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h5>{{ total_errors_count }}</h5>
                    <p>Total Errors</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5>{{ total_errors_removed }}</h5>
                    <p>Errors Eliminated</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h5>{{ total_errors_left }}</h5>
                    <p>Errors Remaining</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h5>{{ percentage_reduction }}</h5>
                    <p>Reduction %</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="text-center mt-4">
        <a href="/newparser" class="btn btn-primary">New Analysis</a>
        <a href="/" class="btn btn-secondary">Home</a>
    </div>
</div>

<script>
function analyzeError(errorMessage) {
    const errorIndex = event.target.closest('.card').querySelector('.card-title').textContent.split(' ')[1];
    const analysisDiv = document.getElementById(`ai-analysis-${errorIndex}`);
    
    analysisDiv.style.display = 'block';
    
    fetch('/ai_analyze_error', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            error_message: errorMessage
        })
    })
    .then(response => response.json())
    .then(data => {
        analysisDiv.innerHTML = `
            <div class="alert alert-success">
                <h6>AI Analysis:</h6>
                <pre>${data.analysis.analysis || data.analysis.error}</pre>
                <h6>KB Suggestions:</h6>
                <pre>${data.kb_suggestions}</pre>
            </div>
        `;
    })
    .catch(error => {
        analysisDiv.innerHTML = `
            <div class="alert alert-danger">
                Error: ${error.message}
            </div>
        `;
    });
}

function getAIInsights(threadName) {
    fetch(`/ai_thread_insights/${threadName}`)
    .then(response => response.json())
    .then(data => {
        alert(`AI Insights for ${threadName}:\n\n${data.summary}\n\nRecommendations: ${data.recommendations}`);
    })
    .catch(error => {
        alert('Failed to get AI insights: ' + error.message);
    });
}
</script>
</body>
</html>