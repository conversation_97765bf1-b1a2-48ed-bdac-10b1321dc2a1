3.5.4 (Bleeding Edge)
fcf7a35f41a5bace788e93ec0ba7fe8c5ecd75ee
o:Sass::Tree::RootNode:@children[o:Sass::Tree::MixinDefNode:
@nameI"
clearfix:ET:
@args[ :@splat0;[o:Sass::Tree::RuleNode:
@rule[I"
&::after;	T:@parsed_rules0:@selector_source_rangeo:Sass::Source::Range	:@start_poso:Sass::Source::Position:
@linei:@offseti:
@end_poso;;i;i:
@fileI"$bootstrap/mixins/_clearfix.scss;	T:@importero: Sass::Importers::Filesystem:
@rootI"G/var/www/html/products/html/admintemplates/sufee-admin/assets/scss;	T:@real_rootI"G/var/www/html/products/html/admintemplates/sufee-admin/assets/scss;	T:@same_name_warningso:Set:
@hash} F:
@tabsi ;[o:Sass::Tree::PropNode;[I"display;	T:@value[o: Sass::Script::Tree::Literal; o: Sass::Script::Value::String	; I"
block;	T:
@options{ :
@type:identifier:"@deprecated_interp_equivalent0;i:@source_rangeo;	;o;;i;i;o;;i;i;@;@;i :@prop_syntax:new;[ :@filename0;#@;i;'o;	;o;;i;i
;o;;i;i;@;@:@name_source_rangeo;	;@%;o;;i;i;@;@:@value_source_rangeo;	;o;;i;i;@&;@;@o;;[I"
clear;	T; [o;!; o;"	; I"	both;	T;#@;$;%;&0;i	;'o;	;o;;i	;i;o;;i	;i;@;@;i ;(;);[ ;*0;#@;i	;'o;	;o;;i	;i
;o;;i	;i;@;@;+o;	;@7;o;;i	;i;@;@;,o;	;o;;i	;i;@8;@;@o;;[I"content;	T; [o;!; o;"	; I""";	T;#@;$;%;&0;i
;'o;	;o;;i
;i;o;;i
;i;@;@;i ;(;);[ ;*0;#@;i
;'o;	;o;;i
;i
;o;;i
;i;@;@;+o;	;@I;o;;i
;i;@;@;,o;	;o;;i
;i;@J;@;@;*0;#@;i;'o;	;@;o;;i;i;@;@:@has_childrenT;*0;#@;i;'o;	;o;;i;i;o;;i;i;@;@;-T;*0;#@:@templateI"b@mixin clearfix() {
  &::after {
    display: block;
    clear: both;
    content: "";
  }
}
;	T;i;'o;	;o;;i;i;o;;i;i;@;@;-T