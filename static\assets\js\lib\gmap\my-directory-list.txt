The file gmap.init.js contains:
(function($){

var map;
$(document).ready(function(){
    map = new GMaps({
        el: '#basic-map',
        lat: -12.043333,
        lng: -77.028333,
        zoomControl : true,
        zoomControlOpt: {
            style : 'SMALL',
            position: 'TOP_LEFT'
        },
        panControl : false,
        streetViewControl : false,
        mapTypeControl: false,
        overviewMapControl: false
    });
});




var map, infoWindow;
$(document).ready(function(){
    infoWindow = new google.maps.InfoWindow({});
    map = new GMaps({
        el: '#map-2',
        zoom: 11,
        lat: 41.850033,
        lng: -87.6500523
    });
    map.loadFromFusionTables({
        query: {
            select: '\'Geocodable address\'',
            from: '1mZ53Z70NsChnBMm-qEYmSDOvLXgrreLTkQUvvg'
        },
        suppressInfoWindows: true,
        events: {
            click: function(point){
                infoWindow.setContent('You clicked here!');
                infoWindow.setPosition(point.latLng);
                infoWindow.open(map.map);
            }
        }
    });
});




var map, rectangle, polygon, circle;
$(document).ready(function(){
    map = new GMaps({
        el: '#map-3',
        lat: -12.043333,
        lng: -77.028333
    });
    var bounds = [[-12.030397656836609,-77.02373871559225],[-12.034804866577001,-77.01154422636042]];
    rectangle = map.drawRectangle({
        bounds: bounds,
        strokeColor: '#BBD8E9',
        strokeOpacity: 1,
        strokeWeight: 3,
        fillColor: '#BBD8E9',
        fillOpacity: 0.6
    });

    var paths = [[-12.040397656836609,-77.03373871559225],[-12.040248585302038,-77.03993927003302],[-12.050047116528843,-77.02448169303511],[-12.044804866577001,-77.02154422636042]];
    polygon = map.drawPolygon({
        paths: paths,
        strokeColor: '#25D359',
        strokeOpacity: 1,
        strokeWeight: 3,
        fillColor: '#25D359',
        fillOpacity: 0.6
    });
    var lat = -12.040504866577001;
    var lng = -77.02024422636042;
    circle = map.drawCircle({
        lat: lat,
        lng: lng,
        radius: 350,
        strokeColor: '#432070',
        strokeOpacity: 1,
        strokeWeight: 3,
        fillColor: '#432070',
        fillOpacity: 0.6
    });
    for(var i in paths){
        bounds.push(paths[i]);
    }
    var b = [];
    for(var i in bounds){
        latlng = new google.maps.LatLng(bounds[i][0], bounds[i][1]);
        b.push(latlng);
    }
    for(var i in paths){
        latlng = new google.maps.LatLng(paths[i][0], paths[i][1]);
        b.push(latlng);
    }
    map.fitLatLngBounds(b);
});






var map;
$(document).ready(function(){
    map = new GMaps({
        el: '#map-4',
        lat: -12.043333,
        lng: -77.028333
    });
    //locations request
    map.getElevations({
        locations : [[-12.040397656836609,-77.03373871559225], [-12.050047116528843,-77.02448169303511],  [-12.044804866577001,-77.02154422636042]],
        callback : function (result, status){
            if (status == google.maps.ElevationStatus.OK) {
                for (var i in result){
                    map.addMarker({
                        lat: result[i].location.lat(),
                        lng: result[i].location.lng(),
                        title: 'Marker with InfoWindow',
                        infoWindow: {
                            content: '<p>The elevation is '+result[i].elevation+' in meters</p>'
                        }
                    });
                }
            }
        }
    });
});
















var map;
$(document).ready(function(){
    var map = new GMaps({
        el: '#map-5',
        lat: -12.043333,
        lng: -77.028333
    });

    GMaps.geolocate({
        success: function(position){
            map.setCenter(position.coords.latitude, position.coords.longitude);
        },
        error: function(error){
            alert('Geolocation failed: '+error.message);
        },
        not_supported: function(){
            alert("Your browser does not support geolocation");
        },
        always: function(){
            alert("Done!");
        }
    });
});











var map, infoWindow;
$(document).ready(function(){
    infoWindow = new google.maps.InfoWindow({});
    map = new GMaps({
        el: '#map-6',
        zoom: 12,
        lat: 40.65,
        lng: -73.95
    });
    map.loadFromKML({
        url: 'https://api.flickr.com/services/feeds/geo/?g=322338@N20&lang=en-us&format=feed-georss',
        suppressInfoWindows: true,
        events: {
            click: function(point){
                infoWindow.setContent(point.featureData.infoWindowHtml);
                infoWindow.setPosition(point.latLng);
                infoWindow.open(map.map);
            }
        }
    });
});





var map;
$(function () {
    map = new GMaps({
        el: "#map-7",
        lat: -12.043333,
        lng: -77.028333,
        zoom: 3
    });

    map.addLayer('weather', {
        clickable: false
    });
    map.addLayer('clouds');
});






map = new GMaps({
    el: '#map-8',
    zoom: 16,
    lat: -12.043333,
    lng: -77.028333,
    click: function(e){
        alert('click');
    },
    dragend: function(e){
        alert('dragend');
    }
});


})(jQuery);

The file gmapApi.js contains:


window.google = window.google || {};
google.maps = google.maps || {};
(function() {

  function getScript(src) {
    document.write('<' + 'script src="' + src + '"><' + '/script>');
  }

  var modules = google.maps.modules = {};
  google.maps.__gjsload__ = function(name, text) {
    modules[name] = text;
  };

  google.maps.Load = function(apiLoad) {
    delete google.maps.Load;
    apiLoad([0.009999999776482582,[null,[["http://khm0.googleapis.com/kh?v=717\u0026hl=en-US\u0026","http://khm1.googleapis.com/kh?v=717\u0026hl=en-US\u0026"],null,null,null,1,"717",["https://khms0.google.com/kh?v=717\u0026hl=en-US\u0026","https://khms1.google.com/kh?v=717\u0026hl=en-US\u0026"]],null,null,null,null,[["http://cbk0.googleapis.com/cbk?","http://cbk1.googleapis.com/cbk?"]],[["http://khm0.googleapis.com/kh?v=103\u0026hl=en-US\u0026","http://khm1.googleapis.com/kh?v=103\u0026hl=en-US\u0026"],null,null,null,null,"103",["https://khms0.google.com/kh?v=103\u0026hl=en-US\u0026","https://khms1.google.com/kh?v=103\u0026hl=en-US\u0026"]],[["http://mt0.googleapis.com/mapslt?hl=en-US\u0026","http://mt1.googleapis.com/mapslt?hl=en-US\u0026"]],null,null,null,[["https://mts0.googleapis.com/mapslt?hl=en-US\u0026","https://mts1.googleapis.com/mapslt?hl=en-US\u0026"]]],["en-US","US",null,0,null,null,"http://maps.gstatic.com/mapfiles/","http://csi.gstatic.com","https://maps.googleapis.com","http://maps.googleapis.com",null,"https://maps.google.com","https://gg.google.com","http://maps.gstatic.com/maps-api-v3/api/images/","https://www.google.com/maps",0,"https://www.google.com"],["http://maps.google.com/maps-api-v3/api/js/28/3","3.28.3"],[1327120018],1,null,null,null,null,null,"",null,null,0,"http://khm.googleapis.com/mz?v=717\u0026",null,"https://earthbuilder.googleapis.com","https://earthbuilder.googleapis.com",null,"http://mt.googleapis.com/maps/vt/icon",[["http://maps.google.com/maps/vt"],["https://maps.google.com/maps/vt"],null,null,null,null,null,null,null,null,null,null,["https://www.google.com/maps/vt"],"/maps/vt",375000000,375],2,500,[null,null,null,null,"http://www.google.com/maps/preview/log204","","http://static.panoramio.com.storage.googleapis.com/photos/",["http://geo0.ggpht.com/cbk","http://geo1.ggpht.com/cbk","http://geo2.ggpht.com/cbk","http://geo3.ggpht.com/cbk"],"https://maps.googleapis.com/maps/api/js/GeoPhotoService.GetMetadata","https://maps.googleapis.com/maps/api/js/GeoPhotoService.SingleImageSearch",["http://lh3.ggpht.com/","http://lh4.ggpht.com/","http://lh5.ggpht.com/","http://lh6.ggpht.com/"]],["https://www.google.com/maps/api/js/master?pb=!1m2!1u28!2s3!2sen-US!3sUS!4s28/3","https://www.google.com/maps/api/js/widget?pb=!1m2!1u28!2s3!2sen-US"],null,0,null,"/maps/api/js/ApplicationService.GetEntityDetails",0,null,null,[null,null,null,null,null,null,null,null,null,[0,0]],null,[],["28.3"]], loadScriptTime);
  };
  var loadScriptTime = (new Date).getTime();
})();
// inlined
(function(_){var Ga,Ha,Ma,Pa,ib,ob,pb,qb,rb,vb,wb,yb,Bb,xb,Cb,Db,Kb,Ob,Qb,Rb,Sb,Vb,Xb,$b,Ub,Wb,bc,dc,gc,rc,xc,Hc,Mc,Lc,Nc,Qc,Vc,Xc,$c,bd,ad,fd,nd,pd,qd,ud,yd,Bd,Fd,Hd,Nd,ae,ce,ne,oe,qe,se,te,we,ye,xe,Ae,Be,Ce,He,Ie,Je,Me,Ne,Pe,Qe,Re,Se,Ye,$e,af,hf,jf,kf,lf,mf,of,pf,qf,uf,vf,Df,Ff,Of,Uf,Wf,dg,fg,gg,hg,ig,jg,lg,mg,ng,og,sg,qg,tg,ug,yg,Ag,Dg,Eg,Kg,Jg,Ng,Og,Sg,Tg,Wg,Xg,Yg,Zg,$g,ah,bh,ch,dh,Da,Ea;_.ba="ERROR";_.ca="INVALID_REQUEST";_.da="MAX_DIMENSIONS_EXCEEDED";_.ea="MAX_ELEMENTS_EXCEEDED";_.fa="MAX_WAYPOINTS_EXCEEDED";
_.ga="NOT_FOUND";_.ha="OK";_.ia="OVER_QUERY_LIMIT";_.ja="REQUEST_DENIED";_.ka="UNKNOWN_ERROR";_.la="ZERO_RESULTS";_.ma=function(){return function(a){return a}};_.na=function(){return function(){}};_.oa=function(a){return function(b){this[a]=b}};_.pa=function(a){return function(){return this[a]}};_.qa=function(a){return function(){return a}};_.sa=function(a){return function(){return _.ra[a].apply(this,arguments)}};_.m=function(a){return void 0!==a};_.ta=_.na();
_.ua=function(a){var b=typeof a;if("object"==b)if(a){if(a instanceof Array)return"array";if(a instanceof Object)return b;var c=Object.prototype.toString.call(a);if("[object Window]"==c)return"object";if("[object Array]"==c||"number"==typeof a.length&&"undefined"!=typeof a.splice&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("splice"))return"array";if("[object Function]"==c||"undefined"!=typeof a.call&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("call"))return"function"}else return"null";
else if("function"==b&&"undefined"==typeof a.call)return"object";return b};_.va=function(a){return"array"==_.ua(a)};_.wa=function(a){var b=_.ua(a);return"array"==b||"object"==b&&"number"==typeof a.length};_.ya=function(a){return"string"==typeof a};_.za=function(a){return"number"==typeof a};_.Aa=function(a){return"function"==_.ua(a)};_.Ba=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b};_.Fa=function(a){return a[Da]||(a[Da]=++Ea)};
Ga=function(a,b,c){return a.call.apply(a.bind,arguments)};Ha=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var c=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(c,d);return a.apply(b,c)}}return function(){return a.apply(b,arguments)}};_.p=function(a,b,c){_.p=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?Ga:Ha;return _.p.apply(null,arguments)};_.Ia=function(){return+new Date};
_.t=function(a,b){function c(){}c.prototype=b.prototype;a.Hb=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ee=function(a,c,f){for(var d=Array(arguments.length-2),e=2;e<arguments.length;e++)d[e-2]=arguments[e];b.prototype[c].apply(a,d)}};_.Ja=function(a){return a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")};_.La=function(){return-1!=_.Ka.toLowerCase().indexOf("webkit")};
_.Na=function(a,b){var c=0;a=_.Ja(String(a)).split(".");b=_.Ja(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;0==c&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(0==f[0].length&&0==g[0].length)break;c=Ma(0==f[1].length?0:(0,window.parseInt)(f[1],10),0==g[1].length?0:(0,window.parseInt)(g[1],10))||Ma(0==f[2].length,0==g[2].length)||Ma(f[2],g[2]);f=f[3];g=g[3]}while(0==c)}return c};
Ma=function(a,b){return a<b?-1:a>b?1:0};_.Oa=function(a,b,c){c=null==c?0:0>c?Math.max(0,a.length+c):c;if(_.ya(a))return _.ya(b)&&1==b.length?a.indexOf(b,c):-1;for(;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};_.v=function(a,b,c){for(var d=a.length,e=_.ya(a)?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};Pa=function(a,b){for(var c=a.length,d=_.ya(a)?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return e;return-1};
_.Ra=function(a,b){b=_.Oa(a,b);var c;(c=0<=b)&&_.Qa(a,b);return c};_.Qa=function(a,b){Array.prototype.splice.call(a,b,1)};_.Sa=function(a,b,c){return 2>=arguments.length?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};_.Ta=function(a){return""+(_.Ba(a)?_.Fa(a):a)};_.w=function(a){return a?a.length:0};_.Wa=function(a,b){_.Ua(b,function(c){a[c]=b[c]})};_.Xa=function(a){for(var b in a)return!1;return!0};_.Ya=function(a,b,c){null!=b&&(a=Math.max(a,b));null!=c&&(a=Math.min(a,c));return a};
_.Za=function(a,b,c){c-=b;return((a-b)%c+c)%c+b};_.$a=function(a,b,c){return Math.abs(a-b)<=(c||1E-9)};_.ab=function(a,b){for(var c=[],d=_.w(a),e=0;e<d;++e)c.push(b(a[e],e));return c};_.cb=function(a,b){for(var c=_.bb(void 0,_.w(b)),d=_.bb(void 0,0);d<c;++d)a.push(b[d])};_.x=function(a){return"number"==typeof a};_.db=function(a){return"object"==typeof a};_.bb=function(a,b){return null==a?b:a};_.eb=function(a){return"string"==typeof a};_.fb=function(a){return a===!!a};
_.Ua=function(a,b){for(var c in a)b(c,a[c])};_.hb=function(a){return function(){var b=this,c=arguments;_.gb(function(){a.apply(b,c)})}};_.gb=function(a){return window.setTimeout(a,0)};ib=function(a,b){if(Object.prototype.hasOwnProperty.call(a,b))return a[b]};_.jb=function(a){window.console&&window.console.error&&window.console.error(a)};_.mb=function(a){a=a||window.event;_.kb(a);_.lb(a)};_.kb=function(a){a.cancelBubble=!0;a.stopPropagation&&a.stopPropagation()};
_.lb=function(a){a.preventDefault&&_.m(a.defaultPrevented)?a.preventDefault():a.returnValue=!1};_.nb=function(a){a.handled=!0;_.m(a.bubbles)||(a.returnValue="handled")};ob=function(a,b){a.__e3_||(a.__e3_={});a=a.__e3_;a[b]||(a[b]={});return a[b]};pb=function(a,b){var c=a.__e3_||{};if(b)a=c[b]||{};else for(b in a={},c)_.Wa(a,c[b]);return a};qb=function(a,b){return function(c){return b.call(a,c,this)}};
rb=function(a,b,c){return function(d){var e=[b,a];_.cb(e,arguments);_.y.trigger.apply(this,e);c&&_.nb.apply(null,arguments)}};vb=function(a,b,c,d){this.f=a;this.j=b;this.b=c;this.l=null;this.m=d;this.id=++sb;ob(a,b)[this.id]=this;tb&&"tagName"in a&&(ub[this.id]=this)};wb=function(a){return a.l=function(b){b||(b=window.event);if(b&&!b.target)try{b.target=b.srcElement}catch(d){}var c;c=a.b.apply(a.f,[b]);return b&&"click"==b.type&&(b=b.srcElement)&&"A"==b.tagName&&"javascript:void(0)"==b.href?!1:c}};
_.C=_.na();yb=function(a,b){var c=b+"_changed";if(a[c])a[c]();else a.changed(b);var c=xb(a,b),d;for(d in c){var e=c[d];yb(e.Ac,e.Ya)}_.y.trigger(a,b.toLowerCase()+"_changed")};_.Ab=function(a){return zb[a]||(zb[a]=a.substr(0,1).toUpperCase()+a.substr(1))};Bb=function(a){a.gm_accessors_||(a.gm_accessors_={});return a.gm_accessors_};xb=function(a,b){a.gm_bindings_||(a.gm_bindings_={});a.gm_bindings_.hasOwnProperty(b)||(a.gm_bindings_[b]={});return a.gm_bindings_[b]};Cb=_.na();
Db=function(a,b,c){this.l=c;this.j=a;this.m=b;this.f=0;this.b=null};_.Eb=_.ma();_.Fb=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};_.Gb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};_.Ib=function(a){_.Ib[" "](a);return a};Kb=function(a,b){var c=Jb;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};_.Lb=function(a,b){this.f=a||0;this.j=b||0};_.Mb=function(a,b){if(a)return function(){--a||b()};b();return _.ta};
_.Nb=function(a,b,c){var d=a.getElementsByTagName("head")[0];a=a.createElement("script");a.type="text/javascript";a.charset="UTF-8";a.src=b;c&&(a.onerror=c);d.appendChild(a);return a};Ob=function(a){for(var b="",c=0,d=arguments.length;c<d;++c){var e=arguments[c];e.length&&"/"==e[0]?b=e:(b&&"/"!=b[b.length-1]&&(b+="/"),b+=e)}return b};_.Pb=function(a){return-1!=_.Ka.indexOf(a)};Qb=function(a){this.j=window.document;this.b={};this.f=a};Rb=function(){this.f=this.b=null};
Sb=function(){this.next=this.b=this.xc=null};_.Tb=function(){return _.Pb("iPhone")&&!_.Pb("iPod")&&!_.Pb("iPad")};Vb=function(){this.l={};this.f={};this.m={};this.b={};this.j=new Ub};Xb=function(a,b){a.l[b]||(a.l[b]=!0,Wb(a.j,function(c){for(var d=c.fh[b],e=d?d.length:0,f=0;f<e;++f){var g=d[f];a.b[g]||Xb(a,g)}c=c.Yl;c.b[b]||_.Nb(c.j,Ob(c.f,b)+".js")}))};
$b=function(a,b){var c=Yb;this.Yl=a;this.fh=c;a={};for(var d in c)for(var e=c[d],f=0,g=e.length;f<g;++f){var h=e[f];a[h]||(a[h]=[]);a[h].push(d)}this.gn=a;this.nk=b};Ub=function(){this.b=[]};Wb=function(a,b){a.f?b(a.f):a.b.push(b)};bc=function(a){var b=a;if(a instanceof Array)b=Array(a.length),_.ac(b,a);else if(a instanceof Object){var c=b={},d;for(d in a)a.hasOwnProperty(d)&&(c[d]=bc(a[d]))}return b};_.ac=function(a,b){for(var c=0;c<b.length;++c)b.hasOwnProperty(c)&&(a[c]=bc(b[c]))};
_.cc=function(a,b){a[b]||(a[b]=[]);return a[b]};_.ec=function(a,b){if(null==a||null==b)return null==a==(null==b);if(a.constructor!=Array&&a.constructor!=Object)throw Error("Invalid object type passed into jsproto.areObjectsEqual()");if(a===b)return!0;if(a.constructor!=b.constructor)return!1;for(var c in a)if(!(c in b&&dc(a[c],b[c])))return!1;for(var d in b)if(!(d in a))return!1;return!0};
dc=function(a,b){if(a===b||!(!0!==a&&1!==a||!0!==b&&1!==b)||!(!1!==a&&0!==a||!1!==b&&0!==b))return!0;if(a instanceof Object&&b instanceof Object){if(!_.ec(a,b))return!1}else return!1;return!0};_.fc=function(a,b,c,d){this.type=a;this.label=b;this.vk=c;this.vc=d};gc=function(a){switch(a){case "d":case "f":case "i":case "j":case "u":case "v":case "x":case "y":case "g":case "h":case "n":case "o":case "e":return 0;case "s":case "z":case "B":return"";case "b":return!1;default:return null}};
_.hc=function(a,b,c){return new _.fc(a,1,_.m(b)?b:gc(a),c)};_.ic=function(a,b,c){return new _.fc(a,2,_.m(b)?b:gc(a),c)};_.jc=function(a){return _.hc("i",a)};_.nc=function(a){return _.hc("v",a)};_.oc=function(a){return _.hc("b",a)};_.pc=function(a){return _.hc("e",a)};_.F=function(a,b){return _.hc("m",a,b)};_.qc=function(){return _.Pb("Trident")||_.Pb("MSIE")};_.sc=function(){return _.Pb("Safari")&&!(rc()||_.Pb("Coast")||_.Pb("Opera")||_.Pb("Edge")||_.Pb("Silk")||_.Pb("Android"))};
rc=function(){return(_.Pb("Chrome")||_.Pb("CriOS"))&&!_.Pb("Edge")};_.tc=function(a){return a*Math.PI/180};_.uc=function(a){return 180*a/Math.PI};_.vc=_.oa("b");_.G=function(a,b,c){var d=Vb.b();a=""+a;d.b[a]?b(d.b[a]):((d.f[a]=d.f[a]||[]).push(b),c||Xb(d,a))};_.wc=function(a,b){Vb.b().b[""+a]=b};xc=function(a,b,c){var d=[],e=_.Mb(a.length,function(){b.apply(null,d)});_.v(a,function(a,b){_.G(a,function(a){d[b]=a;e()},c)})};_.I=function(a){this.data=a||[]};
_.yc=function(a,b,c){a=a.data[b];return null!=a?a:c};_.K=function(a,b,c){return _.yc(a,b,c||0)};_.L=function(a,b,c){return _.yc(a,b,c||"")};_.M=function(a,b){var c=a.data[b];c||(c=a.data[b]=[]);return c};_.zc=function(a,b){return _.cc(a.data,b)};_.Ec=function(a,b,c){return _.zc(a,b)[c]};_.Fc=function(a,b){return a.data[b]?a.data[b].length:0};Hc=function(a){_.Gc.setTimeout(function(){throw a;},0)};
Mc=function(){var a=_.Ic.f,a=Jc(a);!_.Aa(_.Gc.setImmediate)||_.Gc.Window&&_.Gc.Window.prototype&&!_.Pb("Edge")&&_.Gc.Window.prototype.setImmediate==_.Gc.setImmediate?(Kc||(Kc=Lc()),Kc(a)):_.Gc.setImmediate(a)};
Lc=function(){var a=_.Gc.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!_.Pb("Presto")&&(a=function(){var a=window.document.createElement("IFRAME");a.style.display="none";a.src="";window.document.documentElement.appendChild(a);var b=a.contentWindow,a=b.document;a.open();a.write("");a.close();var c="callImmediate"+Math.random(),d="file:"==b.location.protocol?"*":b.location.protocol+"//"+b.location.host,a=(0,_.p)(function(a){if(("*"==
d||a.origin==d)&&a.data==c)this.port1.onmessage()},this);b.addEventListener("message",a,!1);this.port1={};this.port2={postMessage:function(){b.postMessage(c,d)}}});if("undefined"!==typeof a&&!_.qc()){var b=new a,c={},d=c;b.port1.onmessage=function(){if(_.m(c.next)){c=c.next;var a=c.ng;c.ng=null;a()}};return function(a){d.next={ng:a};d=d.next;b.port2.postMessage(0)}}return"undefined"!==typeof window.document&&"onreadystatechange"in window.document.createElement("SCRIPT")?function(a){var b=window.document.createElement("SCRIPT");
b.onreadystatechange=function(){b.onreadystatechange=null;b.parentNode.removeChild(b);b=null;a();a=null};window.document.documentElement.appendChild(b)}:function(a){_.Gc.setTimeout(a,0)}};Nc=function(){var a=_.Gc.document;return a?a.documentMode:void 0};_.Pc=function(a){return Kb(a,function(){return 0<=_.Na(_.Oc,a)})};Qc=function(a,b){-180==a&&180!=b&&(a=180);-180==b&&180!=a&&(b=180);this.b=a;this.f=b};_.Rc=function(a){return a.b>a.f};
_.Tc=function(a,b){return 1E-9>=Math.abs(b.b-a.b)%360+Math.abs(_.Sc(b)-_.Sc(a))};_.Uc=function(a,b){var c=b-a;return 0<=c?c:b+180-(a-180)};_.Sc=function(a){return a.isEmpty()?0:_.Rc(a)?360-(a.b-a.f):a.f-a.b};Vc=function(a,b){this.f=a;this.b=b};_.Wc=function(a){return a.isEmpty()?0:a.b-a.f};Xc=function(a){this.message=a;this.name="InvalidValueError";this.stack=Error().stack};_.Yc=function(a,b){var c="";if(null!=b){if(!(b instanceof Xc))return b;c=": "+b.message}return new Xc(a+c)};
_.Zc=function(a){if(!(a instanceof Xc))throw a;_.jb(a.name+": "+a.message)};$c=_.na();bd=function(a,b,c){for(var d=1;d<b.A.length;++d){var e=b.A[d],f=a[d+b.b];if(e&&null!=f)if(3==e.label)for(var g=0;g<f.length;++g)ad(f[g],d,e,c);else ad(f,d,e,c)}};ad=function(a,b,c,d){if("m"==c.type){var e=d.length;bd(a,c.vc,d);d.splice(e,0,[b,"m",d.length-e].join(""))}else"b"==c.type&&(a=a?"1":"0"),d.push([b,c.type,(0,window.encodeURIComponent)(a)].join(""))};
_.Ic=function(a,b){_.Ic.b||_.Ic.m();_.Ic.j||(_.Ic.b(),_.Ic.j=!0);_.Ic.l.add(a,b)};_.cd=function(a,b){var c;c=c?c+": ":"";return function(d){if(!d||!_.db(d))throw _.Yc(c+"not an Object");var e={},f;for(f in d)if(e[f]=d[f],!b&&!a[f])throw _.Yc(c+"unknown property "+f);for(f in a)try{var g=a[f](e[f]);if(_.m(g)||Object.prototype.hasOwnProperty.call(d,f))e[f]=a[f](e[f])}catch(h){throw _.Yc(c+"in property "+f,h);}return e}};fd=function(a){try{return!!a.cloneNode}catch(b){return!1}};
_.gd=function(a,b,c){return c?function(c){if(c instanceof a)return c;try{return new a(c)}catch(e){throw _.Yc("when calling new "+b,e);}}:function(c){if(c instanceof a)return c;throw _.Yc("not an instance of "+b);}};_.hd=function(a){return function(b){for(var c in a)if(a[c]==b)return b;throw _.Yc(b);}};_.id=function(a){return function(b){if(!_.va(b))throw _.Yc("not an Array");return _.ab(b,function(b,d){try{return a(b)}catch(e){throw _.Yc("at index "+d,e);}})}};
_.jd=function(a,b){return function(c){if(a(c))return c;throw _.Yc(b||""+c);}};_.kd=function(a){return function(b){for(var c=[],d=0,e=a.length;d<e;++d){var f=a[d];try{(f.If||f)(b)}catch(g){if(!(g instanceof Xc))throw g;c.push(g.message);continue}return(f.then||f)(b)}throw _.Yc(c.join("; and "));}};_.ld=function(a,b){return function(c){return b(a(c))}};_.md=function(a){return function(b){return null==b?b:a(b)}};
nd=function(a){return function(b){if(b&&null!=b[a])return b;throw _.Yc("no "+a+" property");}};_.N=function(a,b){this.x=a;this.y=b};pd=function(a){if(a instanceof _.N)return a;try{_.cd({x:_.od,y:_.od},!0)(a)}catch(b){throw _.Yc("not a Point",b);}return new _.N(a.x,a.y)};_.O=function(a,b,c,d){this.width=a;this.height=b;this.j=c||"px";this.f=d||"px"};qd=function(a){if(a instanceof _.O)return a;try{_.cd({height:_.od,width:_.od},!0)(a)}catch(b){throw _.Yc("not a Size",b);}return new _.O(a.width,a.height)};
ud=function(a){var b=rd,c=Vb.b().j;a=c.f=new $b(new Qb(a),b);for(var b=0,d=c.b.length;b<d;++b)c.b[b](a);c.b.length=0};_.vd=function(a){this.j=a||_.Ta;this.f={}};_.wd=function(a,b){var c=a.f,d=a.j(b);c[d]||(c[d]=b,_.y.trigger(a,"insert",b),a.b&&a.b(b))};_.xd=function(a,b,c){this.heading=a;this.pitch=_.Ya(b,-90,90);this.zoom=Math.max(0,c)};yd=function(a){this.P=[];this.b=a&&a.bd||_.ta;this.f=a&&a.cd||_.ta};
_.Ad=function(a,b,c,d){function e(){_.v(f,function(a){b.call(c||null,function(b){if(a.once){if(a.once.lg)return;a.once.lg=!0;_.Ra(g.P,a);g.P.length||g.b()}a.xc.call(a.context,b)})})}var f=a.P.slice(0),g=a;d&&d.Gn?e():zd(e)};Bd=function(a,b){return function(c){return c.xc==a&&c.context==(b||null)}};
_.Q=function(a,b,c){if(a&&(void 0!==a.lat||void 0!==a.lng))try{Cd(a),b=a.lng,a=a.lat,c=!1}catch(d){_.Zc(d)}a-=0;b-=0;c||(a=_.Ya(a,-90,90),180!=b&&(b=_.Za(b,-180,180)));this.lat=function(){return a};this.lng=function(){return b}};_.Dd=function(a){return _.tc(a.lat())};_.Ed=function(a){return _.tc(a.lng())};Fd=function(a,b){b=Math.pow(10,b);return Math.round(a*b)/b};_.Gd=function(){this.P=new yd({bd:(0,_.p)(this.bd,this),cd:(0,_.p)(this.cd,this)})};Hd=_.na();
_.Id=function(a){try{if(a instanceof _.Q)return a;a=Cd(a);return new _.Q(a.lat,a.lng)}catch(b){throw _.Yc("not a LatLng or LatLngLiteral",b);}};_.Jd=function(a){return function(){return this.get(a)}};_.Kd=function(a,b){return b?function(c){try{this.set(a,b(c))}catch(d){_.Zc(_.Yc("set"+_.Ab(a),d))}}:function(b){this.set(a,b)}};_.Ld=function(a,b){_.Ua(b,function(b,d){var c=_.Jd(b);a["get"+_.Ab(b)]=c;d&&(d=_.Kd(b,d),a["set"+_.Ab(b)]=d)})};_.Md=function(){_.Gd.call(this)};_.Od=function(a){return new Nd(a)};
Nd=function(a){_.Gd.call(this);this.b=a};_.Td=function(a){this.b=(0,_.Pd)(a)};_.Ud=function(a){this.b=(0,_.Pd)(a)};_.Vd=function(a){this.b=(0,_.Pd)(a)};_.Wd=function(a){this.b=_.Id(a)};_.Xd=function(a,b){a=a&&_.Id(a);b=b&&_.Id(b);if(a){b=b||a;var c=_.Ya(a.lat(),-90,90),d=_.Ya(b.lat(),-90,90);this.f=new Vc(c,d);a=a.lng();b=b.lng();360<=b-a?this.b=new Qc(-180,180):(a=_.Za(a,-180,180),b=_.Za(b,-180,180),this.b=new Qc(a,b))}else this.f=new Vc(1,-1),this.b=new Qc(180,-180)};
_.Yd=function(a,b,c,d){return new _.Xd(new _.Q(a,b,!0),new _.Q(c,d,!0))};_.$d=function(a){if(a instanceof _.Xd)return a;try{return a=Zd(a),_.Yd(a.south,a.west,a.north,a.east)}catch(b){throw _.Yc("not a LatLngBounds or LatLngBoundsLiteral",b);}};_.be=function(a){this.b=a||[];ae(this)};ae=function(a){a.set("length",a.b.length)};ce=function(a){if(a instanceof Hd)return a;try{return new _.Wd(_.Id(a))}catch(b){}throw _.Yc("not a Geometry or LatLng or LatLngLiteral object");};_.ee=function(a){this.b=de(a)};
_.ge=function(a){this.b=fe(a)};_.he=function(a){a=a||{};this.j=a.id;this.b=null;try{this.b=a.geometry?ce(a.geometry):null}catch(b){_.Zc(b)}this.f=a.properties||{}};_.je=function(a){this.b=[];try{this.b=ie(a)}catch(b){_.Zc(b)}};_.le=function(a){this.b=ke(a)};_.me=function(){this.__gm=new _.C;this.l=null};ne=function(){this.b={}};oe=_.na();qe=function(){this.b={};this.j={};this.f={}};
_.re=function(a,b,c){function d(a){if(!a)throw _.Yc("not a Feature");if("Feature"!=a.type)throw _.Yc('type != "Feature"');var b=a.geometry;try{b=null==b?null:e(b)}catch(J){throw _.Yc('in property "geometry"',J);}var d=a.properties||{};if(!_.db(d))throw _.Yc("properties is not an Object");var f=c.idPropertyName;a=f?d[f]:a.id;if(null!=a&&!_.x(a)&&!_.eb(a))throw _.Yc((f||"id")+" is not a string or number");return{id:a,geometry:b,properties:d}}function e(a){if(null==a)throw _.Yc("is null");var b=(a.type+
"").toLowerCase(),c=a.coordinates;try{switch(b){case "point":return new _.Wd(h(c));case "multipoint":return new _.Vd(n(c));case "linestring":return g(c);case "multilinestring":return new _.ee(q(c));case "polygon":return f(c);case "multipolygon":return new _.le(u(c))}}catch(D){throw _.Yc('in property "coordinates"',D);}if("geometrycollection"==b)try{return new _.je(A(a.geometries))}catch(D){throw _.Yc('in property "geometries"',D);}throw _.Yc("invalid type");}function f(a){return new _.ge(r(a))}function g(a){return new _.Td(n(a))}
function h(a){a=l(a);return _.Id({lat:a[1],lng:a[0]})}if(!b)return[];c=c||{};var l=_.id(_.od),n=_.id(h),q=_.id(g),r=_.id(function(a){a=n(a);if(!a.length)throw _.Yc("contains no elements");if(!a[0].b(a[a.length-1]))throw _.Yc("first and last positions are not equal");return new _.Ud(a.slice(0,-1))}),u=_.id(f),A=_.id(e),B=_.id(d);if("FeatureCollection"==b.type){b=b.features;try{return _.ab(B(b),function(b){return a.add(b)})}catch(E){throw _.Yc('in property "features"',E);}}if("Feature"==b.type)return[a.add(d(b))];
throw _.Yc("not a Feature or FeatureCollection");};se=_.na();te=function(a){a=a||{};a.visible=_.bb(a.visible,!0);return a};_.ue=function(a){return a&&a.radius||6378137};we=function(a){return a instanceof _.be?ve(a):new _.be((0,_.Pd)(a))};ye=function(a){var b;_.va(a)||a instanceof _.be?0==_.w(a)?b=!0:(b=a instanceof _.be?a.getAt(0):a[0],b=_.va(b)||b instanceof _.be):b=!1;return b?a instanceof _.be?xe(ve)(a):new _.be(_.id(we)(a)):new _.be([we(a)])};
xe=function(a){return function(b){if(!(b instanceof _.be))throw _.Yc("not an MVCArray");b.forEach(function(b,d){try{a(b)}catch(e){throw _.Yc("at index "+d,e);}});return b}};_.ze=_.oa("__gm");Ae=function(a){this.b=new ne;var b=this;_.y.addListenerOnce(a,"addfeature",function(){_.G("data",function(c){c.b(b,a,b.b)})})};Be=function(a){a=a||{};a.clickable=_.bb(a.clickable,!0);a.visible=_.bb(a.visible,!0);this.setValues(a);_.G("marker",_.ta)};
Ce=function(a){this.set("latLngs",new _.be([new _.be]));this.setValues(te(a));_.G("poly",_.ta)};_.De=function(a){this.__gm={set:null,Gd:null,Eb:{map:null,Wd:null}};Be.call(this,a)};_.Ee=function(a){Ce.call(this,a)};_.Fe=function(a){Ce.call(this,a)};
He=function(a){var b=this;a=a||{};this.setValues(a);this.b=new qe;_.y.forward(this.b,"addfeature",this);_.y.forward(this.b,"removefeature",this);_.y.forward(this.b,"setgeometry",this);_.y.forward(this.b,"setproperty",this);_.y.forward(this.b,"removeproperty",this);this.f=new Ae(this.b);this.f.bindTo("map",this);this.f.bindTo("style",this);_.v(_.Ge,function(a){_.y.forward(b.f,a,b)});this.j=!1};Ie=function(a){a.j||(a.j=!0,_.G("drawing_impl",function(b){b.ql(a)}))};
Je=function(a){if(!a)return null;var b;_.ya(a)?(b=window.document.createElement("div"),b.style.overflow="auto",b.innerHTML=a):a.nodeType==window.Node.TEXT_NODE?(b=window.document.createElement("div"),b.appendChild(a)):b=a;return b};_.Le=function(a){_.Ke&&a&&_.Ke.push(a)};
Me=function(a,b){this.b=a;this.f=b;a.addListener("map_changed",(0,_.p)(this.qm,this));this.bindTo("map",a);this.bindTo("disableAutoPan",a);this.bindTo("maxWidth",a);this.bindTo("position",a);this.bindTo("zIndex",a);this.bindTo("internalAnchor",a,"anchor");this.bindTo("internalContent",a,"content");this.bindTo("internalPixelOffset",a,"pixelOffset")};Ne=function(a,b,c,d){c?a.bindTo(b,c,d):(a.unbind(b),a.set(b,void 0))};
_.Oe=function(a){function b(){e||(e=!0,_.G("infowindow",function(a){a.Tj(d)}))}window.setTimeout(function(){_.G("infowindow",_.ta)},100);a=a||{};var c=!!a.b;delete a.b;var d=new Me(this,c),e=!1;_.y.addListenerOnce(this,"anchor_changed",b);_.y.addListenerOnce(this,"map_changed",b);this.setValues(a)};Pe=function(a){this.setValues(a)};Qe=_.na();Re=_.na();Se=_.na();_.Te=function(){_.G("geocoder",_.ta)};_.Ue=function(a,b,c){this.H=null;this.set("url",a);this.set("bounds",_.md(_.$d)(b));this.setValues(c)};
Ye=function(a,b){_.eb(a)?(this.set("url",a),this.setValues(b)):this.setValues(a)};_.Ze=function(){var a=this;_.G("layers",function(b){b.b(a)})};$e=function(a){this.setValues(a);var b=this;_.G("layers",function(a){a.f(b)})};af=function(){var a=this;_.G("layers",function(b){b.j(a)})};_.bf=function(){this.b=""};_.cf=function(a){var b=new _.bf;b.b=a;return b};_.df=_.na();_.ff=function(){this.Ue="";this.kj=_.ef;this.b=null};_.gf=function(a,b){var c=new _.ff;c.Ue=a;c.b=b;return c};
hf=function(a){this.data=a||[]};jf=function(a){this.data=a||[]};kf=function(a){this.data=a||[]};lf=function(a){this.data=a||[]};mf=function(a){this.data=a||[]};_.nf=function(a){this.data=a||[]};of=function(a){this.data=a||[]};pf=function(a){this.data=a||[]};qf=function(a){this.data=a||[]};_.rf=function(a){return _.L(a,0)};_.sf=function(a){return _.L(a,1)};_.tf=function(a){return new mf(a.data[2])};uf=function(a){this.data=a||[]};vf=function(a){this.data=a||[]};
_.wf=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.xf=function(a){a&&a.parentNode&&a.parentNode.removeChild(a)};_.yf=function(a){this.J=this.I=window.Infinity;this.M=this.L=-window.Infinity;_.v(a||[],this.extend,this)};_.zf=function(a,b,c,d){var e=new _.yf;e.I=a;e.J=b;e.L=c;e.M=d;return e};_.Af=function(a,b){a=a.style;a.width=b.width+b.j;a.height=b.height+b.f};_.Bf=function(a){return new _.O(a.offsetWidth,a.offsetHeight)};_.Cf=function(){this.P=new yd};
Df=function(a,b,c,d,e){this.b=!!b;this.node=null;this.f=0;this.j=!1;this.l=!c;a&&this.setPosition(a,d);this.depth=void 0!=e?e:this.f||0;this.b&&(this.depth*=-1)};_.Ef=function(a){this.ki=a||0;_.y.bind(this,"forceredraw",this,this.C)};Ff=function(a,b,c,d){Df.call(this,a,b,c,null,d)};
Of=function(a,b,c,d,e){var f=_.L(_.tf(_.R),7);this.b=a;this.f=d;this.j=_.m(e)?e:_.Ia();var g=f+"/csi?v=2&s=mapsapi3&v3v="+_.L(new qf(_.R.data[36]),0)+"&action="+a;_.Gb(c,function(a,b){g+="&"+(0,window.encodeURIComponent)(b)+"="+(0,window.encodeURIComponent)(a)});b&&(g+="&e="+b);this.l=g};_.Qf=function(a,b){var c={};c[b]=void 0;_.Pf(a,c)};
_.Pf=function(a,b){var c="";_.Gb(b,function(a,b){var d=(null!=a?a:_.Ia())-this.j;c&&(c+=",");c+=b+"."+Math.round(d);null==a&&window.performance&&window.performance.mark&&window.performance.mark("mapsapi:"+this.b+":"+b)},a);b=a.l+"&rt="+c;a.f.createElement("img").src=b;(a=_.Gc.__gm_captureCSI)&&a(b)};
_.Rf=function(a,b){b=b||{};var c=b.Km||{},d=_.zc(_.R,12).join(",");d&&(c.libraries=d);var d=_.L(_.R,6),e=new hf(_.R.data[33]),f=[];d&&f.push(d);_.v(e.data,function(a,b){a&&_.v(a,function(a,c){null!=a&&f.push(b+1+"_"+(c+1)+"_"+a)})});b.Jk&&(f=f.concat(b.Jk));return new Of(a,f.join(","),c,b.document||window.document,b.startTime)};_.Sf=function(){this.b=new _.N(128,128);this.j=256/360;this.l=256/(2*Math.PI);this.f=!0};
Uf=function(){this.f=_.Rf("apiboot2",{startTime:_.Tf});_.Qf(this.f,"main");this.b=!1};Wf=function(){var a=Vf;a.b||(a.b=!0,_.Qf(a.f,"firstmap"))};_.Xf=function(a,b,c){if(a=a.fromLatLngToPoint(b))c=Math.pow(2,c),a.x*=c,a.y*=c;return a};_.Zf=function(a){for(var b;b=a.firstChild;)_.Yf(b),a.removeChild(b)};_.Yf=function(a){a=new Ff(a);try{for(;;)_.y.clearInstanceListeners(a.next())}catch(b){if(b!==_.$f)throw b;}};
_.ag=function(a,b){var c=a.lat()+_.uc(b);90<c&&(c=90);var d=a.lat()-_.uc(b);-90>d&&(d=-90);b=Math.sin(b);var e=Math.cos(_.tc(a.lat()));if(90==c||-90==d||1E-6>e)return new _.Xd(new _.Q(d,-180),new _.Q(c,180));b=_.uc(Math.asin(b/e));return new _.Xd(new _.Q(d,a.lng()-b),new _.Q(c,a.lng()+b))};
dg=function(a,b){_.me.call(this);_.Le(a);this.__gm=new _.C;this.f=null;b&&b.client&&(this.f=_.bg[b.client]||null);var c=this.controls=[];_.Ua(_.cg,function(a,b){c[b]=new _.be});this.j=!0;this.b=a;this.m=!1;this.__gm.ca=b&&b.ca||new _.vd;this.set("standAlone",!0);this.setPov(new _.xd(0,0,1));b&&b.gd&&!_.x(b.gd.zoom)&&(b.gd.zoom=_.x(b.zoom)?b.zoom:1);this.setValues(b);void 0==this.getVisible()&&this.setVisible(!0);_.y.addListenerOnce(this,"pano_changed",_.hb(function(){_.G("marker",(0,_.p)(function(a){a.b(this.__gm.ca,
this)},this))}))};_.eg=function(){this.l=[];this.j=this.b=this.f=null};fg=function(a,b,c){this.R=b;this.b=_.Od(new _.vc([]));this.B=new _.vd;new _.be;this.D=new _.vd;this.F=new _.vd;this.l=new _.vd;var d=this.ca=new _.vd;d.b=function(){delete d.b;_.G("marker",_.hb(function(b){b.b(d,a)}))};this.j=new dg(c,{visible:!1,enableCloseButton:!0,ca:d});this.j.bindTo("reportErrorControl",a);this.j.j=!1;this.f=new _.eg};gg=function(a){this.data=a||[]};hg=function(a){this.data=a||[]};
ig=function(a){this.data=a||[]};jg=function(a,b,c,d){_.Ef.call(this);this.m=b;this.l=new _.Sf;this.B=c+"/maps/api/js/StaticMapService.GetMapImage";this.f=this.b=null;this.j=d;this.set("div",a);this.set("loading",!0)};lg=function(a){var b=a.get("tilt")||_.w(a.get("styles"));a=a.get("mapTypeId");return b?null:kg[a]};mg=function(a){a.parentNode&&a.parentNode.removeChild(a)};
ng=function(a,b){var c=a.f;c.onload=null;c.onerror=null;a.get("size")&&(b&&(c.parentNode||a.b.appendChild(c),_.Af(c,a.get("size")),_.y.trigger(a,"staticmaploaded"),a.j.set(_.Ia())),a.set("loading",!1))};og=function(a,b){var c=a.f;b!=c.src?(mg(c),c.onload=function(){ng(a,!0)},c.onerror=function(){ng(a,!1)},c.src=b):!c.parentNode&&b&&a.b.appendChild(c)};
sg=function(a,b){var c=_.Ia();Vf&&Wf();var d=new _.Cf,e=b||{};e.noClear||_.Zf(a);var f="undefined"==typeof window.document?null:window.document.createElement("div");f&&a.appendChild&&(a.appendChild(f),f.style.width=f.style.height="100%");_.ze.call(this,new fg(this,a,f));_.m(e.mapTypeId)||(e.mapTypeId="roadmap");this.setValues(e);this.U=_.pg[15]&&e.noControlsOrLogging;this.mapTypes=new se;this.features=new _.C;_.Le(f);this.notify("streetView");a=_.Bf(f);var g=null;_.R&&qg(e.useStaticMap,a)&&(g=new jg(f,
_.rg,_.L(_.tf(_.R),9),new Nd(null)),_.y.forward(g,"staticmaploaded",this),g.set("size",a),g.bindTo("center",this),g.bindTo("zoom",this),g.bindTo("mapTypeId",this),g.bindTo("styles",this));this.overlayMapTypes=new _.be;var h=this.controls=[];_.Ua(_.cg,function(a,b){h[b]=new _.be});var l=this,n=!0;_.G("map",function(a){l.getDiv()&&f&&a.f(l,e,f,g,n,c,d)});n=!1;this.data=new He({map:this})};qg=function(a,b){if(_.m(a))return!!a;a=b.width;b=b.height;return 384E3>=a*b&&800>=a&&800>=b};
tg=function(){_.G("maxzoom",_.ta)};ug=function(a,b){!a||_.eb(a)||_.x(a)?(this.set("tableId",a),this.setValues(b)):this.setValues(a)};_.vg=_.na();_.wg=function(a){this.setValues(te(a));_.G("poly",_.ta)};_.xg=function(a){this.setValues(te(a));_.G("poly",_.ta)};yg=function(){this.b=null};_.zg=function(){this.b=null};Ag=function(a,b){this.b=a;this.f=b||0};
Dg=function(){var a=window.navigator.userAgent;this.l=a;this.b=this.type=0;this.version=new Ag(0);this.m=new Ag(0);for(var a=a.toLowerCase(),b=1;8>b;++b){var c=Bg[b];if(-1!=a.indexOf(c)){this.type=b;var d=(new RegExp(c+"[ /]?([0-9]+).?([0-9]+)?")).exec(a);d&&(this.version=new Ag((0,window.parseInt)(d[1],10),(0,window.parseInt)(d[2]||"0",10)));break}}7==this.type&&(b=/^Mozilla\/.*Gecko\/.*[Minefield|Shiretoko][ /]?([0-9]+).?([0-9]+)?/,d=b.exec(this.l))&&(this.type=5,this.version=new Ag((0,window.parseInt)(d[1],
10),(0,window.parseInt)(d[2]||"0",10)));6==this.type&&(b=/rv:([0-9]{2,}.?[0-9]+)/,b=b.exec(this.l))&&(this.type=1,this.version=new Ag((0,window.parseInt)(b[1],10)));for(b=1;7>b;++b)if(c=Cg[b],-1!=a.indexOf(c)){this.b=b;break}if(5==this.b||6==this.b||2==this.b)if(b=/OS (?:X )?(\d+)[_.]?(\d+)/.exec(this.l))this.m=new Ag((0,window.parseInt)(b[1],10),(0,window.parseInt)(b[2]||"0",10));4==this.b&&(b=/Android (\d+)\.?(\d+)?/.exec(this.l))&&(this.m=new Ag((0,window.parseInt)(b[1],10),(0,window.parseInt)(b[2]||
"0",10)));this.j=5==this.type||7==this.type;this.f=4==this.type||3==this.type;this.D=0;this.j&&(d=/\brv:\s*(\d+\.\d+)/.exec(a))&&(this.D=(0,window.parseFloat)(d[1]));this.B=window.document.compatMode||"";this.C=1==this.b||2==this.b||3==this.b&&-1==a.toLowerCase().indexOf("mobile")};Eg=_.oa("b");
Kg=function(){var a=window.document;this.f=_.S;this.b=Jg(a,["transform","WebkitTransform","MozTransform","msTransform"]);this.C=Jg(a,["WebkitUserSelect","MozUserSelect","msUserSelect"]);this.m=Jg(a,["transition","WebkitTransition","MozTransition","OTransition","msTransition"]);var b;a:{for(var c=["-webkit-linear-gradient","-moz-linear-gradient","-o-linear-gradient","-ms-linear-gradient"],d=a.createElement("div"),e=0,f;f=c[e];++e)try{if(d.style.background=f+"(left, #000, #fff)",-1!=d.style.background.indexOf(f)){b=
f;break a}}catch(g){}b=null}this.B=b;this.l="string"==typeof a.documentElement.style.opacity;a=window.document.getElementsByTagName("script")[0];b=a.style.color;a.style.color="";try{a.style.color="rgba(0, 0, 0, 0.5)"}catch(g){}c=a.style.color!=b;a.style.color=b;this.j=c};Jg=function(a,b){for(var c=0,d;d=b[c];++c)if("string"==typeof a.documentElement.style[d])return d;return null};_.Lg=function(a,b){this.size=new Cb;this.b=a;this.heading=b};
_.Mg=function(a){this.tileSize=a.tileSize||new _.O(256,256);this.name=a.name;this.alt=a.alt;this.minZoom=a.minZoom;this.maxZoom=a.maxZoom;this.j=(0,_.p)(a.getTileUrl,a);this.b=new _.vd;this.f=null;this.set("opacity",a.opacity);var b=this;_.G("map",function(a){var c=b.f=a.b,e=b.tileSize||new _.O(256,256);b.b.forEach(function(a){var d=a.__gmimt,f=d.Y,l=d.zoom,n=b.j(f,l);d.Ib=c(f,l,e,a,n,function(){_.y.trigger(a,"load")})})})};
Ng=function(a,b){null!=a.style.opacity?a.style.opacity=b:a.style.filter=b&&"alpha(opacity="+Math.round(100*b)+")"};Og=function(a){a=a.get("opacity");return"number"==typeof a?a:1};_.Pg=function(){_.Pg.Ee(this,"constructor")};_.Qg=function(a,b){_.Qg.Ee(this,"constructor");this.set("styles",a);a=b||{};this.f=a.baseMapTypeId||"roadmap";this.minZoom=a.minZoom;this.maxZoom=a.maxZoom||20;this.name=a.name;this.alt=a.alt;this.projection=null;this.tileSize=new _.O(256,256)};
_.Rg=function(a,b){_.jd(fd,"container is not a Node")(a);this.setValues(b);_.G("controls",(0,_.p)(function(b){b.Fl(this,a)},this))};Sg=_.oa("b");Tg=function(a,b,c){for(var d=Array(b.length),e=0,f=b.length;e<f;++e)d[e]=b.charCodeAt(e);d.unshift(c);a=a.b;c=b=0;for(e=d.length;c<e;++c)b*=1729,b+=d[c],b%=a;return b};
Wg=function(){var a=_.K(new of(_.R.data[4]),0),b=new Sg(131071),c=(0,window.unescape)("%26%74%6F%6B%65%6E%3D");return function(d){d=d.replace(Ug,"%27");var e=d+c;Vg||(Vg=/(?:https?:\/\/[^/]+)?(.*)/);d=Vg.exec(d);return e+Tg(b,d&&d[1],a)}};Xg=function(){var a=new Sg(2147483647);return function(b){return Tg(a,b,0)}};Yg=function(a){for(var b=a.split("."),c=window,d=window,e=0;e<b.length;e++)if(d=c,c=c[b[e]],!c)throw _.Yc(a+" is not a function");return function(){c.apply(d)}};
Zg=function(){for(var a in Object.prototype)window.console&&window.console.error("This site adds property <"+a+"> to Object.prototype. Extending Object.prototype breaks JavaScript for..in loops, which are used heavily in Google Maps API v3.")};$g=function(a){(a="version"in a)&&window.console&&window.console.error("You have included the Google Maps API multiple times on this page. This may cause unexpected errors.");return a};_.ra=[];
ah="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(c.get||c.set)throw new TypeError("ES3 does not support getters and setters.");a!=Array.prototype&&a!=Object.prototype&&(a[b]=c.value)};bh="undefined"!=typeof window&&window===this?this:"undefined"!=typeof window.global&&null!=window.global?window.global:this;ch=["Array","prototype","fill"];dh=0;for(;dh<ch.length-1;dh++){var eh=ch[dh];eh in bh||(bh[eh]={});bh=bh[eh]}
var fh=ch[ch.length-1],gh=bh[fh],hh=gh?gh:function(a,b,c){var d=this.length||0;0>b&&(b=Math.max(0,d+b));if(null==c||c>d)c=d;c=Number(c);0>c&&(c=Math.max(0,d+c));for(b=Number(b||0);b<c;b++)this[b]=a;return this};hh!=gh&&null!=hh&&ah(bh,fh,{configurable:!0,writable:!0,value:hh});_.Gc=this;Da="closure_uid_"+(1E9*Math.random()>>>0);Ea=0;var tb,ub;_.y={};tb="undefined"!=typeof window.navigator&&-1!=window.navigator.userAgent.toLowerCase().indexOf("msie");ub={};_.y.addListener=function(a,b,c){return new vb(a,b,c,0)};_.y.hasListeners=function(a,b){b=(a=a.__e3_)&&a[b];return!!b&&!_.Xa(b)};_.y.removeListener=function(a){a&&a.remove()};_.y.clearListeners=function(a,b){_.Ua(pb(a,b),function(a,b){b&&b.remove()})};_.y.clearInstanceListeners=function(a){_.Ua(pb(a),function(a,c){c&&c.remove()})};
_.y.trigger=function(a,b,c){if(_.y.hasListeners(a,b)){var d=_.Sa(arguments,2),e=pb(a,b),f;for(f in e){var g=e[f];g&&g.b.apply(g.f,d)}}};_.y.addDomListener=function(a,b,c,d){if(a.addEventListener){var e=d?4:1;a.addEventListener(b,c,d);c=new vb(a,b,c,e)}else a.attachEvent?(c=new vb(a,b,c,2),a.attachEvent("on"+b,wb(c))):(a["on"+b]=c,c=new vb(a,b,c,3));return c};_.y.addDomListenerOnce=function(a,b,c,d){var e=_.y.addDomListener(a,b,function(){e.remove();return c.apply(this,arguments)},d);return e};
_.y.T=function(a,b,c,d){return _.y.addDomListener(a,b,qb(c,d))};_.y.bind=function(a,b,c,d){return _.y.addListener(a,b,(0,_.p)(d,c))};_.y.addListenerOnce=function(a,b,c){var d=_.y.addListener(a,b,function(){d.remove();return c.apply(this,arguments)});return d};_.y.forward=function(a,b,c){return _.y.addListener(a,b,rb(b,c))};_.y.Ga=function(a,b,c,d){return _.y.addDomListener(a,b,rb(b,c,!d))};_.y.Xh=function(){var a=ub,b;for(b in a)a[b].remove();ub={};(a=_.Gc.CollectGarbage)&&a()};
_.y.Ym=function(){tb&&_.y.addDomListener(window,"unload",_.y.Xh)};var sb=0;vb.prototype.remove=function(){if(this.f){switch(this.m){case 1:this.f.removeEventListener(this.j,this.b,!1);break;case 4:this.f.removeEventListener(this.j,this.b,!0);break;case 2:this.f.detachEvent("on"+this.j,this.l);break;case 3:this.f["on"+this.j]=null}delete ob(this.f,this.j)[this.id];this.l=this.b=this.f=null;delete ub[this.id]}};_.k=_.C.prototype;_.k.get=function(a){var b=Bb(this);a+="";b=ib(b,a);if(_.m(b)){if(b){a=b.Ya;var b=b.Ac,c="get"+_.Ab(a);return b[c]?b[c]():b.get(a)}return this[a]}};_.k.set=function(a,b){var c=Bb(this);a+="";var d=ib(c,a);if(d)if(a=d.Ya,d=d.Ac,c="set"+_.Ab(a),d[c])d[c](b);else d.set(a,b);else this[a]=b,c[a]=null,yb(this,a)};_.k.notify=function(a){var b=Bb(this);a+="";(b=ib(b,a))?b.Ac.notify(b.Ya):yb(this,a)};
_.k.setValues=function(a){for(var b in a){var c=a[b],d="set"+_.Ab(b);if(this[d])this[d](c);else this.set(b,c)}};_.k.setOptions=_.C.prototype.setValues;_.k.changed=_.na();var zb={};_.C.prototype.bindTo=function(a,b,c,d){a+="";c=(c||a)+"";this.unbind(a);var e={Ac:this,Ya:a},f={Ac:b,Ya:c,jg:e};Bb(this)[a]=f;xb(b,c)[_.Ta(e)]=e;d||yb(this,a)};_.C.prototype.unbind=function(a){var b=Bb(this),c=b[a];c&&(c.jg&&delete xb(c.Ac,c.Ya)[_.Ta(c.jg)],this[a]=this.get(a),b[a]=null)};
_.C.prototype.unbindAll=function(){var a=(0,_.p)(this.unbind,this),b=Bb(this),c;for(c in b)a(c)};_.C.prototype.addListener=function(a,b){return _.y.addListener(this,a,b)};_.ih={ROADMAP:"roadmap",SATELLITE:"satellite",HYBRID:"hybrid",TERRAIN:"terrain"};_.cg={TOP_LEFT:1,TOP_CENTER:2,TOP:2,TOP_RIGHT:3,LEFT_CENTER:4,LEFT_TOP:5,LEFT:5,LEFT_BOTTOM:6,RIGHT_TOP:7,RIGHT:7,RIGHT_CENTER:8,RIGHT_BOTTOM:9,BOTTOM_LEFT:10,BOTTOM_CENTER:11,BOTTOM:11,BOTTOM_RIGHT:12,CENTER:13};Db.prototype.get=function(){var a;0<this.f?(this.f--,a=this.b,this.b=a.next,a.next=null):a=this.j();return a};var jh=function(a){return function(){return a}}(null);_.Ib[" "]=_.ta;var kh={xo:"Point",vo:"LineString",POLYGON:"Polygon"};_.Lb.prototype.heading=_.pa("f");_.Lb.prototype.b=_.pa("j");_.Lb.prototype.toString=function(){return this.f+","+this.j};_.lh=new _.Lb;var mh={CIRCLE:0,FORWARD_CLOSED_ARROW:1,FORWARD_OPEN_ARROW:2,BACKWARD_CLOSED_ARROW:3,BACKWARD_OPEN_ARROW:4};a:{var nh=_.Gc.navigator;if(nh){var oh=nh.userAgent;if(oh){_.Ka=oh;break a}}_.Ka=""};var ph=new Db(function(){return new Sb},function(a){a.reset()},100);Rb.prototype.add=function(a,b){var c=ph.get();c.set(a,b);this.f?this.f.next=c:this.b=c;this.f=c};Rb.prototype.remove=function(){var a=null;this.b&&(a=this.b,this.b=this.b.next,this.b||(this.f=null),a.next=null);return a};Sb.prototype.set=function(a,b){this.xc=a;this.b=b;this.next=null};Sb.prototype.reset=function(){this.next=this.b=this.xc=null};Vb.f=void 0;Vb.b=function(){return Vb.f?Vb.f:Vb.f=new Vb};Vb.prototype.cb=function(a,b){var c=this,d=c.m;Wb(c.j,function(e){for(var f=e.fh[a]||[],g=e.gn[a]||[],h=d[a]=_.Mb(f.length,function(){delete d[a];b(e.nk);for(var f=c.f[a],h=f?f.length:0,l=0;l<h;++l)f[l](c.b[a]);delete c.f[a];l=0;for(f=g.length;l<f;++l)h=g[l],d[h]&&d[h]()}),l=0,n=f.length;l<n;++l)c.b[f[l]]&&h()})};_.qh=_.hc("d",void 0);_.rh=_.hc("f",void 0);_.T=_.jc();_.sh=_.ic("i",void 0);_.th=new _.fc("i",3,void 0,void 0);_.uh=new _.fc("j",3,"",void 0);_.vh=_.hc("u",void 0);_.wh=_.ic("u",void 0);_.xh=new _.fc("u",3,void 0,void 0);_.yh=_.nc();_.U=_.oc();_.V=_.pc();_.zh=new _.fc("e",3,void 0,void 0);_.W=_.hc("s",void 0);_.Ah=_.ic("s",void 0);_.Bh=new _.fc("s",3,void 0,void 0);_.Ch=_.hc("x",void 0);_.Dh=_.ic("x",void 0);_.Eh=new _.fc("x",3,void 0,void 0);_.Fh=new _.fc("y",3,void 0,void 0);_.vc.prototype.Pa=_.sa(0);_.vc.prototype.forEach=function(a,b){_.v(this.b,function(c,d){a.call(b,c,d)})};_.I.prototype.Uh=_.sa(1);var Kc,Jc=_.Eb;var Sh,Jb;_.Gh=_.Pb("Opera");_.Hh=_.qc();_.Ih=_.Pb("Edge");_.Jh=_.Pb("Gecko")&&!(_.La()&&!_.Pb("Edge"))&&!(_.Pb("Trident")||_.Pb("MSIE"))&&!_.Pb("Edge");_.Kh=_.La()&&!_.Pb("Edge");_.Lh=_.Pb("Macintosh");_.Mh=_.Pb("Windows");_.Nh=_.Pb("Linux")||_.Pb("CrOS");_.Oh=_.Pb("Android");_.Ph=_.Tb();_.Qh=_.Pb("iPad");_.Rh=_.Pb("iPod");
a:{var Th="",Uh=function(){var a=_.Ka;if(_.Jh)return/rv\:([^\);]+)(\)|;)/.exec(a);if(_.Ih)return/Edge\/([\d\.]+)/.exec(a);if(_.Hh)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Kh)return/WebKit\/(\S+)/.exec(a);if(_.Gh)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Uh&&(Th=Uh?Uh[1]:"");if(_.Hh){var Vh=Nc();if(null!=Vh&&Vh>(0,window.parseFloat)(Th)){Sh=String(Vh);break a}}Sh=Th}_.Oc=Sh;Jb={};var Xh=_.Gc.document;_.Wh=Xh&&_.Hh?Nc()||("CSS1Compat"==Xh.compatMode?(0,window.parseInt)(_.Oc,10):5):void 0;_.k=Qc.prototype;_.k.isEmpty=function(){return 360==this.b-this.f};_.k.intersects=function(a){var b=this.b,c=this.f;return this.isEmpty()||a.isEmpty()?!1:_.Rc(this)?_.Rc(a)||a.b<=this.f||a.f>=b:_.Rc(a)?a.b<=c||a.f>=b:a.b<=c&&a.f>=b};_.k.contains=function(a){-180==a&&(a=180);var b=this.b,c=this.f;return _.Rc(this)?(a>=b||a<=c)&&!this.isEmpty():a>=b&&a<=c};_.k.extend=function(a){this.contains(a)||(this.isEmpty()?this.b=this.f=a:_.Uc(a,this.b)<_.Uc(this.f,a)?this.b=a:this.f=a)};
_.k.ub=function(){var a=(this.b+this.f)/2;_.Rc(this)&&(a=_.Za(a+180,-180,180));return a};_.k=Vc.prototype;_.k.isEmpty=function(){return this.f>this.b};_.k.intersects=function(a){var b=this.f,c=this.b;return b<=a.f?a.f<=c&&a.f<=a.b:b<=a.b&&b<=c};_.k.contains=function(a){return a>=this.f&&a<=this.b};_.k.extend=function(a){this.isEmpty()?this.b=this.f=a:a<this.f?this.f=a:a>this.b&&(this.b=a)};_.k.ub=function(){return(this.b+this.f)/2};_.t(Xc,Error);var Zh;_.Yh=new $c;Zh=/'/g;$c.prototype.b=function(a,b){var c=[];bd(a,b,c);return c.join("&").replace(Zh,"%27")};_.Ic.m=function(){if(-1!=String(_.Gc.Promise).indexOf("[native code]")){var a=_.Gc.Promise.resolve(void 0);_.Ic.b=function(){a.then(_.Ic.f)}}else _.Ic.b=function(){Mc()}};_.Ic.B=function(a){_.Ic.b=function(){Mc();a&&a(_.Ic.f)}};_.Ic.j=!1;_.Ic.l=new Rb;_.Ic.f=function(){for(var a;a=_.Ic.l.remove();){try{a.xc.call(a.b)}catch(c){Hc(c)}var b=ph;b.m(a);b.f<b.l&&(b.f++,a.next=b.b,b.b=a)}_.Ic.j=!1};_.$h=_.Pb("Firefox");_.ai=_.Tb()||_.Pb("iPod");_.bi=_.Pb("iPad");_.ci=_.Pb("Android")&&!(rc()||_.Pb("Firefox")||_.Pb("Opera")||_.Pb("Silk"));_.di=rc();_.ei=_.sc()&&!(_.Tb()||_.Pb("iPad")||_.Pb("iPod"));var Yb={main:[],common:["main"],util:["common"],adsense:["main"],controls:["util"],data:["util"],directions:["util","geometry"],distance_matrix:["util"],drawing:["main"],drawing_impl:["controls"],elevation:["util","geometry"],geocoder:["util"],geojson:["main"],imagery_viewer:["main"],geometry:["main"],infowindow:["util"],kml:["onion","util","map"],layers:["map"],map:["common"],marker:["util"],maxzoom:["util"],onion:["util","map"],overlay:["common"],panoramio:["main"],places:["main"],places_impl:["controls"],
poly:["util","map","geometry"],search:["main"],search_impl:["onion"],stats:["util"],streetview:["util","geometry"],usage:["util"],visualization:["main"],visualization_impl:["onion"],weather:["main"],zombie:["main"]};var fi,hi;_.od=_.jd(_.x,"not a number");fi=_.ld(_.od,function(a){if((0,window.isNaN)(a))throw _.Yc("NaN is not an accepted value");return a});_.gi=_.jd(_.eb,"not a string");hi=_.jd(_.fb,"not a boolean");_.ii=_.md(_.od);_.ji=_.md(_.gi);_.ki=_.md(hi);var Cd=_.cd({lat:_.od,lng:_.od},!0);_.li=new _.N(0,0);_.N.prototype.toString=function(){return"("+this.x+", "+this.y+")"};_.N.prototype.b=function(a){return a?a.x==this.x&&a.y==this.y:!1};_.N.prototype.equals=_.N.prototype.b;_.N.prototype.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y)};_.N.prototype.Jd=_.sa(2);_.mi=new _.O(0,0);_.O.prototype.toString=function(){return"("+this.width+", "+this.height+")"};_.O.prototype.b=function(a){return a?a.width==this.width&&a.height==this.height:!1};_.O.prototype.equals=_.O.prototype.b;var ni=_.Gc.google.maps,oi=Vb.b(),pi=(0,_.p)(oi.cb,oi);ni.__gjsload__=pi;_.Ua(ni.modules,pi);delete ni.modules;_.vd.prototype.remove=function(a){var b=this.f,c=this.j(a);b[c]&&(delete b[c],_.y.trigger(this,"remove",a),this.onRemove&&this.onRemove(a))};_.vd.prototype.contains=function(a){return!!this.f[this.j(a)]};_.vd.prototype.forEach=function(a){var b=this.f,c;for(c in b)a.call(this,b[c])};var qi=_.cd({zoom:_.md(fi),heading:fi,pitch:fi});var ri=_.cd({source:_.gi,webUrl:_.ji,iosDeepLinkId:_.ji});yd.prototype.addListener=function(a,b,c){c=c?{lg:!1}:null;var d=!this.P.length,e;e=this.P;var f=Pa(e,Bd(a,b));(e=0>f?null:_.ya(e)?e.charAt(f):e[f])?e.once=e.once&&c:this.P.push({xc:a,context:b||null,once:c});d&&this.f();return a};yd.prototype.addListenerOnce=function(a,b){this.addListener(a,b,!0);return a};yd.prototype.removeListener=function(a,b){if(this.P.length){var c=this.P;a=Pa(c,Bd(a,b));0<=a&&_.Qa(c,a);this.P.length||this.b()}};var zd=_.Ic;_.Q.prototype.toString=function(){return"("+this.lat()+", "+this.lng()+")"};_.Q.prototype.toJSON=function(){return{lat:this.lat(),lng:this.lng()}};_.Q.prototype.b=function(a){return a?_.$a(this.lat(),a.lat())&&_.$a(this.lng(),a.lng()):!1};_.Q.prototype.equals=_.Q.prototype.b;_.Q.prototype.toUrlValue=function(a){a=_.m(a)?a:6;return Fd(this.lat(),a)+","+Fd(this.lng(),a)};_.k=_.Gd.prototype;_.k.cd=_.na();_.k.bd=_.na();_.k.addListener=function(a,b){return this.P.addListener(a,b)};_.k.addListenerOnce=function(a,b){return this.P.addListenerOnce(a,b)};_.k.removeListener=function(a,b){return this.P.removeListener(a,b)};_.k.notify=function(a){_.Ad(this.P,function(a){a(this.get())},this,a)};_.Pd=_.id(_.Id);_.t(_.Md,_.Gd);_.Md.prototype.set=function(a){this.Ih(a);this.notify()};_.t(Nd,_.Md);Nd.prototype.get=_.pa("b");Nd.prototype.Ih=_.oa("b");_.t(_.Td,Hd);_.k=_.Td.prototype;_.k.getType=_.qa("LineString");_.k.getLength=function(){return this.b.length};_.k.getAt=function(a){return this.b[a]};_.k.getArray=function(){return this.b.slice()};_.k.forEachLatLng=function(a){this.b.forEach(a)};var de=_.id(_.gd(_.Td,"google.maps.Data.LineString",!0));_.t(_.Ud,Hd);_.k=_.Ud.prototype;_.k.getType=_.qa("LinearRing");_.k.getLength=function(){return this.b.length};_.k.getAt=function(a){return this.b[a]};_.k.getArray=function(){return this.b.slice()};_.k.forEachLatLng=function(a){this.b.forEach(a)};var fe=_.id(_.gd(_.Ud,"google.maps.Data.LinearRing",!0));_.t(_.Vd,Hd);_.k=_.Vd.prototype;_.k.getType=_.qa("MultiPoint");_.k.getLength=function(){return this.b.length};_.k.getAt=function(a){return this.b[a]};_.k.getArray=function(){return this.b.slice()};_.k.forEachLatLng=function(a){this.b.forEach(a)};_.t(_.Wd,Hd);_.Wd.prototype.getType=_.qa("Point");_.Wd.prototype.forEachLatLng=function(a){a(this.b)};_.Wd.prototype.get=_.pa("b");_.k=_.Xd.prototype;_.k.getCenter=function(){return new _.Q(this.f.ub(),this.b.ub())};_.k.toString=function(){return"("+this.getSouthWest()+", "+this.getNorthEast()+")"};_.k.toJSON=function(){return{south:this.f.f,west:this.b.b,north:this.f.b,east:this.b.f}};_.k.toUrlValue=function(a){var b=this.getSouthWest(),c=this.getNorthEast();return[b.toUrlValue(a),c.toUrlValue(a)].join()};
_.k.Ei=function(a){if(!a)return!1;a=_.$d(a);var b=this.f,c=a.f;return(b.isEmpty()?c.isEmpty():1E-9>=Math.abs(c.f-b.f)+Math.abs(b.b-c.b))&&_.Tc(this.b,a.b)};_.Xd.prototype.equals=_.Xd.prototype.Ei;_.k=_.Xd.prototype;_.k.contains=function(a){a=_.Id(a);return this.f.contains(a.lat())&&this.b.contains(a.lng())};_.k.intersects=function(a){a=_.$d(a);return this.f.intersects(a.f)&&this.b.intersects(a.b)};_.k.extend=function(a){a=_.Id(a);this.f.extend(a.lat());this.b.extend(a.lng());return this};
_.k.union=function(a){a=_.$d(a);if(!a||a.isEmpty())return this;this.extend(a.getSouthWest());this.extend(a.getNorthEast());return this};_.k.getSouthWest=function(){return new _.Q(this.f.f,this.b.b,!0)};_.k.getNorthEast=function(){return new _.Q(this.f.b,this.b.f,!0)};_.k.toSpan=function(){return new _.Q(_.Wc(this.f),_.Sc(this.b),!0)};_.k.isEmpty=function(){return this.f.isEmpty()||this.b.isEmpty()};var Zd=_.cd({south:_.od,west:_.od,north:_.od,east:_.od},!1);_.t(_.be,_.C);_.k=_.be.prototype;_.k.getAt=function(a){return this.b[a]};_.k.indexOf=function(a){for(var b=0,c=this.b.length;b<c;++b)if(a===this.b[b])return b;return-1};_.k.forEach=function(a){for(var b=0,c=this.b.length;b<c;++b)a(this.b[b],b)};_.k.setAt=function(a,b){var c=this.b[a],d=this.b.length;if(a<d)this.b[a]=b,_.y.trigger(this,"set_at",a,c),this.l&&this.l(a,c);else{for(c=d;c<a;++c)this.insertAt(c,void 0);this.insertAt(a,b)}};
_.k.insertAt=function(a,b){this.b.splice(a,0,b);ae(this);_.y.trigger(this,"insert_at",a);this.f&&this.f(a)};_.k.removeAt=function(a){var b=this.b[a];this.b.splice(a,1);ae(this);_.y.trigger(this,"remove_at",a,b);this.j&&this.j(a,b);return b};_.k.push=function(a){this.insertAt(this.b.length,a);return this.b.length};_.k.pop=function(){return this.removeAt(this.b.length-1)};_.k.getArray=_.pa("b");_.k.clear=function(){for(;this.get("length");)this.pop()};_.Ld(_.be.prototype,{length:null});var Ci=_.ld(_.cd({placeId:_.ji,query:_.ji,location:_.Id}),function(a){if(a.placeId&&a.query)throw _.Yc("cannot set both placeId and query");if(!a.placeId&&!a.query)throw _.Yc("must set one of placeId or query");return a});var ie=_.id(ce);_.t(_.ee,Hd);_.k=_.ee.prototype;_.k.getType=_.qa("MultiLineString");_.k.getLength=function(){return this.b.length};_.k.getAt=function(a){return this.b[a]};_.k.getArray=function(){return this.b.slice()};_.k.forEachLatLng=function(a){this.b.forEach(function(b){b.forEachLatLng(a)})};_.t(_.ge,Hd);_.k=_.ge.prototype;_.k.getType=_.qa("Polygon");_.k.getLength=function(){return this.b.length};_.k.getAt=function(a){return this.b[a]};_.k.getArray=function(){return this.b.slice()};_.k.forEachLatLng=function(a){this.b.forEach(function(b){b.forEachLatLng(a)})};var ke=_.id(_.gd(_.ge,"google.maps.Data.Polygon",!0));_.k=_.he.prototype;_.k.getId=_.pa("j");_.k.getGeometry=_.pa("b");_.k.setGeometry=function(a){var b=this.b;try{this.b=a?ce(a):null}catch(c){_.Zc(c);return}_.y.trigger(this,"setgeometry",{feature:this,newGeometry:this.b,oldGeometry:b})};_.k.getProperty=function(a){return ib(this.f,a)};_.k.setProperty=function(a,b){if(void 0===b)this.removeProperty(a);else{var c=this.getProperty(a);this.f[a]=b;_.y.trigger(this,"setproperty",{feature:this,name:a,newValue:b,oldValue:c})}};
_.k.removeProperty=function(a){var b=this.getProperty(a);delete this.f[a];_.y.trigger(this,"removeproperty",{feature:this,name:a,oldValue:b})};_.k.forEachProperty=function(a){for(var b in this.f)a(this.getProperty(b),b)};_.k.toGeoJson=function(a){var b=this;_.G("data",function(c){c.f(b,a)})};_.t(_.je,Hd);_.k=_.je.prototype;_.k.getType=_.qa("GeometryCollection");_.k.getLength=function(){return this.b.length};_.k.getAt=function(a){return this.b[a]};_.k.getArray=function(){return this.b.slice()};_.k.forEachLatLng=function(a){this.b.forEach(function(b){b.forEachLatLng(a)})};_.t(_.le,Hd);_.k=_.le.prototype;_.k.getType=_.qa("MultiPolygon");_.k.getLength=function(){return this.b.length};_.k.getAt=function(a){return this.b[a]};_.k.getArray=function(){return this.b.slice()};_.k.forEachLatLng=function(a){this.b.forEach(function(b){b.forEachLatLng(a)})};_.t(_.me,_.C);ne.prototype.get=function(a){return this.b[a]};ne.prototype.set=function(a,b){var c=this.b;c[a]||(c[a]={});_.Wa(c[a],b);_.y.trigger(this,"changed",a)};ne.prototype.reset=function(a){delete this.b[a];_.y.trigger(this,"changed",a)};ne.prototype.forEach=function(a){_.Ua(this.b,a)};_.t(oe,_.C);var Di=_.md(_.gd(_.me,"StreetViewPanorama"));_.k=qe.prototype;_.k.contains=function(a){return this.b.hasOwnProperty(_.Ta(a))};_.k.getFeatureById=function(a){return ib(this.f,a)};
_.k.add=function(a){a=a||{};a=a instanceof _.he?a:new _.he(a);if(!this.contains(a)){var b=a.getId();if(b){var c=this.getFeatureById(b);c&&this.remove(c)}c=_.Ta(a);this.b[c]=a;b&&(this.f[b]=a);var d=_.y.forward(a,"setgeometry",this),e=_.y.forward(a,"setproperty",this),f=_.y.forward(a,"removeproperty",this);this.j[c]=function(){_.y.removeListener(d);_.y.removeListener(e);_.y.removeListener(f)};_.y.trigger(this,"addfeature",{feature:a})}return a};
_.k.remove=function(a){var b=_.Ta(a),c=a.getId();if(this.b[b]){delete this.b[b];c&&delete this.f[c];if(c=this.j[b])delete this.j[b],c();_.y.trigger(this,"removefeature",{feature:a})}};_.k.forEach=function(a){for(var b in this.b)a(this.b[b])};_.t(se,_.C);se.prototype.set=function(a,b){if(null!=b&&!(b&&_.x(b.maxZoom)&&b.tileSize&&b.tileSize.width&&b.tileSize.height&&b.getTile&&b.getTile.apply))throw Error("Expected value implementing google.maps.MapType");return _.C.prototype.set.apply(this,arguments)};var ve=xe(_.gd(_.Q,"LatLng"));_.t(_.ze,_.C);_.t(Ae,_.C);Ae.prototype.overrideStyle=function(a,b){this.b.set(_.Ta(a),b)};Ae.prototype.revertStyle=function(a){a?this.b.reset(_.Ta(a)):this.b.forEach((0,_.p)(this.b.reset,this.b))};_.Ei=_.md(_.gd(_.ze,"Map"));_.t(Be,_.C);
_.Ld(Be.prototype,{position:_.md(_.Id),title:_.ji,icon:_.md(_.kd([_.gi,{If:nd("url"),then:_.cd({url:_.gi,scaledSize:_.md(qd),size:_.md(qd),origin:_.md(pd),anchor:_.md(pd),labelOrigin:_.md(pd),path:_.jd(function(a){return null==a})},!0)},{If:nd("path"),then:_.cd({path:_.kd([_.gi,_.hd(mh)]),anchor:_.md(pd),labelOrigin:_.md(pd),fillColor:_.ji,fillOpacity:_.ii,rotation:_.ii,scale:_.ii,strokeColor:_.ji,strokeOpacity:_.ii,strokeWeight:_.ii,url:_.jd(function(a){return null==a})},!0)}])),label:_.md(_.kd([_.gi,{If:nd("text"),
then:_.cd({text:_.gi,fontSize:_.ji,fontWeight:_.ji,fontFamily:_.ji},!0)}])),shadow:_.Eb,shape:_.Eb,cursor:_.ji,clickable:_.ki,animation:_.Eb,draggable:_.ki,visible:_.ki,flat:_.Eb,zIndex:_.ii,opacity:_.ii,place:_.md(Ci),attribution:_.md(ri)});_.t(Ce,_.C);Ce.prototype.map_changed=Ce.prototype.visible_changed=function(){var a=this;_.G("poly",function(b){b.f(a)})};Ce.prototype.getPath=function(){return this.get("latLngs").getAt(0)};Ce.prototype.setPath=function(a){try{this.get("latLngs").setAt(0,we(a))}catch(b){_.Zc(b)}};_.Ld(Ce.prototype,{draggable:_.ki,editable:_.ki,map:_.Ei,visible:_.ki});_.t(_.De,Be);_.De.prototype.map_changed=function(){this.__gm.set&&this.__gm.set.remove(this);var a=this.get("map");this.__gm.set=a&&a.__gm.ca;this.__gm.set&&_.wd(this.__gm.set,this)};_.De.MAX_ZINDEX=1E6;_.Ld(_.De.prototype,{map:_.kd([_.Ei,Di])});_.t(_.Ee,Ce);_.Ee.prototype.Aa=!0;_.Ee.prototype.getPaths=function(){return this.get("latLngs")};_.Ee.prototype.setPaths=function(a){this.set("latLngs",ye(a))};_.t(_.Fe,Ce);_.Fe.prototype.Aa=!1;_.Ge="click dblclick mousedown mousemove mouseout mouseover mouseup rightclick".split(" ");_.t(He,_.C);_.k=He.prototype;_.k.contains=function(a){return this.b.contains(a)};_.k.getFeatureById=function(a){return this.b.getFeatureById(a)};_.k.add=function(a){return this.b.add(a)};_.k.remove=function(a){this.b.remove(a)};_.k.forEach=function(a){this.b.forEach(a)};_.k.addGeoJson=function(a,b){return _.re(this.b,a,b)};_.k.loadGeoJson=function(a,b,c){var d=this.b;_.G("data",function(e){e.Mk(d,a,b,c)})};_.k.toGeoJson=function(a){var b=this.b;_.G("data",function(c){c.Ik(b,a)})};
_.k.overrideStyle=function(a,b){this.f.overrideStyle(a,b)};_.k.revertStyle=function(a){this.f.revertStyle(a)};_.k.controls_changed=function(){this.get("controls")&&Ie(this)};_.k.drawingMode_changed=function(){this.get("drawingMode")&&Ie(this)};_.Ld(He.prototype,{map:_.Ei,style:_.Eb,controls:_.md(_.id(_.hd(kh))),controlPosition:_.md(_.hd(_.cg)),drawingMode:_.md(_.hd(kh))});_.Fi={METRIC:0,IMPERIAL:1};_.Gi={DRIVING:"DRIVING",WALKING:"WALKING",BICYCLING:"BICYCLING",TRANSIT:"TRANSIT"};_.Hi={BEST_GUESS:"bestguess",OPTIMISTIC:"optimistic",PESSIMISTIC:"pessimistic"};_.Ii={BUS:"BUS",RAIL:"RAIL",SUBWAY:"SUBWAY",TRAIN:"TRAIN",TRAM:"TRAM"};_.Ji={LESS_WALKING:"LESS_WALKING",FEWER_TRANSFERS:"FEWER_TRANSFERS"};var Ki=_.cd({routes:_.id(_.jd(_.db))},!0);_.Ke=[];_.t(Me,_.C);_.k=Me.prototype;_.k.internalAnchor_changed=function(){var a=this.get("internalAnchor");Ne(this,"attribution",a);Ne(this,"place",a);Ne(this,"internalAnchorMap",a,"map");Ne(this,"internalAnchorPoint",a,"anchorPoint");a instanceof _.De?Ne(this,"internalAnchorPosition",a,"internalPosition"):Ne(this,"internalAnchorPosition",a,"position")};
_.k.internalAnchorPoint_changed=Me.prototype.internalPixelOffset_changed=function(){var a=this.get("internalAnchorPoint")||_.li,b=this.get("internalPixelOffset")||_.mi;this.set("pixelOffset",new _.O(b.width+Math.round(a.x),b.height+Math.round(a.y)))};_.k.internalAnchorPosition_changed=function(){var a=this.get("internalAnchorPosition");a&&this.set("position",a)};_.k.internalAnchorMap_changed=function(){this.get("internalAnchor")&&this.b.set("map",this.get("internalAnchorMap"))};
_.k.qm=function(){var a=this.get("internalAnchor");!this.b.get("map")&&a&&a.get("map")&&this.set("internalAnchor",null)};_.k.internalContent_changed=function(){this.set("content",Je(this.get("internalContent")))};_.k.trigger=function(a){_.y.trigger(this.b,a)};_.k.close=function(){this.b.set("map",null)};_.t(_.Oe,_.C);_.Ld(_.Oe.prototype,{content:_.kd([_.ji,_.jd(fd)]),position:_.md(_.Id),size:_.md(qd),map:_.kd([_.Ei,Di]),anchor:_.md(_.gd(_.C,"MVCObject")),zIndex:_.ii});_.Oe.prototype.open=function(a,b){this.set("anchor",b);b?!this.get("map")&&a&&this.set("map",a):this.set("map",a)};_.Oe.prototype.close=function(){this.set("map",null)};_.t(Pe,_.C);Pe.prototype.changed=function(a){if("map"==a||"panel"==a){var b=this;_.G("directions",function(c){c.rl(b,a)})}"panel"==a&&_.Le(this.getPanel())};_.Ld(Pe.prototype,{directions:Ki,map:_.Ei,panel:_.md(_.jd(fd)),routeIndex:_.ii});Qe.prototype.route=function(a,b){_.G("directions",function(c){c.Gh(a,b,!0)})};Re.prototype.getDistanceMatrix=function(a,b){_.G("distance_matrix",function(c){c.b(a,b)})};Se.prototype.getElevationAlongPath=function(a,b){_.G("elevation",function(c){c.getElevationAlongPath(a,b)})};Se.prototype.getElevationForLocations=function(a,b){_.G("elevation",function(c){c.getElevationForLocations(a,b)})};_.Mi=_.gd(_.Xd,"LatLngBounds");_.Te.prototype.geocode=function(a,b){_.G("geocoder",function(c){c.geocode(a,b)})};_.t(_.Ue,_.C);_.Ue.prototype.map_changed=function(){var a=this;_.G("kml",function(b){b.b(a)})};_.Ld(_.Ue.prototype,{map:_.Ei,url:null,bounds:null,opacity:_.ii});_.Ni={UNKNOWN:"UNKNOWN",OK:_.ha,INVALID_REQUEST:_.ca,DOCUMENT_NOT_FOUND:"DOCUMENT_NOT_FOUND",FETCH_ERROR:"FETCH_ERROR",INVALID_DOCUMENT:"INVALID_DOCUMENT",DOCUMENT_TOO_LARGE:"DOCUMENT_TOO_LARGE",LIMITS_EXCEEDED:"LIMITS_EXECEEDED",TIMED_OUT:"TIMED_OUT"};_.t(Ye,_.C);_.k=Ye.prototype;_.k.rd=function(){var a=this;_.G("kml",function(b){b.f(a)})};_.k.url_changed=Ye.prototype.rd;_.k.driveFileId_changed=Ye.prototype.rd;_.k.map_changed=Ye.prototype.rd;_.k.zIndex_changed=Ye.prototype.rd;_.Ld(Ye.prototype,{map:_.Ei,defaultViewport:null,metadata:null,status:null,url:_.ji,screenOverlays:_.ki,zIndex:_.ii});_.t(_.Ze,_.C);_.Ld(_.Ze.prototype,{map:_.Ei});_.t($e,_.C);_.Ld($e.prototype,{map:_.Ei});_.t(af,_.C);_.Ld(af.prototype,{map:_.Ei});_.Oi={NEAREST:"nearest",BEST:"best"};_.Pi={DEFAULT:"default",OUTDOOR:"outdoor"};_.bg={japan_prequake:20,japan_postquake2010:24};_.bf.prototype.Ye=!0;_.bf.prototype.wb=_.sa(4);_.bf.prototype.Wg=!0;_.bf.prototype.Dd=_.sa(6);_.cf("about:blank");_.$f="StopIteration"in _.Gc?_.Gc.StopIteration:{message:"StopIteration",stack:""};_.df.prototype.next=function(){throw _.$f;};_.df.prototype.Ae=function(){return this};!_.Jh&&!_.Hh||_.Hh&&9<=Number(_.Wh)||_.Jh&&_.Pc("1.9.1");_.Hh&&_.Pc("9");_.ff.prototype.Wg=!0;_.ff.prototype.Dd=_.sa(5);_.ff.prototype.Ye=!0;_.ff.prototype.wb=_.sa(3);_.ef={};_.gf("<!DOCTYPE html>",0);_.gf("",0);_.gf("<br>",0);var Qi;_.t(hf,_.I);var Ri;_.t(jf,_.I);var Si;_.t(kf,_.I);var Ti;_.t(lf,_.I);_.t(mf,_.I);_.t(_.nf,_.I);_.t(of,_.I);_.t(pf,_.I);_.t(qf,_.I);var Ui;_.t(uf,_.I);var Vi;_.t(vf,_.I);_.pg={};_.yf.prototype.isEmpty=function(){return!(this.I<this.L&&this.J<this.M)};_.yf.prototype.extend=function(a){a&&(this.I=Math.min(this.I,a.x),this.L=Math.max(this.L,a.x),this.J=Math.min(this.J,a.y),this.M=Math.max(this.M,a.y))};_.yf.prototype.getCenter=function(){return new _.N((this.I+this.L)/2,(this.J+this.M)/2)};_.Wi=_.zf(-window.Infinity,-window.Infinity,window.Infinity,window.Infinity);_.Xi=_.zf(0,0,0,0);_.Cf.prototype.addListener=function(a,b){this.P.addListener(a,b)};_.Cf.prototype.addListenerOnce=function(a,b){this.P.addListenerOnce(a,b)};_.Cf.prototype.removeListener=function(a,b){this.P.removeListener(a,b)};_.Cf.prototype.b=_.sa(7);_.t(Df,_.df);Df.prototype.setPosition=function(a,b,c){if(this.node=a)this.f=_.za(b)?b:1!=this.node.nodeType?0:this.b?-1:1;_.za(c)&&(this.depth=c)};
Df.prototype.next=function(){var a;if(this.j){if(!this.node||this.l&&0==this.depth)throw _.$f;a=this.node;var b=this.b?-1:1;if(this.f==b){var c=this.b?a.lastChild:a.firstChild;c?this.setPosition(c):this.setPosition(a,-1*b)}else(c=this.b?a.previousSibling:a.nextSibling)?this.setPosition(c):this.setPosition(a.parentNode,-1*b);this.depth+=this.f*(this.b?-1:1)}else this.j=!0;a=this.node;if(!this.node)throw _.$f;return a};
Df.prototype.splice=function(a){var b=this.node,c=this.b?1:-1;this.f==c&&(this.f=-1*c,this.depth+=this.f*(this.b?-1:1));this.b=!this.b;Df.prototype.next.call(this);this.b=!this.b;for(var c=_.wa(arguments[0])?arguments[0]:arguments,d=c.length-1;0<=d;d--)_.wf(c[d],b);_.xf(b)};_.t(_.Ef,_.C);_.Ef.prototype.K=function(){var a=this;a.D||(a.D=window.setTimeout(function(){a.D=void 0;a.Z()},a.ki))};_.Ef.prototype.C=function(){this.D&&window.clearTimeout(this.D);this.D=void 0;this.Z()};_.t(Ff,Df);Ff.prototype.next=function(){do Ff.Hb.next.call(this);while(-1==this.f);return this.node};_.Sf.prototype.fromLatLngToPoint=function(a,b){b=b||new _.N(0,0);var c=this.b;b.x=c.x+a.lng()*this.j;a=_.Ya(Math.sin(_.tc(a.lat())),-(1-1E-15),1-1E-15);b.y=c.y+.5*Math.log((1+a)/(1-a))*-this.l;return b};_.Sf.prototype.fromPointToLatLng=function(a,b){var c=this.b;return new _.Q(_.uc(2*Math.atan(Math.exp((a.y-c.y)/-this.l))-Math.PI/2),(a.x-c.x)/this.j,b)};var Vf;_.t(dg,_.me);dg.prototype.visible_changed=function(){var a=this;!a.m&&a.getVisible()&&(a.m=!0,_.G("streetview",function(b){var c;a.f&&(c=a.f);b.Im(a,c)}))};_.Ld(dg.prototype,{visible:_.ki,pano:_.ji,position:_.md(_.Id),pov:_.md(qi),motionTracking:hi,photographerPov:null,location:null,links:_.id(_.jd(_.db)),status:null,zoom:_.ii,enableCloseButton:_.ki});dg.prototype.registerPanoProvider=function(a,b){this.set("panoProvider",{yh:a,options:b||{}})};_.k=_.eg.prototype;_.k.vg=_.sa(8);_.k.Mb=_.sa(9);_.k.Nf=_.sa(10);_.k.Mf=_.sa(11);_.k.Lf=_.sa(12);_.t(fg,oe);var Zi;_.t(gg,_.I);var $i;_.t(hg,_.I);var aj;_.t(ig,_.I);ig.prototype.getZoom=function(){return _.K(this,2)};ig.prototype.setZoom=function(a){this.data[2]=a};_.t(jg,_.Ef);var kg={roadmap:0,satellite:2,hybrid:3,terrain:4},bj={0:1,2:2,3:2,4:2};_.k=jg.prototype;_.k.Jg=_.Jd("center");_.k.Zf=_.Jd("zoom");_.k.changed=function(){var a=this.Jg(),b=this.Zf(),c=lg(this);if(a&&!a.b(this.G)||this.F!=b||this.O!=c)mg(this.f),this.K(),this.F=b,this.O=c;this.G=a};
_.k.Z=function(){var a="",b=this.Jg(),c=this.Zf(),d=lg(this),e=this.get("size");if(e){if(b&&(0,window.isFinite)(b.lat())&&(0,window.isFinite)(b.lng())&&1<c&&null!=d&&e&&e.width&&e.height&&this.b){_.Af(this.b,e);var f;(b=_.Xf(this.l,b,c))?(f=new _.yf,f.I=Math.round(b.x-e.width/2),f.L=f.I+e.width,f.J=Math.round(b.y-e.height/2),f.M=f.J+e.height):f=null;b=bj[d];if(f){var a=new ig,g=new gg(_.M(a,0));g.data[0]=f.I;g.data[1]=f.J;a.data[1]=b;a.setZoom(c);c=new hg(_.M(a,3));c.data[0]=f.L-f.I;c.data[1]=f.M-
f.J;c=new vf(_.M(a,4));c.data[0]=d;c.data[4]=_.rf(_.tf(_.R));c.data[5]=_.sf(_.tf(_.R)).toLowerCase();c.data[9]=!0;c.data[11]=!0;d=this.B+(0,window.unescape)("%3F");if(!aj){c=aj={b:-1,A:[]};b=new gg([]);Zi||(Zi={b:-1,A:[,_.T,_.T]});b=_.F(b,Zi);f=new hg([]);$i||($i={b:-1,A:[]},$i.A=[,_.vh,_.vh,_.pc(1)]);f=_.F(f,$i);g=new vf([]);if(!Vi){var h=[];Vi={b:-1,A:h};h[1]=_.V;h[2]=_.U;h[3]=_.U;h[5]=_.W;h[6]=_.W;var l=new uf([]);Ui||(Ui={b:-1,A:[,_.zh,_.U]});h[9]=_.F(l,Ui);h[10]=_.U;h[11]=_.U;h[12]=_.U;h[13]=
_.zh;h[100]=_.U}g=_.F(g,Vi);h=new hf([]);if(!Qi){var l=Qi={b:-1,A:[]},n=new jf([]);Ri||(Ri={b:-1,A:[,_.U]});var n=_.F(n,Ri),q=new lf([]);Ti||(Ti={b:-1,A:[,_.U,_.U]});var q=_.F(q,Ti),r=new kf([]);Si||(Si={b:-1,A:[,_.U]});l.A=[,n,,,,,,,,,q,,_.F(r,Si)]}c.A=[,b,_.V,_.vh,f,g,_.F(h,Qi)]}a=_.Yh.b(a.data,aj);a=this.m(d+a)}}this.f&&(_.Af(this.f,e),og(this,a))}};
_.k.div_changed=function(){var a=this.get("div"),b=this.b;if(a)if(b)a.appendChild(b);else{b=this.b=window.document.createElement("div");b.style.overflow="hidden";var c=this.f=window.document.createElement("img");_.y.addDomListener(b,"contextmenu",function(a){_.lb(a);_.nb(a)});c.ontouchstart=c.ontouchmove=c.ontouchend=c.ontouchcancel=function(a){_.mb(a);_.nb(a)};_.Af(c,_.mi);a.appendChild(b);this.Z()}else b&&(mg(b),this.b=null)};_.t(sg,_.ze);_.k=sg.prototype;_.k.streetView_changed=function(){var a=this.get("streetView");a?a.set("standAlone",!1):this.set("streetView",this.__gm.j)};_.k.getDiv=function(){return this.__gm.R};_.k.panBy=function(a,b){var c=this.__gm;_.G("map",function(){_.y.trigger(c,"panby",a,b)})};_.k.panTo=function(a){var b=this.__gm;a=_.Id(a);_.G("map",function(){_.y.trigger(b,"panto",a)})};_.k.panToBounds=function(a){var b=this.__gm,c=_.$d(a);_.G("map",function(){_.y.trigger(b,"pantolatlngbounds",c)})};
_.k.fitBounds=function(a){var b=this;a=_.$d(a);_.G("map",function(c){c.fitBounds(b,a)})};_.Ld(sg.prototype,{bounds:null,streetView:Di,center:_.md(_.Id),zoom:_.ii,mapTypeId:_.ji,projection:null,heading:_.ii,tilt:_.ii,clickableIcons:hi});tg.prototype.getMaxZoomAtLatLng=function(a,b){_.G("maxzoom",function(c){c.getMaxZoomAtLatLng(a,b)})};_.t(ug,_.C);ug.prototype.changed=function(a){if("suppressInfoWindows"!=a&&"clickable"!=a){var b=this;_.G("onion",function(a){a.b(b)})}};_.Ld(ug.prototype,{map:_.Ei,tableId:_.ii,query:_.md(_.kd([_.gi,_.jd(_.db,"not an Object")]))});_.t(_.vg,_.C);_.vg.prototype.map_changed=function(){var a=this;_.G("overlay",function(b){b.Vj(a)})};_.Ld(_.vg.prototype,{panes:null,projection:null,map:_.kd([_.Ei,Di])});_.t(_.wg,_.C);_.wg.prototype.map_changed=_.wg.prototype.visible_changed=function(){var a=this;_.G("poly",function(b){b.b(a)})};_.wg.prototype.center_changed=function(){_.y.trigger(this,"bounds_changed")};_.wg.prototype.radius_changed=_.wg.prototype.center_changed;_.wg.prototype.getBounds=function(){var a=this.get("radius"),b=this.get("center");if(b&&_.x(a)){var c=this.get("map"),c=c&&c.__gm.get("baseMapType");return _.ag(b,a/_.ue(c))}return null};
_.Ld(_.wg.prototype,{center:_.md(_.Id),draggable:_.ki,editable:_.ki,map:_.Ei,radius:_.ii,visible:_.ki});_.t(_.xg,_.C);_.xg.prototype.map_changed=_.xg.prototype.visible_changed=function(){var a=this;_.G("poly",function(b){b.j(a)})};_.Ld(_.xg.prototype,{draggable:_.ki,editable:_.ki,bounds:_.md(_.$d),map:_.Ei,visible:_.ki});_.t(yg,_.C);yg.prototype.map_changed=function(){var a=this;_.G("streetview",function(b){b.Uj(a)})};_.Ld(yg.prototype,{map:_.Ei});_.zg.prototype.getPanorama=function(a,b){var c=this.b||void 0;_.G("streetview",function(d){_.G("geometry",function(e){d.Sk(a,b,e.computeHeading,e.computeOffset,c)})})};_.zg.prototype.getPanoramaByLocation=function(a,b,c){this.getPanorama({location:a,radius:b,preference:50>(b||0)?"best":"nearest"},c)};_.zg.prototype.getPanoramaById=function(a,b){this.getPanorama({pano:a},b)};var Bg,Cg;Bg={0:"",1:"msie",3:"chrome",4:"applewebkit",5:"firefox",6:"trident",7:"mozilla",2:"edge"};Cg={0:"",1:"x11",2:"macintosh",3:"windows",4:"android",5:"iphone",6:"ipad"};_.S=null;"undefined"!=typeof window.navigator&&(_.S=new Dg);Eg.prototype.j=_.Fb(function(){var a=new window.Image;return _.m(a.crossOrigin)});Eg.prototype.l=_.Fb(function(){return _.m(window.document.createElement("span").draggable)});Eg.prototype.f=_.Fb(function(){try{var a=window.document.createElement("canvas").getContext("2d"),b=a.getImageData(0,0,1,1);b.data[0]=b.data[1]=b.data[2]=100;b.data[3]=64;a.putImageData(b,0,0);b=a.getImageData(0,0,1,1);return 95>b.data[0]||105<b.data[0]}catch(c){return!1}});_.cj=_.S?new Eg(_.S):null;_.dj=_.S?new Kg:null;_.ej=new _.Lg(0,0);_.t(_.Mg,_.C);_.k=_.Mg.prototype;_.k.getTile=function(a,b,c){if(!a||!c)return null;var d=c.createElement("div");c={Y:a,zoom:b,Ib:null};d.__gmimt=c;_.wd(this.b,d);var e=Og(this);1!=e&&Ng(d,e);if(this.f){var e=this.tileSize||new _.O(256,256),f=this.j(a,b);c.Ib=this.f(a,b,e,d,f,function(){_.y.trigger(d,"load")})}return d};_.k.releaseTile=function(a){a&&this.b.contains(a)&&(this.b.remove(a),(a=a.__gmimt.Ib)&&a.release())};_.k.Pe=_.sa(13);
_.k.opacity_changed=function(){var a=Og(this);this.b.forEach(function(b){Ng(b,a)})};_.k.md=!0;_.Ld(_.Mg.prototype,{opacity:_.ii});_.t(_.Pg,_.C);_.Pg.prototype.getTile=jh;_.Pg.prototype.tileSize=new _.O(256,256);_.Pg.prototype.md=!0;_.t(_.Qg,_.Pg);_.t(_.Rg,_.C);_.Ld(_.Rg.prototype,{attribution:_.md(ri),place:_.md(Ci)});var fj={Animation:{BOUNCE:1,DROP:2,yo:3,wo:4},Circle:_.wg,ControlPosition:_.cg,Data:He,GroundOverlay:_.Ue,ImageMapType:_.Mg,InfoWindow:_.Oe,LatLng:_.Q,LatLngBounds:_.Xd,MVCArray:_.be,MVCObject:_.C,Map:sg,MapTypeControlStyle:{DEFAULT:0,HORIZONTAL_BAR:1,DROPDOWN_MENU:2,INSET:3,INSET_LARGE:4},MapTypeId:_.ih,MapTypeRegistry:se,Marker:_.De,MarkerImage:function(a,b,c,d,e){this.url=a;this.size=b||e;this.origin=c;this.anchor=d;this.scaledSize=e;this.labelOrigin=null},NavigationControlStyle:{DEFAULT:0,SMALL:1,
ANDROID:2,ZOOM_PAN:3,zo:4,Ej:5},OverlayView:_.vg,Point:_.N,Polygon:_.Ee,Polyline:_.Fe,Rectangle:_.xg,ScaleControlStyle:{DEFAULT:0},Size:_.O,StreetViewPreference:_.Oi,StreetViewSource:_.Pi,StrokePosition:{CENTER:0,INSIDE:1,OUTSIDE:2},SymbolPath:mh,ZoomControlStyle:{DEFAULT:0,SMALL:1,LARGE:2,Ej:3},event:_.y};
_.Wa(fj,{BicyclingLayer:_.Ze,DirectionsRenderer:Pe,DirectionsService:Qe,DirectionsStatus:{OK:_.ha,UNKNOWN_ERROR:_.ka,OVER_QUERY_LIMIT:_.ia,REQUEST_DENIED:_.ja,INVALID_REQUEST:_.ca,ZERO_RESULTS:_.la,MAX_WAYPOINTS_EXCEEDED:_.fa,NOT_FOUND:_.ga},DirectionsTravelMode:_.Gi,DirectionsUnitSystem:_.Fi,DistanceMatrixService:Re,DistanceMatrixStatus:{OK:_.ha,INVALID_REQUEST:_.ca,OVER_QUERY_LIMIT:_.ia,REQUEST_DENIED:_.ja,UNKNOWN_ERROR:_.ka,MAX_ELEMENTS_EXCEEDED:_.ea,MAX_DIMENSIONS_EXCEEDED:_.da},DistanceMatrixElementStatus:{OK:_.ha,
NOT_FOUND:_.ga,ZERO_RESULTS:_.la},ElevationService:Se,ElevationStatus:{OK:_.ha,UNKNOWN_ERROR:_.ka,OVER_QUERY_LIMIT:_.ia,REQUEST_DENIED:_.ja,INVALID_REQUEST:_.ca,ro:"DATA_NOT_AVAILABLE"},FusionTablesLayer:ug,Geocoder:_.Te,GeocoderLocationType:{ROOFTOP:"ROOFTOP",RANGE_INTERPOLATED:"RANGE_INTERPOLATED",GEOMETRIC_CENTER:"GEOMETRIC_CENTER",APPROXIMATE:"APPROXIMATE"},GeocoderStatus:{OK:_.ha,UNKNOWN_ERROR:_.ka,OVER_QUERY_LIMIT:_.ia,REQUEST_DENIED:_.ja,INVALID_REQUEST:_.ca,ZERO_RESULTS:_.la,ERROR:_.ba},KmlLayer:Ye,
KmlLayerStatus:_.Ni,MaxZoomService:tg,MaxZoomStatus:{OK:_.ha,ERROR:_.ba},SaveWidget:_.Rg,StreetViewCoverageLayer:yg,StreetViewPanorama:dg,StreetViewService:_.zg,StreetViewStatus:{OK:_.ha,UNKNOWN_ERROR:_.ka,ZERO_RESULTS:_.la},StyledMapType:_.Qg,TrafficLayer:$e,TrafficModel:_.Hi,TransitLayer:af,TransitMode:_.Ii,TransitRoutePreference:_.Ji,TravelMode:_.Gi,UnitSystem:_.Fi});_.Wa(He,{Feature:_.he,Geometry:Hd,GeometryCollection:_.je,LineString:_.Td,LinearRing:_.Ud,MultiLineString:_.ee,MultiPoint:_.Vd,MultiPolygon:_.le,Point:_.Wd,Polygon:_.ge});_.wc("main",{});var Ug=/'/g,Vg;var rd=arguments[0];
window.google.maps.Load(function(a,b){var c=window.google.maps;Zg();var d=$g(c);_.R=new pf(a);_.gj=Math.random()<_.K(_.R,0,1);_.hj=Math.round(1E15*Math.random()).toString(36);_.rg=Wg();_.Li=Xg();_.Yi=new _.be;_.Tf=b;for(a=0;a<_.Fc(_.R,8);++a)_.pg[_.Ec(_.R,8,a)]=!0;a=new _.nf(_.R.data[3]);ud(_.L(a,0));_.Ua(fj,function(a,b){c[a]=b});c.version=_.L(a,1);window.setTimeout(function(){xc(["util","stats"],function(a,b){a.f.b();a.j();d&&b.b.b({ev:"api_alreadyloaded",client:_.L(_.R,6),key:_.L(_.R,16)})})},
5E3);_.y.Ym();Vf=new Uf;(a=_.L(_.R,11))&&xc(_.zc(_.R,12),Yg(a),!0)});}).call(this,{});
The file gmaps.js contains:
"use strict";
(function(root, factory) {
  if(typeof exports === 'object') {
    module.exports = factory();
  }
  else if(typeof define === 'function' && define.amd) {
    define(['jquery', 'googlemaps!'], factory);
  }
  else {
    root.GMaps = factory();
  }


}(this, function() {

/*!
 * GMaps.js v0.4.25
 * http://hpneo.github.com/gmaps/
 *
 * Copyright 2017, Gustavo Leon
 * Released under the MIT License.
 */

var extend_object = function(obj, new_obj) {
  var name;

  if (obj === new_obj) {
    return obj;
  }

  for (name in new_obj) {
    if (new_obj[name] !== undefined) {
      obj[name] = new_obj[name];
    }
  }

  return obj;
};

var replace_object = function(obj, replace) {
  var name;

  if (obj === replace) {
    return obj;
  }

  for (name in replace) {
    if (obj[name] != undefined) {
      obj[name] = replace[name];
    }
  }

  return obj;
};

var array_map = function(array, callback) {
  var original_callback_params = Array.prototype.slice.call(arguments, 2),
      array_return = [],
      array_length = array.length,
      i;

  if (Array.prototype.map && array.map === Array.prototype.map) {
    array_return = Array.prototype.map.call(array, function(item) {
      var callback_params = original_callback_params.slice(0);
      callback_params.splice(0, 0, item);

      return callback.apply(this, callback_params);
    });
  }
  else {
    for (i = 0; i < array_length; i++) {
      callback_params = original_callback_params;
      callback_params.splice(0, 0, array[i]);
      array_return.push(callback.apply(this, callback_params));
    }
  }

  return array_return;
};

var array_flat = function(array) {
  var new_array = [],
      i;

  for (i = 0; i < array.length; i++) {
    new_array = new_array.concat(array[i]);
  }

  return new_array;
};

var coordsToLatLngs = function(coords, useGeoJSON) {
  var first_coord = coords[0],
      second_coord = coords[1];

  if (useGeoJSON) {
    first_coord = coords[1];
    second_coord = coords[0];
  }

  return new google.maps.LatLng(first_coord, second_coord);
};

var arrayToLatLng = function(coords, useGeoJSON) {
  var i;

  for (i = 0; i < coords.length; i++) {
    if (!(coords[i] instanceof google.maps.LatLng)) {
      if (coords[i].length > 0 && typeof(coords[i][0]) === "object") {
        coords[i] = arrayToLatLng(coords[i], useGeoJSON);
      }
      else {
        coords[i] = coordsToLatLngs(coords[i], useGeoJSON);
      }
    }
  }

  return coords;
};

var getElementsByClassName = function (class_name, context) {
    var element,
        _class = class_name.replace('.', '');

    if ('jQuery' in this && context) {
        element = $("." + _class, context)[0];
    } else {
        element = document.getElementsByClassName(_class)[0];
    }
    return element;

};

var getElementById = function(id, context) {
  var element,
  id = id.replace('#', '');

  if ('jQuery' in window && context) {
    element = $('#' + id, context)[0];
  } else {
    element = document.getElementById(id);
  };

  return element;
};

var findAbsolutePosition = function(obj)  {
  var curleft = 0,
      curtop = 0;

  if (obj.getBoundingClientRect) {
      var rect = obj.getBoundingClientRect();
      var sx = -(window.scrollX ? window.scrollX : window.pageXOffset);
      var sy = -(window.scrollY ? window.scrollY : window.pageYOffset);

      return [(rect.left - sx), (rect.top - sy)];
  }

  if (obj.offsetParent) {
    do {
      curleft += obj.offsetLeft;
      curtop += obj.offsetTop;
    } while (obj = obj.offsetParent);
  }

  return [curleft, curtop];
};

var GMaps = (function(global) {
  "use strict";

  var doc = document;
  /**
   * Creates a new GMaps instance, including a Google Maps map.
   * @class GMaps
   * @constructs
   * @param {object} options - `options` accepts all the [MapOptions](https://developers.google.com/maps/documentation/javascript/reference#MapOptions) and [events](https://developers.google.com/maps/documentation/javascript/reference#Map) listed in the Google Maps API. Also accepts:
   * * `lat` (number): Latitude of the map's center
   * * `lng` (number): Longitude of the map's center
   * * `el` (string or HTMLElement): container where the map will be rendered
   * * `markerClusterer` (function): A function to create a marker cluster. You can use MarkerClusterer or MarkerClustererPlus.
   */
  var GMaps = function(options) {

    if (!(typeof window.google === 'object' && window.google.maps)) {
      if (typeof window.console === 'object' && window.console.error) {
        console.error('Google Maps API is required. Please register the following JavaScript library https://maps.googleapis.com/maps/api/js.');
      }

      return function() {};
    }

    if (!this) return new GMaps(options);

    options.zoom = options.zoom || 15;
    options.mapType = options.mapType || 'roadmap';

    var valueOrDefault = function(value, defaultValue) {
      return value === undefined ? defaultValue : value;
    };

    var self = this,
        i,
        events_that_hide_context_menu = [
          'bounds_changed', 'center_changed', 'click', 'dblclick', 'drag',
          'dragend', 'dragstart', 'idle', 'maptypeid_changed', 'projection_changed',
          'resize', 'tilesloaded', 'zoom_changed'
        ],
        events_that_doesnt_hide_context_menu = ['mousemove', 'mouseout', 'mouseover'],
        options_to_be_deleted = ['el', 'lat', 'lng', 'mapType', 'width', 'height', 'markerClusterer', 'enableNewStyle'],
        identifier = options.el || options.div,
        markerClustererFunction = options.markerClusterer,
        mapType = google.maps.MapTypeId[options.mapType.toUpperCase()],
        map_center = new google.maps.LatLng(options.lat, options.lng),
        zoomControl = valueOrDefault(options.zoomControl, true),
        zoomControlOpt = options.zoomControlOpt || {
          style: 'DEFAULT',
          position: 'TOP_LEFT'
        },
        zoomControlStyle = zoomControlOpt.style || 'DEFAULT',
        zoomControlPosition = zoomControlOpt.position || 'TOP_LEFT',
        panControl = valueOrDefault(options.panControl, true),
        mapTypeControl = valueOrDefault(options.mapTypeControl, true),
        scaleControl = valueOrDefault(options.scaleControl, true),
        streetViewControl = valueOrDefault(options.streetViewControl, true),
        overviewMapControl = valueOrDefault(overviewMapControl, true),
        map_options = {},
        map_base_options = {
          zoom: this.zoom,
          center: map_center,
          mapTypeId: mapType
        },
        map_controls_options = {
          panControl: panControl,
          zoomControl: zoomControl,
          zoomControlOptions: {
            style: google.maps.ZoomControlStyle[zoomControlStyle],
            position: google.maps.ControlPosition[zoomControlPosition]
          },
          mapTypeControl: mapTypeControl,
          scaleControl: scaleControl,
          streetViewControl: streetViewControl,
          overviewMapControl: overviewMapControl
        };

      if (typeof(options.el) === 'string' || typeof(options.div) === 'string') {
        if (identifier.indexOf("#") > -1) {
            /**
             * Container element
             *
             * @type {HTMLElement}
             */
            this.el = getElementById(identifier, options.context);
        } else {
            this.el = getElementsByClassName.apply(this, [identifier, options.context]);
        }
      } else {
          this.el = identifier;
      }

    if (typeof(this.el) === 'undefined' || this.el === null) {
      throw 'No element defined.';
    }

    window.context_menu = window.context_menu || {};
    window.context_menu[self.el.id] = {};

    /**
     * Collection of custom controls in the map UI
     *
     * @type {array}
     */
    this.controls = [];
    /**
     * Collection of map's overlays
     *
     * @type {array}
     */
    this.overlays = [];
    /**
     * Collection of KML/GeoRSS and FusionTable layers
     *
     * @type {array}
     */
    this.layers = [];
    /**
     * Collection of data layers (See {@link GMaps#addLayer})
     *
     * @type {object}
     */
    this.singleLayers = {};
    /**
     * Collection of map's markers
     *
     * @type {array}
     */
    this.markers = [];
    /**
     * Collection of map's lines
     *
     * @type {array}
     */
    this.polylines = [];
    /**
     * Collection of map's routes requested by {@link GMaps#getRoutes}, {@link GMaps#renderRoute}, {@link GMaps#drawRoute}, {@link GMaps#travelRoute} or {@link GMaps#drawSteppedRoute}
     *
     * @type {array}
     */
    this.routes = [];
    /**
     * Collection of map's polygons
     *
     * @type {array}
     */
    this.polygons = [];
    this.infoWindow = null;
    this.overlay_el = null;
    /**
     * Current map's zoom
     *
     * @type {number}
     */
    this.zoom = options.zoom;
    this.registered_events = {};

    this.el.style.width = options.width || this.el.scrollWidth || this.el.offsetWidth;
    this.el.style.height = options.height || this.el.scrollHeight || this.el.offsetHeight;

    google.maps.visualRefresh = options.enableNewStyle;

    for (i = 0; i < options_to_be_deleted.length; i++) {
      delete options[options_to_be_deleted[i]];
    }

    if(options.disableDefaultUI != true) {
      map_base_options = extend_object(map_base_options, map_controls_options);
    }

    map_options = extend_object(map_base_options, options);

    for (i = 0; i < events_that_hide_context_menu.length; i++) {
      delete map_options[events_that_hide_context_menu[i]];
    }

    for (i = 0; i < events_that_doesnt_hide_context_menu.length; i++) {
      delete map_options[events_that_doesnt_hide_context_menu[i]];
    }

    /**
     * Google Maps map instance
     *
     * @type {google.maps.Map}
     */
    this.map = new google.maps.Map(this.el, map_options);

    if (markerClustererFunction) {
      /**
       * Marker Clusterer instance
       *
       * @type {object}
       */
      this.markerClusterer = markerClustererFunction.apply(this, [this.map]);
    }

    var buildContextMenuHTML = function(control, e) {
      var html = '',
          options = window.context_menu[self.el.id][control];

      for (var i in options){
        if (options.hasOwnProperty(i)) {
          var option = options[i];

          html += '<li><a id="' + control + '_' + i + '" href="#">' + option.title + '</a></li>';
        }
      }

      if (!getElementById('gmaps_context_menu')) return;

      var context_menu_element = getElementById('gmaps_context_menu');

      context_menu_element.innerHTML = html;

      var context_menu_items = context_menu_element.getElementsByTagName('a'),
          context_menu_items_count = context_menu_items.length,
          i;

      for (i = 0; i < context_menu_items_count; i++) {
        var context_menu_item = context_menu_items[i];

        var assign_menu_item_action = function(ev){
          ev.preventDefault();

          options[this.id.replace(control + '_', '')].action.apply(self, [e]);
          self.hideContextMenu();
        };

        google.maps.event.clearListeners(context_menu_item, 'click');
        google.maps.event.addDomListenerOnce(context_menu_item, 'click', assign_menu_item_action, false);
      }

      var position = findAbsolutePosition.apply(this, [self.el]),
          left = position[0] + e.pixel.x - 15,
          top = position[1] + e.pixel.y- 15;

      context_menu_element.style.left = left + "px";
      context_menu_element.style.top = top + "px";

      // context_menu_element.style.display = 'block';
    };

    this.buildContextMenu = function(control, e) {
      if (control === 'marker') {
        e.pixel = {};

        var overlay = new google.maps.OverlayView();
        overlay.setMap(self.map);

        overlay.draw = function() {
          var projection = overlay.getProjection(),
              position = e.marker.getPosition();

          e.pixel = projection.fromLatLngToContainerPixel(position);

          buildContextMenuHTML(control, e);
        };
      }
      else {
        buildContextMenuHTML(control, e);
      }

      var context_menu_element = getElementById('gmaps_context_menu');

      setTimeout(function() {
        context_menu_element.style.display = 'block';
      }, 0);
    };

    /**
     * Add a context menu for a map or a marker.
     *
     * @param {object} options - The `options` object should contain:
     * * `control` (string): Kind of control the context menu will be attached. Can be "map" or "marker".
     * * `options` (array): A collection of context menu items:
     *   * `title` (string): Item's title shown in the context menu.
     *   * `name` (string): Item's identifier.
     *   * `action` (function): Function triggered after selecting the context menu item.
     */
    this.setContextMenu = function(options) {
      window.context_menu[self.el.id][options.control] = {};

      var i,
          ul = doc.createElement('ul');

      for (i in options.options) {
        if (options.options.hasOwnProperty(i)) {
          var option = options.options[i];

          window.context_menu[self.el.id][options.control][option.name] = {
            title: option.title,
            action: option.action
          };
        }
      }

      ul.id = 'gmaps_context_menu';
      ul.style.display = 'none';
      ul.style.position = 'absolute';
      ul.style.minWidth = '100px';
      ul.style.background = 'white';
      ul.style.listStyle = 'none';
      ul.style.padding = '8px';
      ul.style.boxShadow = '2px 2px 6px #ccc';

      if (!getElementById('gmaps_context_menu')) {
        doc.body.appendChild(ul);
      }

      var context_menu_element = getElementById('gmaps_context_menu');

      google.maps.event.addDomListener(context_menu_element, 'mouseout', function(ev) {
        if (!ev.relatedTarget || !this.contains(ev.relatedTarget)) {
          window.setTimeout(function(){
            context_menu_element.style.display = 'none';
          }, 400);
        }
      }, false);
    };

    /**
     * Hide the current context menu
     */
    this.hideContextMenu = function() {
      var context_menu_element = getElementById('gmaps_context_menu');

      if (context_menu_element) {
        context_menu_element.style.display = 'none';
      }
    };

    var setupListener = function(object, name) {
      google.maps.event.addListener(object, name, function(e){
        if (e == undefined) {
          e = this;
        }

        options[name].apply(this, [e]);

        self.hideContextMenu();
      });
    };

    //google.maps.event.addListener(this.map, 'idle', this.hideContextMenu);
    google.maps.event.addListener(this.map, 'zoom_changed', this.hideContextMenu);

    for (var ev = 0; ev < events_that_hide_context_menu.length; ev++) {
      var name = events_that_hide_context_menu[ev];

      if (name in options) {
        setupListener(this.map, name);
      }
    }

    for (var ev = 0; ev < events_that_doesnt_hide_context_menu.length; ev++) {
      var name = events_that_doesnt_hide_context_menu[ev];

      if (name in options) {
        setupListener(this.map, name);
      }
    }

    google.maps.event.addListener(this.map, 'rightclick', function(e) {
      if (options.rightclick) {
        options.rightclick.apply(this, [e]);
      }

      if(window.context_menu[self.el.id]['map'] != undefined) {
        self.buildContextMenu('map', e);
      }
    });

    /**
     * Trigger a `resize` event, useful if you need to repaint the current map (for changes in the viewport or display / hide actions).
     */
    this.refresh = function() {
      google.maps.event.trigger(this.map, 'resize');
    };

    /**
     * Adjust the map zoom to include all the markers added in the map.
     */
    this.fitZoom = function() {
      var latLngs = [],
          markers_length = this.markers.length,
          i;

      for (i = 0; i < markers_length; i++) {
        if(typeof(this.markers[i].visible) === 'boolean' && this.markers[i].visible) {
          latLngs.push(this.markers[i].getPosition());
        }
      }

      this.fitLatLngBounds(latLngs);
    };

    /**
     * Adjust the map zoom to include all the coordinates in the `latLngs` array.
     *
     * @param {array} latLngs - Collection of `google.maps.LatLng` objects.
     */
    this.fitLatLngBounds = function(latLngs) {
      var total = latLngs.length,
          bounds = new google.maps.LatLngBounds(),
          i;

      for(i = 0; i < total; i++) {
        bounds.extend(latLngs[i]);
      }

      this.map.fitBounds(bounds);
    };

    /**
     * Center the map using the `lat` and `lng` coordinates.
     *
     * @param {number} lat - Latitude of the coordinate.
     * @param {number} lng - Longitude of the coordinate.
     * @param {function} [callback] - Callback that will be executed after the map is centered.
     */
    this.setCenter = function(lat, lng, callback) {
      this.map.panTo(new google.maps.LatLng(lat, lng));

      if (callback) {
        callback();
      }
    };

    /**
     * Return the HTML element container of the map.
     *
     * @returns {HTMLElement} the element container.
     */
    this.getElement = function() {
      return this.el;
    };

    /**
     * Increase the map's zoom.
     *
     * @param {number} [magnitude] - The number of times the map will be zoomed in.
     */
    this.zoomIn = function(value) {
      value = value || 1;

      this.zoom = this.map.getZoom() + value;
      this.map.setZoom(this.zoom);
    };

    /**
     * Decrease the map's zoom.
     *
     * @param {number} [magnitude] - The number of times the map will be zoomed out.
     */
    this.zoomOut = function(value) {
      value = value || 1;

      this.zoom = this.map.getZoom() - value;
      this.map.setZoom(this.zoom);
    };

    var native_methods = [],
        method;

    for (method in this.map) {
      if (typeof(this.map[method]) == 'function' && !this[method]) {
        native_methods.push(method);
      }
    }

    for (i = 0; i < native_methods.length; i++) {
      (function(gmaps, scope, method_name) {
        gmaps[method_name] = function(){
          return scope[method_name].apply(scope, arguments);
        };
      })(this, this.map, native_methods[i]);
    }
  };

  return GMaps;
})(this);

GMaps.prototype.createControl = function(options) {
  var control = document.createElement('div');

  control.style.cursor = 'pointer';

  if (options.disableDefaultStyles !== true) {
    control.style.fontFamily = 'Roboto, Arial, sans-serif';
    control.style.fontSize = '11px';
    control.style.boxShadow = 'rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px';
  }

  for (var option in options.style) {
    control.style[option] = options.style[option];
  }

  if (options.id) {
    control.id = options.id;
  }

  if (options.title) {
    control.title = options.title;
  }

  if (options.classes) {
    control.className = options.classes;
  }

  if (options.content) {
    if (typeof options.content === 'string') {
      control.innerHTML = options.content;
    }
    else if (options.content instanceof HTMLElement) {
      control.appendChild(options.content);
    }
  }

  if (options.position) {
    control.position = google.maps.ControlPosition[options.position.toUpperCase()];
  }

  for (var ev in options.events) {
    (function(object, name) {
      google.maps.event.addDomListener(object, name, function(){
        options.events[name].apply(this, [this]);
      });
    })(control, ev);
  }

  control.index = 1;

  return control;
};

/**
 * Add a custom control to the map UI.
 *
 * @param {object} options - The `options` object should contain:
 * * `style` (object): The keys and values of this object should be valid CSS properties and values.
 * * `id` (string): The HTML id for the custom control.
 * * `classes` (string): A string containing all the HTML classes for the custom control.
 * * `content` (string or HTML element): The content of the custom control.
 * * `position` (string): Any valid [`google.maps.ControlPosition`](https://developers.google.com/maps/documentation/javascript/controls#ControlPositioning) value, in lower or upper case.
 * * `events` (object): The keys of this object should be valid DOM events. The values should be functions.
 * * `disableDefaultStyles` (boolean): If false, removes the default styles for the controls like font (family and size), and box shadow.
 * @returns {HTMLElement}
 */
GMaps.prototype.addControl = function(options) {
  var control = this.createControl(options);

  this.controls.push(control);
  this.map.controls[control.position].push(control);

  return control;
};

/**
 * Remove a control from the map. `control` should be a control returned by `addControl()`.
 *
 * @param {HTMLElement} control - One of the controls returned by `addControl()`.
 * @returns {HTMLElement} the removed control.
 */
GMaps.prototype.removeControl = function(control) {
  var position = null,
      i;

  for (i = 0; i < this.controls.length; i++) {
    if (this.controls[i] == control) {
      position = this.controls[i].position;
      this.controls.splice(i, 1);
    }
  }

  if (position) {
    for (i = 0; i < this.map.controls.length; i++) {
      var controlsForPosition = this.map.controls[control.position];

      if (controlsForPosition.getAt(i) == control) {
        controlsForPosition.removeAt(i);

        break;
      }
    }
  }

  return control;
};

GMaps.prototype.createMarker = function(options) {
  if (options.lat == undefined && options.lng == undefined && options.position == undefined) {
    throw 'No latitude or longitude defined.';
  }

  var self = this,
      details = options.details,
      fences = options.fences,
      outside = options.outside,
      base_options = {
        position: new google.maps.LatLng(options.lat, options.lng),
        map: null
      },
      marker_options = extend_object(base_options, options);

  delete marker_options.lat;
  delete marker_options.lng;
  delete marker_options.fences;
  delete marker_options.outside;

  var marker = new google.maps.Marker(marker_options);

  marker.fences = fences;

  if (options.infoWindow) {
    marker.infoWindow = new google.maps.InfoWindow(options.infoWindow);

    var info_window_events = ['closeclick', 'content_changed', 'domready', 'position_changed', 'zindex_changed'];

    for (var ev = 0; ev < info_window_events.length; ev++) {
      (function(object, name) {
        if (options.infoWindow[name]) {
          google.maps.event.addListener(object, name, function(e){
            options.infoWindow[name].apply(this, [e]);
          });
        }
      })(marker.infoWindow, info_window_events[ev]);
    }
  }

  var marker_events = ['animation_changed', 'clickable_changed', 'cursor_changed', 'draggable_changed', 'flat_changed', 'icon_changed', 'position_changed', 'shadow_changed', 'shape_changed', 'title_changed', 'visible_changed', 'zindex_changed'];

  var marker_events_with_mouse = ['dblclick', 'drag', 'dragend', 'dragstart', 'mousedown', 'mouseout', 'mouseover', 'mouseup'];

  for (var ev = 0; ev < marker_events.length; ev++) {
    (function(object, name) {
      if (options[name]) {
        google.maps.event.addListener(object, name, function(){
          options[name].apply(this, [this]);
        });
      }
    })(marker, marker_events[ev]);
  }

  for (var ev = 0; ev < marker_events_with_mouse.length; ev++) {
    (function(map, object, name) {
      if (options[name]) {
        google.maps.event.addListener(object, name, function(me){
          if(!me.pixel){
            me.pixel = map.getProjection().fromLatLngToPoint(me.latLng)
          }

          options[name].apply(this, [me]);
        });
      }
    })(this.map, marker, marker_events_with_mouse[ev]);
  }

  google.maps.event.addListener(marker, 'click', function() {
    this.details = details;

    if (options.click) {
      options.click.apply(this, [this]);
    }

    if (marker.infoWindow) {
      self.hideInfoWindows();
      marker.infoWindow.open(self.map, marker);
    }
  });

  google.maps.event.addListener(marker, 'rightclick', function(e) {
    e.marker = this;

    if (options.rightclick) {
      options.rightclick.apply(this, [e]);
    }

    if (window.context_menu[self.el.id]['marker'] != undefined) {
      self.buildContextMenu('marker', e);
    }
  });

  if (marker.fences) {
    google.maps.event.addListener(marker, 'dragend', function() {
      self.checkMarkerGeofence(marker, function(m, f) {
        outside(m, f);
      });
    });
  }

  return marker;
};

GMaps.prototype.addMarker = function(options) {
  var marker;
  if(options.hasOwnProperty('gm_accessors_')) {
    // Native google.maps.Marker object
    marker = options;
  }
  else {
    if ((options.hasOwnProperty('lat') && options.hasOwnProperty('lng')) || options.position) {
      marker = this.createMarker(options);
    }
    else {
      throw 'No latitude or longitude defined.';
    }
  }

  marker.setMap(this.map);

  if(this.markerClusterer) {
    this.markerClusterer.addMarker(marker);
  }

  this.markers.push(marker);

  GMaps.fire('marker_added', marker, this);

  return marker;
};

GMaps.prototype.addMarkers = function(array) {
  for (var i = 0, marker; marker=array[i]; i++) {
    this.addMarker(marker);
  }

  return this.markers;
};

GMaps.prototype.hideInfoWindows = function() {
  for (var i = 0, marker; marker = this.markers[i]; i++){
    if (marker.infoWindow) {
      marker.infoWindow.close();
    }
  }
};

GMaps.prototype.removeMarker = function(marker) {
  for (var i = 0; i < this.markers.length; i++) {
    if (this.markers[i] === marker) {
      this.markers[i].setMap(null);
      this.markers.splice(i, 1);

      if(this.markerClusterer) {
        this.markerClusterer.removeMarker(marker);
      }

      GMaps.fire('marker_removed', marker, this);

      break;
    }
  }

  return marker;
};

GMaps.prototype.removeMarkers = function (collection) {
  var new_markers = [];

  if (typeof collection == 'undefined') {
    for (var i = 0; i < this.markers.length; i++) {
      var marker = this.markers[i];
      marker.setMap(null);

      GMaps.fire('marker_removed', marker, this);
    }

    if(this.markerClusterer && this.markerClusterer.clearMarkers) {
      this.markerClusterer.clearMarkers();
    }

    this.markers = new_markers;
  }
  else {
    for (var i = 0; i < collection.length; i++) {
      var index = this.markers.indexOf(collection[i]);

      if (index > -1) {
        var marker = this.markers[index];
        marker.setMap(null);

        if(this.markerClusterer) {
          this.markerClusterer.removeMarker(marker);
        }

        GMaps.fire('marker_removed', marker, this);
      }
    }

    for (var i = 0; i < this.markers.length; i++) {
      var marker = this.markers[i];
      if (marker.getMap() != null) {
        new_markers.push(marker);
      }
    }

    this.markers = new_markers;
  }
};

GMaps.prototype.drawOverlay = function(options) {
  var overlay = new google.maps.OverlayView(),
      auto_show = true;

  overlay.setMap(this.map);

  if (options.auto_show != null) {
    auto_show = options.auto_show;
  }

  overlay.onAdd = function() {
    var el = document.createElement('div');

    el.style.borderStyle = "none";
    el.style.borderWidth = "0px";
    el.style.position = "absolute";
    el.style.zIndex = 100;
    el.innerHTML = options.content;

    overlay.el = el;

    if (!options.layer) {
      options.layer = 'overlayLayer';
    }

    var panes = this.getPanes(),
        overlayLayer = panes[options.layer],
        stop_overlay_events = ['contextmenu', 'DOMMouseScroll', 'dblclick', 'mousedown'];

    overlayLayer.appendChild(el);

    for (var ev = 0; ev < stop_overlay_events.length; ev++) {
      (function(object, name) {
        google.maps.event.addDomListener(object, name, function(e){
          if (navigator.userAgent.toLowerCase().indexOf('msie') != -1 && document.all) {
            e.cancelBubble = true;
            e.returnValue = false;
          }
          else {
            e.stopPropagation();
          }
        });
      })(el, stop_overlay_events[ev]);
    }

    if (options.click) {
      panes.overlayMouseTarget.appendChild(overlay.el);
      google.maps.event.addDomListener(overlay.el, 'click', function() {
        options.click.apply(overlay, [overlay]);
      });
    }

    google.maps.event.trigger(this, 'ready');
  };

  overlay.draw = function() {
    var projection = this.getProjection(),
        pixel = projection.fromLatLngToDivPixel(new google.maps.LatLng(options.lat, options.lng));

    options.horizontalOffset = options.horizontalOffset || 0;
    options.verticalOffset = options.verticalOffset || 0;

    var el = overlay.el,
        content = el.children[0],
        content_height = content.clientHeight,
        content_width = content.clientWidth;

    switch (options.verticalAlign) {
      case 'top':
        el.style.top = (pixel.y - content_height + options.verticalOffset) + 'px';
        break;
      default:
      case 'middle':
        el.style.top = (pixel.y - (content_height / 2) + options.verticalOffset) + 'px';
        break;
      case 'bottom':
        el.style.top = (pixel.y + options.verticalOffset) + 'px';
        break;
    }

    switch (options.horizontalAlign) {
      case 'left':
        el.style.left = (pixel.x - content_width + options.horizontalOffset) + 'px';
        break;
      default:
      case 'center':
        el.style.left = (pixel.x - (content_width / 2) + options.horizontalOffset) + 'px';
        break;
      case 'right':
        el.style.left = (pixel.x + options.horizontalOffset) + 'px';
        break;
    }

    el.style.display = auto_show ? 'block' : 'none';

    if (!auto_show) {
      options.show.apply(this, [el]);
    }
  };

  overlay.onRemove = function() {
    var el = overlay.el;

    if (options.remove) {
      options.remove.apply(this, [el]);
    }
    else {
      overlay.el.parentNode.removeChild(overlay.el);
      overlay.el = null;
    }
  };

  this.overlays.push(overlay);
  return overlay;
};

GMaps.prototype.removeOverlay = function(overlay) {
  for (var i = 0; i < this.overlays.length; i++) {
    if (this.overlays[i] === overlay) {
      this.overlays[i].setMap(null);
      this.overlays.splice(i, 1);

      break;
    }
  }
};

GMaps.prototype.removeOverlays = function() {
  for (var i = 0, item; item = this.overlays[i]; i++) {
    item.setMap(null);
  }

  this.overlays = [];
};

GMaps.prototype.drawPolyline = function(options) {
  var path = [],
      points = options.path;

  if (points.length) {
    if (points[0][0] === undefined) {
      path = points;
    }
    else {
      for (var i = 0, latlng; latlng = points[i]; i++) {
        path.push(new google.maps.LatLng(latlng[0], latlng[1]));
      }
    }
  }

  var polyline_options = {
    map: this.map,
    path: path,
    strokeColor: options.strokeColor,
    strokeOpacity: options.strokeOpacity,
    strokeWeight: options.strokeWeight,
    geodesic: options.geodesic,
    clickable: true,
    editable: false,
    visible: true
  };

  if (options.hasOwnProperty("clickable")) {
    polyline_options.clickable = options.clickable;
  }

  if (options.hasOwnProperty("editable")) {
    polyline_options.editable = options.editable;
  }

  if (options.hasOwnProperty("icons")) {
    polyline_options.icons = options.icons;
  }

  if (options.hasOwnProperty("zIndex")) {
    polyline_options.zIndex = options.zIndex;
  }

  var polyline = new google.maps.Polyline(polyline_options);

  var polyline_events = ['click', 'dblclick', 'mousedown', 'mousemove', 'mouseout', 'mouseover', 'mouseup', 'rightclick'];

  for (var ev = 0; ev < polyline_events.length; ev++) {
    (function(object, name) {
      if (options[name]) {
        google.maps.event.addListener(object, name, function(e){
          options[name].apply(this, [e]);
        });
      }
    })(polyline, polyline_events[ev]);
  }

  this.polylines.push(polyline);

  GMaps.fire('polyline_added', polyline, this);

  return polyline;
};

GMaps.prototype.removePolyline = function(polyline) {
  for (var i = 0; i < this.polylines.length; i++) {
    if (this.polylines[i] === polyline) {
      this.polylines[i].setMap(null);
      this.polylines.splice(i, 1);

      GMaps.fire('polyline_removed', polyline, this);

      break;
    }
  }
};

GMaps.prototype.removePolylines = function() {
  for (var i = 0, item; item = this.polylines[i]; i++) {
    item.setMap(null);
  }

  this.polylines = [];
};

GMaps.prototype.drawCircle = function(options) {
  options =  extend_object({
    map: this.map,
    center: new google.maps.LatLng(options.lat, options.lng)
  }, options);

  delete options.lat;
  delete options.lng;

  var polygon = new google.maps.Circle(options),
      polygon_events = ['click', 'dblclick', 'mousedown', 'mousemove', 'mouseout', 'mouseover', 'mouseup', 'rightclick'];

  for (var ev = 0; ev < polygon_events.length; ev++) {
    (function(object, name) {
      if (options[name]) {
        google.maps.event.addListener(object, name, function(e){
          options[name].apply(this, [e]);
        });
      }
    })(polygon, polygon_events[ev]);
  }

  this.polygons.push(polygon);

  return polygon;
};

GMaps.prototype.drawRectangle = function(options) {
  options = extend_object({
    map: this.map
  }, options);

  var latLngBounds = new google.maps.LatLngBounds(
    new google.maps.LatLng(options.bounds[0][0], options.bounds[0][1]),
    new google.maps.LatLng(options.bounds[1][0], options.bounds[1][1])
  );

  options.bounds = latLngBounds;

  var polygon = new google.maps.Rectangle(options),
      polygon_events = ['click', 'dblclick', 'mousedown', 'mousemove', 'mouseout', 'mouseover', 'mouseup', 'rightclick'];

  for (var ev = 0; ev < polygon_events.length; ev++) {
    (function(object, name) {
      if (options[name]) {
        google.maps.event.addListener(object, name, function(e){
          options[name].apply(this, [e]);
        });
      }
    })(polygon, polygon_events[ev]);
  }

  this.polygons.push(polygon);

  return polygon;
};

GMaps.prototype.drawPolygon = function(options) {
  var useGeoJSON = false;

  if(options.hasOwnProperty("useGeoJSON")) {
    useGeoJSON = options.useGeoJSON;
  }

  delete options.useGeoJSON;

  options = extend_object({
    map: this.map
  }, options);

  if (useGeoJSON == false) {
    options.paths = [options.paths.slice(0)];
  }

  if (options.paths.length > 0) {
    if (options.paths[0].length > 0) {
      options.paths = array_flat(array_map(options.paths, arrayToLatLng, useGeoJSON));
    }
  }

  var polygon = new google.maps.Polygon(options),
      polygon_events = ['click', 'dblclick', 'mousedown', 'mousemove', 'mouseout', 'mouseover', 'mouseup', 'rightclick'];

  for (var ev = 0; ev < polygon_events.length; ev++) {
    (function(object, name) {
      if (options[name]) {
        google.maps.event.addListener(object, name, function(e){
          options[name].apply(this, [e]);
        });
      }
    })(polygon, polygon_events[ev]);
  }

  this.polygons.push(polygon);

  GMaps.fire('polygon_added', polygon, this);

  return polygon;
};

GMaps.prototype.removePolygon = function(polygon) {
  for (var i = 0; i < this.polygons.length; i++) {
    if (this.polygons[i] === polygon) {
      this.polygons[i].setMap(null);
      this.polygons.splice(i, 1);

      GMaps.fire('polygon_removed', polygon, this);

      break;
    }
  }
};

GMaps.prototype.removePolygons = function() {
  for (var i = 0, item; item = this.polygons[i]; i++) {
    item.setMap(null);
  }

  this.polygons = [];
};

GMaps.prototype.getFromFusionTables = function(options) {
  var events = options.events;

  delete options.events;

  var fusion_tables_options = options,
      layer = new google.maps.FusionTablesLayer(fusion_tables_options);

  for (var ev in events) {
    (function(object, name) {
      google.maps.event.addListener(object, name, function(e) {
        events[name].apply(this, [e]);
      });
    })(layer, ev);
  }

  this.layers.push(layer);

  return layer;
};

GMaps.prototype.loadFromFusionTables = function(options) {
  var layer = this.getFromFusionTables(options);
  layer.setMap(this.map);

  return layer;
};

GMaps.prototype.getFromKML = function(options) {
  var url = options.url,
      events = options.events;

  delete options.url;
  delete options.events;

  var kml_options = options,
      layer = new google.maps.KmlLayer(url, kml_options);

  for (var ev in events) {
    (function(object, name) {
      google.maps.event.addListener(object, name, function(e) {
        events[name].apply(this, [e]);
      });
    })(layer, ev);
  }

  this.layers.push(layer);

  return layer;
};

GMaps.prototype.loadFromKML = function(options) {
  var layer = this.getFromKML(options);
  layer.setMap(this.map);

  return layer;
};

GMaps.prototype.addLayer = function(layerName, options) {
  //var default_layers = ['weather', 'clouds', 'traffic', 'transit', 'bicycling', 'panoramio', 'places'];
  options = options || {};
  var layer;

  switch(layerName) {
    case 'weather': this.singleLayers.weather = layer = new google.maps.weather.WeatherLayer();
      break;
    case 'clouds': this.singleLayers.clouds = layer = new google.maps.weather.CloudLayer();
      break;
    case 'traffic': this.singleLayers.traffic = layer = new google.maps.TrafficLayer();
      break;
    case 'transit': this.singleLayers.transit = layer = new google.maps.TransitLayer();
      break;
    case 'bicycling': this.singleLayers.bicycling = layer = new google.maps.BicyclingLayer();
      break;
    case 'panoramio':
        this.singleLayers.panoramio = layer = new google.maps.panoramio.PanoramioLayer();
        layer.setTag(options.filter);
        delete options.filter;

        //click event
        if (options.click) {
          google.maps.event.addListener(layer, 'click', function(event) {
            options.click(event);
            delete options.click;
          });
        }
      break;
      case 'places':
        this.singleLayers.places = layer = new google.maps.places.PlacesService(this.map);

        //search, nearbySearch, radarSearch callback, Both are the same
        if (options.search || options.nearbySearch || options.radarSearch) {
          var placeSearchRequest  = {
            bounds : options.bounds || null,
            keyword : options.keyword || null,
            location : options.location || null,
            name : options.name || null,
            radius : options.radius || null,
            rankBy : options.rankBy || null,
            types : options.types || null
          };

          if (options.radarSearch) {
            layer.radarSearch(placeSearchRequest, options.radarSearch);
          }

          if (options.search) {
            layer.search(placeSearchRequest, options.search);
          }

          if (options.nearbySearch) {
            layer.nearbySearch(placeSearchRequest, options.nearbySearch);
          }
        }

        //textSearch callback
        if (options.textSearch) {
          var textSearchRequest  = {
            bounds : options.bounds || null,
            location : options.location || null,
            query : options.query || null,
            radius : options.radius || null
          };

          layer.textSearch(textSearchRequest, options.textSearch);
        }
      break;
  }

  if (layer !== undefined) {
    if (typeof layer.setOptions == 'function') {
      layer.setOptions(options);
    }
    if (typeof layer.setMap == 'function') {
      layer.setMap(this.map);
    }

    return layer;
  }
};

GMaps.prototype.removeLayer = function(layer) {
  if (typeof(layer) == "string" && this.singleLayers[layer] !== undefined) {
     this.singleLayers[layer].setMap(null);

     delete this.singleLayers[layer];
  }
  else {
    for (var i = 0; i < this.layers.length; i++) {
      if (this.layers[i] === layer) {
        this.layers[i].setMap(null);
        this.layers.splice(i, 1);

        break;
      }
    }
  }
};

var travelMode, unitSystem;

GMaps.prototype.getRoutes = function(options) {
  switch (options.travelMode) {
    case 'bicycling':
      travelMode = google.maps.TravelMode.BICYCLING;
      break;
    case 'transit':
      travelMode = google.maps.TravelMode.TRANSIT;
      break;
    case 'driving':
      travelMode = google.maps.TravelMode.DRIVING;
      break;
    default:
      travelMode = google.maps.TravelMode.WALKING;
      break;
  }

  if (options.unitSystem === 'imperial') {
    unitSystem = google.maps.UnitSystem.IMPERIAL;
  }
  else {
    unitSystem = google.maps.UnitSystem.METRIC;
  }

  var base_options = {
        avoidHighways: false,
        avoidTolls: false,
        optimizeWaypoints: false,
        waypoints: []
      },
      request_options =  extend_object(base_options, options);

  request_options.origin = /string/.test(typeof options.origin) ? options.origin : new google.maps.LatLng(options.origin[0], options.origin[1]);
  request_options.destination = /string/.test(typeof options.destination) ? options.destination : new google.maps.LatLng(options.destination[0], options.destination[1]);
  request_options.travelMode = travelMode;
  request_options.unitSystem = unitSystem;

  delete request_options.callback;
  delete request_options.error;

  var self = this,
      routes = [],
      service = new google.maps.DirectionsService();

  service.route(request_options, function(result, status) {
    if (status === google.maps.DirectionsStatus.OK) {
      for (var r in result.routes) {
        if (result.routes.hasOwnProperty(r)) {
          routes.push(result.routes[r]);
        }
      }

      if (options.callback) {
        options.callback(routes, result, status);
      }
    }
    else {
      if (options.error) {
        options.error(result, status);
      }
    }
  });
};

GMaps.prototype.removeRoutes = function() {
  this.routes.length = 0;
};

GMaps.prototype.getElevations = function(options) {
  options = extend_object({
    locations: [],
    path : false,
    samples : 256
  }, options);

  if (options.locations.length > 0) {
    if (options.locations[0].length > 0) {
      options.locations = array_flat(array_map([options.locations], arrayToLatLng,  false));
    }
  }

  var callback = options.callback;
  delete options.callback;

  var service = new google.maps.ElevationService();

  //location request
  if (!options.path) {
    delete options.path;
    delete options.samples;

    service.getElevationForLocations(options, function(result, status) {
      if (callback && typeof(callback) === "function") {
        callback(result, status);
      }
    });
  //path request
  } else {
    var pathRequest = {
      path : options.locations,
      samples : options.samples
    };

    service.getElevationAlongPath(pathRequest, function(result, status) {
     if (callback && typeof(callback) === "function") {
        callback(result, status);
      }
    });
  }
};

GMaps.prototype.cleanRoute = GMaps.prototype.removePolylines;

GMaps.prototype.renderRoute = function(options, renderOptions) {
  var self = this,
      panel = ((typeof renderOptions.panel === 'string') ? document.getElementById(renderOptions.panel.replace('#', '')) : renderOptions.panel),
      display;

  renderOptions.panel = panel;
  renderOptions = extend_object({
    map: this.map
  }, renderOptions);
  display = new google.maps.DirectionsRenderer(renderOptions);

  this.getRoutes({
    origin: options.origin,
    destination: options.destination,
    travelMode: options.travelMode,
    waypoints: options.waypoints,
    unitSystem: options.unitSystem,
    error: options.error,
    avoidHighways: options.avoidHighways,
    avoidTolls: options.avoidTolls,
    optimizeWaypoints: options.optimizeWaypoints,
    callback: function(routes, response, status) {
      if (status === google.maps.DirectionsStatus.OK) {
        display.setDirections(response);
      }
    }
  });
};

GMaps.prototype.drawRoute = function(options) {
  var self = this;

  this.getRoutes({
    origin: options.origin,
    destination: options.destination,
    travelMode: options.travelMode,
    waypoints: options.waypoints,
    unitSystem: options.unitSystem,
    error: options.error,
    avoidHighways: options.avoidHighways,
    avoidTolls: options.avoidTolls,
    optimizeWaypoints: options.optimizeWaypoints,
    callback: function(routes) {
      if (routes.length > 0) {
        var polyline_options = {
          path: routes[routes.length - 1].overview_path,
          strokeColor: options.strokeColor,
          strokeOpacity: options.strokeOpacity,
          strokeWeight: options.strokeWeight
        };

        if (options.hasOwnProperty("icons")) {
          polyline_options.icons = options.icons;
        }

        self.drawPolyline(polyline_options);

        if (options.callback) {
          options.callback(routes[routes.length - 1]);
        }
      }
    }
  });
};

GMaps.prototype.travelRoute = function(options) {
  if (options.origin && options.destination) {
    this.getRoutes({
      origin: options.origin,
      destination: options.destination,
      travelMode: options.travelMode,
      waypoints : options.waypoints,
      unitSystem: options.unitSystem,
      error: options.error,
      callback: function(e) {
        //start callback
        if (e.length > 0 && options.start) {
          options.start(e[e.length - 1]);
        }

        //step callback
        if (e.length > 0 && options.step) {
          var route = e[e.length - 1];
          if (route.legs.length > 0) {
            var steps = route.legs[0].steps;
            for (var i = 0, step; step = steps[i]; i++) {
              step.step_number = i;
              options.step(step, (route.legs[0].steps.length - 1));
            }
          }
        }

        //end callback
        if (e.length > 0 && options.end) {
           options.end(e[e.length - 1]);
        }
      }
    });
  }
  else if (options.route) {
    if (options.route.legs.length > 0) {
      var steps = options.route.legs[0].steps;
      for (var i = 0, step; step = steps[i]; i++) {
        step.step_number = i;
        options.step(step);
      }
    }
  }
};

GMaps.prototype.drawSteppedRoute = function(options) {
  var self = this;

  if (options.origin && options.destination) {
    this.getRoutes({
      origin: options.origin,
      destination: options.destination,
      travelMode: options.travelMode,
      waypoints : options.waypoints,
      error: options.error,
      callback: function(e) {
        //start callback
        if (e.length > 0 && options.start) {
          options.start(e[e.length - 1]);
        }

        //step callback
        if (e.length > 0 && options.step) {
          var route = e[e.length - 1];
          if (route.legs.length > 0) {
            var steps = route.legs[0].steps;
            for (var i = 0, step; step = steps[i]; i++) {
              step.step_number = i;
              var polyline_options = {
                path: step.path,
                strokeColor: options.strokeColor,
                strokeOpacity: options.strokeOpacity,
                strokeWeight: options.strokeWeight
              };

              if (options.hasOwnProperty("icons")) {
                polyline_options.icons = options.icons;
              }

              self.drawPolyline(polyline_options);
              options.step(step, (route.legs[0].steps.length - 1));
            }
          }
        }

        //end callback
        if (e.length > 0 && options.end) {
           options.end(e[e.length - 1]);
        }
      }
    });
  }
  else if (options.route) {
    if (options.route.legs.length > 0) {
      var steps = options.route.legs[0].steps;
      for (var i = 0, step; step = steps[i]; i++) {
        step.step_number = i;
        var polyline_options = {
          path: step.path,
          strokeColor: options.strokeColor,
          strokeOpacity: options.strokeOpacity,
          strokeWeight: options.strokeWeight
        };

        if (options.hasOwnProperty("icons")) {
          polyline_options.icons = options.icons;
        }

        self.drawPolyline(polyline_options);
        options.step(step);
      }
    }
  }
};

GMaps.Route = function(options) {
  this.origin = options.origin;
  this.destination = options.destination;
  this.waypoints = options.waypoints;

  this.map = options.map;
  this.route = options.route;
  this.step_count = 0;
  this.steps = this.route.legs[0].steps;
  this.steps_length = this.steps.length;

  var polyline_options = {
    path: new google.maps.MVCArray(),
    strokeColor: options.strokeColor,
    strokeOpacity: options.strokeOpacity,
    strokeWeight: options.strokeWeight
  };

  if (options.hasOwnProperty("icons")) {
    polyline_options.icons = options.icons;
  }

  this.polyline = this.map.drawPolyline(polyline_options).getPath();
};

GMaps.Route.prototype.getRoute = function(options) {
  var self = this;

  this.map.getRoutes({
    origin : this.origin,
    destination : this.destination,
    travelMode : options.travelMode,
    waypoints : this.waypoints || [],
    error: options.error,
    callback : function() {
      self.route = e[0];

      if (options.callback) {
        options.callback.call(self);
      }
    }
  });
};

GMaps.Route.prototype.back = function() {
  if (this.step_count > 0) {
    this.step_count--;
    var path = this.route.legs[0].steps[this.step_count].path;

    for (var p in path){
      if (path.hasOwnProperty(p)){
        this.polyline.pop();
      }
    }
  }
};

GMaps.Route.prototype.forward = function() {
  if (this.step_count < this.steps_length) {
    var path = this.route.legs[0].steps[this.step_count].path;

    for (var p in path){
      if (path.hasOwnProperty(p)){
        this.polyline.push(path[p]);
      }
    }
    this.step_count++;
  }
};

GMaps.prototype.checkGeofence = function(lat, lng, fence) {
  return fence.containsLatLng(new google.maps.LatLng(lat, lng));
};

GMaps.prototype.checkMarkerGeofence = function(marker, outside_callback) {
  if (marker.fences) {
    for (var i = 0, fence; fence = marker.fences[i]; i++) {
      var pos = marker.getPosition();
      if (!this.checkGeofence(pos.lat(), pos.lng(), fence)) {
        outside_callback(marker, fence);
      }
    }
  }
};

GMaps.prototype.toImage = function(options) {
  var options = options || {},
      static_map_options = {};

  static_map_options['size'] = options['size'] || [this.el.clientWidth, this.el.clientHeight];
  static_map_options['lat'] = this.getCenter().lat();
  static_map_options['lng'] = this.getCenter().lng();

  if (this.markers.length > 0) {
    static_map_options['markers'] = [];

    for (var i = 0; i < this.markers.length; i++) {
      static_map_options['markers'].push({
        lat: this.markers[i].getPosition().lat(),
        lng: this.markers[i].getPosition().lng()
      });
    }
  }

  if (this.polylines.length > 0) {
    var polyline = this.polylines[0];

    static_map_options['polyline'] = {};
    static_map_options['polyline']['path'] = google.maps.geometry.encoding.encodePath(polyline.getPath());
    static_map_options['polyline']['strokeColor'] = polyline.strokeColor
    static_map_options['polyline']['strokeOpacity'] = polyline.strokeOpacity
    static_map_options['polyline']['strokeWeight'] = polyline.strokeWeight
  }

  return GMaps.staticMapURL(static_map_options);
};

GMaps.staticMapURL = function(options){
  var parameters = [],
      data,
      static_root = (location.protocol === 'file:' ? 'http:' : location.protocol ) + '//maps.googleapis.com/maps/api/staticmap';

  if (options.url) {
    static_root = options.url;
    delete options.url;
  }

  static_root += '?';

  var markers = options.markers;

  delete options.markers;

  if (!markers && options.marker) {
    markers = [options.marker];
    delete options.marker;
  }

  var styles = options.styles;

  delete options.styles;

  var polyline = options.polyline;
  delete options.polyline;

  /** Map options **/
  if (options.center) {
    parameters.push('center=' + options.center);
    delete options.center;
  }
  else if (options.address) {
    parameters.push('center=' + options.address);
    delete options.address;
  }
  else if (options.lat) {
    parameters.push(['center=', options.lat, ',', options.lng].join(''));
    delete options.lat;
    delete options.lng;
  }
  else if (options.visible) {
    var visible = encodeURI(options.visible.join('|'));
    parameters.push('visible=' + visible);
  }

  var size = options.size;
  if (size) {
    if (size.join) {
      size = size.join('x');
    }
    delete options.size;
  }
  else {
    size = '630x300';
  }
  parameters.push('size=' + size);

  if (!options.zoom && options.zoom !== false) {
    options.zoom = 15;
  }

  var sensor = options.hasOwnProperty('sensor') ? !!options.sensor : true;
  delete options.sensor;
  parameters.push('sensor=' + sensor);

  for (var param in options) {
    if (options.hasOwnProperty(param)) {
      parameters.push(param + '=' + options[param]);
    }
  }

  /** Markers **/
  if (markers) {
    var marker, loc;

    for (var i = 0; data = markers[i]; i++) {
      marker = [];

      if (data.size && data.size !== 'normal') {
        marker.push('size:' + data.size);
        delete data.size;
      }
      else if (data.icon) {
        marker.push('icon:' + encodeURI(data.icon));
        delete data.icon;
      }

      if (data.color) {
        marker.push('color:' + data.color.replace('#', '0x'));
        delete data.color;
      }

      if (data.label) {
        marker.push('label:' + data.label[0].toUpperCase());
        delete data.label;
      }

      loc = (data.address ? data.address : data.lat + ',' + data.lng);
      delete data.address;
      delete data.lat;
      delete data.lng;

      for(var param in data){
        if (data.hasOwnProperty(param)) {
          marker.push(param + ':' + data[param]);
        }
      }

      if (marker.length || i === 0) {
        marker.push(loc);
        marker = marker.join('|');
        parameters.push('markers=' + encodeURI(marker));
      }
      // New marker without styles
      else {
        marker = parameters.pop() + encodeURI('|' + loc);
        parameters.push(marker);
      }
    }
  }

  /** Map Styles **/
  if (styles) {
    for (var i = 0; i < styles.length; i++) {
      var styleRule = [];
      if (styles[i].featureType){
        styleRule.push('feature:' + styles[i].featureType.toLowerCase());
      }

      if (styles[i].elementType) {
        styleRule.push('element:' + styles[i].elementType.toLowerCase());
      }

      for (var j = 0; j < styles[i].stylers.length; j++) {
        for (var p in styles[i].stylers[j]) {
          var ruleArg = styles[i].stylers[j][p];
          if (p == 'hue' || p == 'color') {
            ruleArg = '0x' + ruleArg.substring(1);
          }
          styleRule.push(p + ':' + ruleArg);
        }
      }

      var rule = styleRule.join('|');
      if (rule != '') {
        parameters.push('style=' + rule);
      }
    }
  }

  /** Polylines **/
  function parseColor(color, opacity) {
    if (color[0] === '#'){
      color = color.replace('#', '0x');

      if (opacity) {
        opacity = parseFloat(opacity);
        opacity = Math.min(1, Math.max(opacity, 0));
        if (opacity === 0) {
          return '0x00000000';
        }
        opacity = (opacity * 255).toString(16);
        if (opacity.length === 1) {
          opacity += opacity;
        }

        color = color.slice(0,8) + opacity;
      }
    }
    return color;
  }

  if (polyline) {
    data = polyline;
    polyline = [];

    if (data.strokeWeight) {
      polyline.push('weight:' + parseInt(data.strokeWeight, 10));
    }

    if (data.strokeColor) {
      var color = parseColor(data.strokeColor, data.strokeOpacity);
      polyline.push('color:' + color);
    }

    if (data.fillColor) {
      var fillcolor = parseColor(data.fillColor, data.fillOpacity);
      polyline.push('fillcolor:' + fillcolor);
    }

    var path = data.path;
    if (path.join) {
      for (var j=0, pos; pos=path[j]; j++) {
        polyline.push(pos.join(','));
      }
    }
    else {
      polyline.push('enc:' + path);
    }

    polyline = polyline.join('|');
    parameters.push('path=' + encodeURI(polyline));
  }

  /** Retina support **/
  var dpi = window.devicePixelRatio || 1;
  parameters.push('scale=' + dpi);

  parameters = parameters.join('&');
  return static_root + parameters;
};

GMaps.prototype.addMapType = function(mapTypeId, options) {
  if (options.hasOwnProperty("getTileUrl") && typeof(options["getTileUrl"]) == "function") {
    options.tileSize = options.tileSize || new google.maps.Size(256, 256);

    var mapType = new google.maps.ImageMapType(options);

    this.map.mapTypes.set(mapTypeId, mapType);
  }
  else {
    throw "'getTileUrl' function required.";
  }
};

GMaps.prototype.addOverlayMapType = function(options) {
  if (options.hasOwnProperty("getTile") && typeof(options["getTile"]) == "function") {
    var overlayMapTypeIndex = options.index;

    delete options.index;

    this.map.overlayMapTypes.insertAt(overlayMapTypeIndex, options);
  }
  else {
    throw "'getTile' function required.";
  }
};

GMaps.prototype.removeOverlayMapType = function(overlayMapTypeIndex) {
  this.map.overlayMapTypes.removeAt(overlayMapTypeIndex);
};

GMaps.prototype.addStyle = function(options) {
  var styledMapType = new google.maps.StyledMapType(options.styles, { name: options.styledMapName });

  this.map.mapTypes.set(options.mapTypeId, styledMapType);
};

GMaps.prototype.setStyle = function(mapTypeId) {
  this.map.setMapTypeId(mapTypeId);
};

GMaps.prototype.createPanorama = function(streetview_options) {
  if (!streetview_options.hasOwnProperty('lat') || !streetview_options.hasOwnProperty('lng')) {
    streetview_options.lat = this.getCenter().lat();
    streetview_options.lng = this.getCenter().lng();
  }

  this.panorama = GMaps.createPanorama(streetview_options);

  this.map.setStreetView(this.panorama);

  return this.panorama;
};

GMaps.createPanorama = function(options) {
  var el = getElementById(options.el, options.context);

  options.position = new google.maps.LatLng(options.lat, options.lng);

  delete options.el;
  delete options.context;
  delete options.lat;
  delete options.lng;

  var streetview_events = ['closeclick', 'links_changed', 'pano_changed', 'position_changed', 'pov_changed', 'resize', 'visible_changed'],
      streetview_options = extend_object({visible : true}, options);

  for (var i = 0; i < streetview_events.length; i++) {
    delete streetview_options[streetview_events[i]];
  }

  var panorama = new google.maps.StreetViewPanorama(el, streetview_options);

  for (var i = 0; i < streetview_events.length; i++) {
    (function(object, name) {
      if (options[name]) {
        google.maps.event.addListener(object, name, function(){
          options[name].apply(this);
        });
      }
    })(panorama, streetview_events[i]);
  }

  return panorama;
};

GMaps.prototype.on = function(event_name, handler) {
  return GMaps.on(event_name, this, handler);
};

GMaps.prototype.off = function(event_name) {
  GMaps.off(event_name, this);
};

GMaps.prototype.once = function(event_name, handler) {
  return GMaps.once(event_name, this, handler);
};

GMaps.custom_events = ['marker_added', 'marker_removed', 'polyline_added', 'polyline_removed', 'polygon_added', 'polygon_removed', 'geolocated', 'geolocation_failed'];

GMaps.on = function(event_name, object, handler) {
  if (GMaps.custom_events.indexOf(event_name) == -1) {
    if(object instanceof GMaps) object = object.map;
    return google.maps.event.addListener(object, event_name, handler);
  }
  else {
    var registered_event = {
      handler : handler,
      eventName : event_name
    };

    object.registered_events[event_name] = object.registered_events[event_name] || [];
    object.registered_events[event_name].push(registered_event);

    return registered_event;
  }
};

GMaps.off = function(event_name, object) {
  if (GMaps.custom_events.indexOf(event_name) == -1) {
    if(object instanceof GMaps) object = object.map;
    google.maps.event.clearListeners(object, event_name);
  }
  else {
    object.registered_events[event_name] = [];
  }
};

GMaps.once = function(event_name, object, handler) {
  if (GMaps.custom_events.indexOf(event_name) == -1) {
    if(object instanceof GMaps) object = object.map;
    return google.maps.event.addListenerOnce(object, event_name, handler);
  }
};

GMaps.fire = function(event_name, object, scope) {
  if (GMaps.custom_events.indexOf(event_name) == -1) {
    google.maps.event.trigger(object, event_name, Array.prototype.slice.apply(arguments).slice(2));
  }
  else {
    if(event_name in scope.registered_events) {
      var firing_events = scope.registered_events[event_name];

      for(var i = 0; i < firing_events.length; i++) {
        (function(handler, scope, object) {
          handler.apply(scope, [object]);
        })(firing_events[i]['handler'], scope, object);
      }
    }
  }
};

GMaps.geolocate = function(options) {
  var complete_callback = options.always || options.complete;

  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(function(position) {
      options.success(position);

      if (complete_callback) {
        complete_callback();
      }
    }, function(error) {
      options.error(error);

      if (complete_callback) {
        complete_callback();
      }
    }, options.options);
  }
  else {
    options.not_supported();

    if (complete_callback) {
      complete_callback();
    }
  }
};

GMaps.geocode = function(options) {
  this.geocoder = new google.maps.Geocoder();
  var callback = options.callback;
  if (options.hasOwnProperty('lat') && options.hasOwnProperty('lng')) {
    options.latLng = new google.maps.LatLng(options.lat, options.lng);
  }

  delete options.lat;
  delete options.lng;
  delete options.callback;

  this.geocoder.geocode(options, function(results, status) {
    callback(results, status);
  });
};

if (typeof window.google === 'object' && window.google.maps) {
  //==========================
  // Polygon containsLatLng
  // https://github.com/tparkin/Google-Maps-Point-in-Polygon
  // Poygon getBounds extension - google-maps-extensions
  // http://code.google.com/p/google-maps-extensions/source/browse/google.maps.Polygon.getBounds.js
  if (!google.maps.Polygon.prototype.getBounds) {
    google.maps.Polygon.prototype.getBounds = function(latLng) {
      var bounds = new google.maps.LatLngBounds();
      var paths = this.getPaths();
      var path;

      for (var p = 0; p < paths.getLength(); p++) {
        path = paths.getAt(p);
        for (var i = 0; i < path.getLength(); i++) {
          bounds.extend(path.getAt(i));
        }
      }

      return bounds;
    };
  }

  if (!google.maps.Polygon.prototype.containsLatLng) {
    // Polygon containsLatLng - method to determine if a latLng is within a polygon
    google.maps.Polygon.prototype.containsLatLng = function(latLng) {
      // Exclude points outside of bounds as there is no way they are in the poly
      var bounds = this.getBounds();

      if (bounds !== null && !bounds.contains(latLng)) {
        return false;
      }

      // Raycast point in polygon method
      var inPoly = false;

      var numPaths = this.getPaths().getLength();
      for (var p = 0; p < numPaths; p++) {
        var path = this.getPaths().getAt(p);
        var numPoints = path.getLength();
        var j = numPoints - 1;

        for (var i = 0; i < numPoints; i++) {
          var vertex1 = path.getAt(i);
          var vertex2 = path.getAt(j);

          if (vertex1.lng() < latLng.lng() && vertex2.lng() >= latLng.lng() || vertex2.lng() < latLng.lng() && vertex1.lng() >= latLng.lng()) {
            if (vertex1.lat() + (latLng.lng() - vertex1.lng()) / (vertex2.lng() - vertex1.lng()) * (vertex2.lat() - vertex1.lat()) < latLng.lat()) {
              inPoly = !inPoly;
            }
          }

          j = i;
        }
      }

      return inPoly;
    };
  }

  if (!google.maps.Circle.prototype.containsLatLng) {
    google.maps.Circle.prototype.containsLatLng = function(latLng) {
      if (google.maps.geometry) {
        return google.maps.geometry.spherical.computeDistanceBetween(this.getCenter(), latLng) <= this.getRadius();
      }
      else {
        return true;
      }
    };
  }

  google.maps.Rectangle.prototype.containsLatLng = function(latLng) {
    return this.getBounds().contains(latLng);
  };

  google.maps.LatLngBounds.prototype.containsLatLng = function(latLng) {
    return this.contains(latLng);
  };

  google.maps.Marker.prototype.setFences = function(fences) {
    this.fences = fences;
  };

  google.maps.Marker.prototype.addFence = function(fence) {
    this.fences.push(fence);
  };

  google.maps.Marker.prototype.getId = function() {
    return this['__gm_id'];
  };
}

//==========================
// Array indexOf
// https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Global_Objects/Array/indexOf
if (!Array.prototype.indexOf) {
  Array.prototype.indexOf = function (searchElement /*, fromIndex */ ) {
      "use strict";
      if (this == null) {
          throw new TypeError();
      }
      var t = Object(this);
      var len = t.length >>> 0;
      if (len === 0) {
          return -1;
      }
      var n = 0;
      if (arguments.length > 1) {
          n = Number(arguments[1]);
          if (n != n) { // shortcut for verifying if it's NaN
              n = 0;
          } else if (n != 0 && n != Infinity && n != -Infinity) {
              n = (n > 0 || -1) * Math.floor(Math.abs(n));
          }
      }
      if (n >= len) {
          return -1;
      }
      var k = n >= 0 ? n : Math.max(len - Math.abs(n), 0);
      for (; k < len; k++) {
          if (k in t && t[k] === searchElement) {
              return k;
          }
      }
      return -1;
  }
}

return GMaps;
}));
The file gmaps.min.js contains:
"use strict";!function(a,b){"object"==typeof exports?module.exports=b():"function"==typeof define&&define.amd?define(["jquery","googlemaps!"],b):a.GMaps=b()}(this,function(){var a=function(a,b){var c;if(a===b)return a;for(c in b)void 0!==b[c]&&(a[c]=b[c]);return a},b=function(a,b){var c,d=Array.prototype.slice.call(arguments,2),e=[],f=a.length;if(Array.prototype.map&&a.map===Array.prototype.map)e=Array.prototype.map.call(a,function(a){var c=d.slice(0);return c.splice(0,0,a),b.apply(this,c)});else for(c=0;c<f;c++)callback_params=d,callback_params.splice(0,0,a[c]),e.push(b.apply(this,callback_params));return e},c=function(a){var b,c=[];for(b=0;b<a.length;b++)c=c.concat(a[b]);return c},d=function(a,b){var c=a[0],d=a[1];return b&&(c=a[1],d=a[0]),new google.maps.LatLng(c,d)},f=function(a,b){var c;for(c=0;c<a.length;c++)a[c]instanceof google.maps.LatLng||(a[c].length>0&&"object"==typeof a[c][0]?a[c]=f(a[c],b):a[c]=d(a[c],b));return a},g=function(a,b){var c=a.replace(".","");return"jQuery"in this&&b?$("."+c,b)[0]:document.getElementsByClassName(c)[0]},h=function(a,b){var a=a.replace("#","");return"jQuery"in window&&b?$("#"+a,b)[0]:document.getElementById(a)},i=function(a){var b=0,c=0;if(a.getBoundingClientRect){var d=a.getBoundingClientRect(),e=-(window.scrollX?window.scrollX:window.pageXOffset),f=-(window.scrollY?window.scrollY:window.pageYOffset);return[d.left-e,d.top-f]}if(a.offsetParent)do b+=a.offsetLeft,c+=a.offsetTop;while(a=a.offsetParent);return[b,c]},j=function(b){var c=document,d=function(b){if("object"!=typeof window.google||!window.google.maps)return"object"==typeof window.console&&window.console.error&&console.error("Google Maps API is required. Please register the following JavaScript library https://maps.googleapis.com/maps/api/js."),function(){};if(!this)return new d(b);b.zoom=b.zoom||15,b.mapType=b.mapType||"roadmap";var e,f=function(a,b){return void 0===a?b:a},j=this,k=["bounds_changed","center_changed","click","dblclick","drag","dragend","dragstart","idle","maptypeid_changed","projection_changed","resize","tilesloaded","zoom_changed"],l=["mousemove","mouseout","mouseover"],m=["el","lat","lng","mapType","width","height","markerClusterer","enableNewStyle"],n=b.el||b.div,o=b.markerClusterer,p=google.maps.MapTypeId[b.mapType.toUpperCase()],q=new google.maps.LatLng(b.lat,b.lng),r=f(b.zoomControl,!0),s=b.zoomControlOpt||{style:"DEFAULT",position:"TOP_LEFT"},t=s.style||"DEFAULT",u=s.position||"TOP_LEFT",v=f(b.panControl,!0),w=f(b.mapTypeControl,!0),x=f(b.scaleControl,!0),y=f(b.streetViewControl,!0),z=f(z,!0),A={},B={zoom:this.zoom,center:q,mapTypeId:p},C={panControl:v,zoomControl:r,zoomControlOptions:{style:google.maps.ZoomControlStyle[t],position:google.maps.ControlPosition[u]},mapTypeControl:w,scaleControl:x,streetViewControl:y,overviewMapControl:z};if("string"==typeof b.el||"string"==typeof b.div?n.indexOf("#")>-1?this.el=h(n,b.context):this.el=g.apply(this,[n,b.context]):this.el=n,void 0===this.el||null===this.el)throw"No element defined.";for(window.context_menu=window.context_menu||{},window.context_menu[j.el.id]={},this.controls=[],this.overlays=[],this.layers=[],this.singleLayers={},this.markers=[],this.polylines=[],this.routes=[],this.polygons=[],this.infoWindow=null,this.overlay_el=null,this.zoom=b.zoom,this.registered_events={},this.el.style.width=b.width||this.el.scrollWidth||this.el.offsetWidth,this.el.style.height=b.height||this.el.scrollHeight||this.el.offsetHeight,google.maps.visualRefresh=b.enableNewStyle,e=0;e<m.length;e++)delete b[m[e]];for(1!=b.disableDefaultUI&&(B=a(B,C)),A=a(B,b),e=0;e<k.length;e++)delete A[k[e]];for(e=0;e<l.length;e++)delete A[l[e]];this.map=new google.maps.Map(this.el,A),o&&(this.markerClusterer=o.apply(this,[this.map]));var D=function(a,b){var c="",d=window.context_menu[j.el.id][a];for(var e in d)if(d.hasOwnProperty(e)){var f=d[e];c+='<li><a id="'+a+"_"+e+'" href="#">'+f.title+"</a></li>"}if(h("gmaps_context_menu")){var g=h("gmaps_context_menu");g.innerHTML=c;var e,k=g.getElementsByTagName("a"),l=k.length;for(e=0;e<l;e++){var m=k[e],n=function(c){c.preventDefault(),d[this.id.replace(a+"_","")].action.apply(j,[b]),j.hideContextMenu()};google.maps.event.clearListeners(m,"click"),google.maps.event.addDomListenerOnce(m,"click",n,!1)}var o=i.apply(this,[j.el]),p=o[0]+b.pixel.x-15,q=o[1]+b.pixel.y-15;g.style.left=p+"px",g.style.top=q+"px"}};this.buildContextMenu=function(a,b){if("marker"===a){b.pixel={};var c=new google.maps.OverlayView;c.setMap(j.map),c.draw=function(){var d=c.getProjection(),e=b.marker.getPosition();b.pixel=d.fromLatLngToContainerPixel(e),D(a,b)}}else D(a,b);var d=h("gmaps_context_menu");setTimeout(function(){d.style.display="block"},0)},this.setContextMenu=function(a){window.context_menu[j.el.id][a.control]={};var b,d=c.createElement("ul");for(b in a.options)if(a.options.hasOwnProperty(b)){var e=a.options[b];window.context_menu[j.el.id][a.control][e.name]={title:e.title,action:e.action}}d.id="gmaps_context_menu",d.style.display="none",d.style.position="absolute",d.style.minWidth="100px",d.style.background="white",d.style.listStyle="none",d.style.padding="8px",d.style.boxShadow="2px 2px 6px #ccc",h("gmaps_context_menu")||c.body.appendChild(d);var f=h("gmaps_context_menu");google.maps.event.addDomListener(f,"mouseout",function(a){a.relatedTarget&&this.contains(a.relatedTarget)||window.setTimeout(function(){f.style.display="none"},400)},!1)},this.hideContextMenu=function(){var a=h("gmaps_context_menu");a&&(a.style.display="none")};var E=function(a,c){google.maps.event.addListener(a,c,function(a){void 0==a&&(a=this),b[c].apply(this,[a]),j.hideContextMenu()})};google.maps.event.addListener(this.map,"zoom_changed",this.hideContextMenu);for(var F=0;F<k.length;F++){var G=k[F];G in b&&E(this.map,G)}for(var F=0;F<l.length;F++){var G=l[F];G in b&&E(this.map,G)}google.maps.event.addListener(this.map,"rightclick",function(a){b.rightclick&&b.rightclick.apply(this,[a]),void 0!=window.context_menu[j.el.id].map&&j.buildContextMenu("map",a)}),this.refresh=function(){google.maps.event.trigger(this.map,"resize")},this.fitZoom=function(){var a,b=[],c=this.markers.length;for(a=0;a<c;a++)"boolean"==typeof this.markers[a].visible&&this.markers[a].visible&&b.push(this.markers[a].getPosition());this.fitLatLngBounds(b)},this.fitLatLngBounds=function(a){var b,c=a.length,d=new google.maps.LatLngBounds;for(b=0;b<c;b++)d.extend(a[b]);this.map.fitBounds(d)},this.setCenter=function(a,b,c){this.map.panTo(new google.maps.LatLng(a,b)),c&&c()},this.getElement=function(){return this.el},this.zoomIn=function(a){a=a||1,this.zoom=this.map.getZoom()+a,this.map.setZoom(this.zoom)},this.zoomOut=function(a){a=a||1,this.zoom=this.map.getZoom()-a,this.map.setZoom(this.zoom)};var H,I=[];for(H in this.map)"function"!=typeof this.map[H]||this[H]||I.push(H);for(e=0;e<I.length;e++)!function(a,b,c){a[c]=function(){return b[c].apply(b,arguments)}}(this,this.map,I[e])};return d}(this);j.prototype.createControl=function(a){var b=document.createElement("div");b.style.cursor="pointer",a.disableDefaultStyles!==!0&&(b.style.fontFamily="Roboto, Arial, sans-serif",b.style.fontSize="11px",b.style.boxShadow="rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px");for(var c in a.style)b.style[c]=a.style[c];a.id&&(b.id=a.id),a.title&&(b.title=a.title),a.classes&&(b.className=a.classes),a.content&&("string"==typeof a.content?b.innerHTML=a.content:a.content instanceof HTMLElement&&b.appendChild(a.content)),a.position&&(b.position=google.maps.ControlPosition[a.position.toUpperCase()]);for(var d in a.events)!function(b,c){google.maps.event.addDomListener(b,c,function(){a.events[c].apply(this,[this])})}(b,d);return b.index=1,b},j.prototype.addControl=function(a){var b=this.createControl(a);return this.controls.push(b),this.map.controls[b.position].push(b),b},j.prototype.removeControl=function(a){var b,c=null;for(b=0;b<this.controls.length;b++)this.controls[b]==a&&(c=this.controls[b].position,this.controls.splice(b,1));if(c)for(b=0;b<this.map.controls.length;b++){var d=this.map.controls[a.position];if(d.getAt(b)==a){d.removeAt(b);break}}return a},j.prototype.createMarker=function(b){if(void 0==b.lat&&void 0==b.lng&&void 0==b.position)throw"No latitude or longitude defined.";var c=this,d=b.details,e=b.fences,f=b.outside,g={position:new google.maps.LatLng(b.lat,b.lng),map:null},h=a(g,b);delete h.lat,delete h.lng,delete h.fences,delete h.outside;var i=new google.maps.Marker(h);if(i.fences=e,b.infoWindow){i.infoWindow=new google.maps.InfoWindow(b.infoWindow);for(var j=["closeclick","content_changed","domready","position_changed","zindex_changed"],k=0;k<j.length;k++)!function(a,c){b.infoWindow[c]&&google.maps.event.addListener(a,c,function(a){b.infoWindow[c].apply(this,[a])})}(i.infoWindow,j[k])}for(var l=["animation_changed","clickable_changed","cursor_changed","draggable_changed","flat_changed","icon_changed","position_changed","shadow_changed","shape_changed","title_changed","visible_changed","zindex_changed"],m=["dblclick","drag","dragend","dragstart","mousedown","mouseout","mouseover","mouseup"],k=0;k<l.length;k++)!function(a,c){b[c]&&google.maps.event.addListener(a,c,function(){b[c].apply(this,[this])})}(i,l[k]);for(var k=0;k<m.length;k++)!function(a,c,d){b[d]&&google.maps.event.addListener(c,d,function(c){c.pixel||(c.pixel=a.getProjection().fromLatLngToPoint(c.latLng)),b[d].apply(this,[c])})}(this.map,i,m[k]);return google.maps.event.addListener(i,"click",function(){this.details=d,b.click&&b.click.apply(this,[this]),i.infoWindow&&(c.hideInfoWindows(),i.infoWindow.open(c.map,i))}),google.maps.event.addListener(i,"rightclick",function(a){a.marker=this,b.rightclick&&b.rightclick.apply(this,[a]),void 0!=window.context_menu[c.el.id].marker&&c.buildContextMenu("marker",a)}),i.fences&&google.maps.event.addListener(i,"dragend",function(){c.checkMarkerGeofence(i,function(a,b){f(a,b)})}),i},j.prototype.addMarker=function(a){var b;if(a.hasOwnProperty("gm_accessors_"))b=a;else{if(!(a.hasOwnProperty("lat")&&a.hasOwnProperty("lng")||a.position))throw"No latitude or longitude defined.";b=this.createMarker(a)}return b.setMap(this.map),this.markerClusterer&&this.markerClusterer.addMarker(b),this.markers.push(b),j.fire("marker_added",b,this),b},j.prototype.addMarkers=function(a){for(var b,c=0;b=a[c];c++)this.addMarker(b);return this.markers},j.prototype.hideInfoWindows=function(){for(var a,b=0;a=this.markers[b];b++)a.infoWindow&&a.infoWindow.close()},j.prototype.removeMarker=function(a){for(var b=0;b<this.markers.length;b++)if(this.markers[b]===a){this.markers[b].setMap(null),this.markers.splice(b,1),this.markerClusterer&&this.markerClusterer.removeMarker(a),j.fire("marker_removed",a,this);break}return a},j.prototype.removeMarkers=function(a){var b=[];if(void 0===a){for(var c=0;c<this.markers.length;c++){var d=this.markers[c];d.setMap(null),j.fire("marker_removed",d,this)}this.markerClusterer&&this.markerClusterer.clearMarkers&&this.markerClusterer.clearMarkers(),this.markers=b}else{for(var c=0;c<a.length;c++){var e=this.markers.indexOf(a[c]);if(e>-1){var d=this.markers[e];d.setMap(null),this.markerClusterer&&this.markerClusterer.removeMarker(d),j.fire("marker_removed",d,this)}}for(var c=0;c<this.markers.length;c++){var d=this.markers[c];null!=d.getMap()&&b.push(d)}this.markers=b}},j.prototype.drawOverlay=function(a){var b=new google.maps.OverlayView,c=!0;return b.setMap(this.map),null!=a.auto_show&&(c=a.auto_show),b.onAdd=function(){var c=document.createElement("div");c.style.borderStyle="none",c.style.borderWidth="0px",c.style.position="absolute",c.style.zIndex=100,c.innerHTML=a.content,b.el=c,a.layer||(a.layer="overlayLayer");var d=this.getPanes(),e=d[a.layer],f=["contextmenu","DOMMouseScroll","dblclick","mousedown"];e.appendChild(c);for(var g=0;g<f.length;g++)!function(a,b){google.maps.event.addDomListener(a,b,function(a){navigator.userAgent.toLowerCase().indexOf("msie")!=-1&&document.all?(a.cancelBubble=!0,a.returnValue=!1):a.stopPropagation()})}(c,f[g]);a.click&&(d.overlayMouseTarget.appendChild(b.el),google.maps.event.addDomListener(b.el,"click",function(){a.click.apply(b,[b])})),google.maps.event.trigger(this,"ready")},b.draw=function(){var d=this.getProjection(),e=d.fromLatLngToDivPixel(new google.maps.LatLng(a.lat,a.lng));a.horizontalOffset=a.horizontalOffset||0,a.verticalOffset=a.verticalOffset||0;var f=b.el,g=f.children[0],h=g.clientHeight,i=g.clientWidth;switch(a.verticalAlign){case"top":f.style.top=e.y-h+a.verticalOffset+"px";break;default:case"middle":f.style.top=e.y-h/2+a.verticalOffset+"px";break;case"bottom":f.style.top=e.y+a.verticalOffset+"px"}switch(a.horizontalAlign){case"left":f.style.left=e.x-i+a.horizontalOffset+"px";break;default:case"center":f.style.left=e.x-i/2+a.horizontalOffset+"px";break;case"right":f.style.left=e.x+a.horizontalOffset+"px"}f.style.display=c?"block":"none",c||a.show.apply(this,[f])},b.onRemove=function(){var c=b.el;a.remove?a.remove.apply(this,[c]):(b.el.parentNode.removeChild(b.el),b.el=null)},this.overlays.push(b),b},j.prototype.removeOverlay=function(a){for(var b=0;b<this.overlays.length;b++)if(this.overlays[b]===a){this.overlays[b].setMap(null),this.overlays.splice(b,1);break}},j.prototype.removeOverlays=function(){for(var a,b=0;a=this.overlays[b];b++)a.setMap(null);this.overlays=[]},j.prototype.drawPolyline=function(a){var b=[],c=a.path;if(c.length)if(void 0===c[0][0])b=c;else for(var d,e=0;d=c[e];e++)b.push(new google.maps.LatLng(d[0],d[1]));var f={map:this.map,path:b,strokeColor:a.strokeColor,strokeOpacity:a.strokeOpacity,strokeWeight:a.strokeWeight,geodesic:a.geodesic,clickable:!0,editable:!1,visible:!0};a.hasOwnProperty("clickable")&&(f.clickable=a.clickable),a.hasOwnProperty("editable")&&(f.editable=a.editable),a.hasOwnProperty("icons")&&(f.icons=a.icons),a.hasOwnProperty("zIndex")&&(f.zIndex=a.zIndex);for(var g=new google.maps.Polyline(f),h=["click","dblclick","mousedown","mousemove","mouseout","mouseover","mouseup","rightclick"],i=0;i<h.length;i++)!function(b,c){a[c]&&google.maps.event.addListener(b,c,function(b){a[c].apply(this,[b])})}(g,h[i]);return this.polylines.push(g),j.fire("polyline_added",g,this),g},j.prototype.removePolyline=function(a){for(var b=0;b<this.polylines.length;b++)if(this.polylines[b]===a){this.polylines[b].setMap(null),this.polylines.splice(b,1),j.fire("polyline_removed",a,this);break}},j.prototype.removePolylines=function(){for(var a,b=0;a=this.polylines[b];b++)a.setMap(null);this.polylines=[]},j.prototype.drawCircle=function(b){b=a({map:this.map,center:new google.maps.LatLng(b.lat,b.lng)},b),delete b.lat,delete b.lng;for(var c=new google.maps.Circle(b),d=["click","dblclick","mousedown","mousemove","mouseout","mouseover","mouseup","rightclick"],e=0;e<d.length;e++)!function(a,c){b[c]&&google.maps.event.addListener(a,c,function(a){b[c].apply(this,[a])})}(c,d[e]);return this.polygons.push(c),c},j.prototype.drawRectangle=function(b){b=a({map:this.map},b);var c=new google.maps.LatLngBounds(new google.maps.LatLng(b.bounds[0][0],b.bounds[0][1]),new google.maps.LatLng(b.bounds[1][0],b.bounds[1][1]));b.bounds=c;for(var d=new google.maps.Rectangle(b),e=["click","dblclick","mousedown","mousemove","mouseout","mouseover","mouseup","rightclick"],f=0;f<e.length;f++)!function(a,c){b[c]&&google.maps.event.addListener(a,c,function(a){b[c].apply(this,[a])})}(d,e[f]);return this.polygons.push(d),d},j.prototype.drawPolygon=function(d){var e=!1;d.hasOwnProperty("useGeoJSON")&&(e=d.useGeoJSON),delete d.useGeoJSON,d=a({map:this.map},d),0==e&&(d.paths=[d.paths.slice(0)]),d.paths.length>0&&d.paths[0].length>0&&(d.paths=c(b(d.paths,f,e)));for(var g=new google.maps.Polygon(d),h=["click","dblclick","mousedown","mousemove","mouseout","mouseover","mouseup","rightclick"],i=0;i<h.length;i++)!function(a,b){d[b]&&google.maps.event.addListener(a,b,function(a){d[b].apply(this,[a])})}(g,h[i]);return this.polygons.push(g),j.fire("polygon_added",g,this),g},j.prototype.removePolygon=function(a){for(var b=0;b<this.polygons.length;b++)if(this.polygons[b]===a){this.polygons[b].setMap(null),this.polygons.splice(b,1),j.fire("polygon_removed",a,this);break}},j.prototype.removePolygons=function(){for(var a,b=0;a=this.polygons[b];b++)a.setMap(null);this.polygons=[]},j.prototype.getFromFusionTables=function(a){var b=a.events;delete a.events;var c=a,d=new google.maps.FusionTablesLayer(c);for(var e in b)!function(a,c){google.maps.event.addListener(a,c,function(a){b[c].apply(this,[a])})}(d,e);return this.layers.push(d),d},j.prototype.loadFromFusionTables=function(a){var b=this.getFromFusionTables(a);return b.setMap(this.map),b},j.prototype.getFromKML=function(a){var b=a.url,c=a.events;delete a.url,delete a.events;var d=a,e=new google.maps.KmlLayer(b,d);for(var f in c)!function(a,b){google.maps.event.addListener(a,b,function(a){c[b].apply(this,[a])})}(e,f);return this.layers.push(e),e},j.prototype.loadFromKML=function(a){var b=this.getFromKML(a);return b.setMap(this.map),b},j.prototype.addLayer=function(a,b){b=b||{};var c;switch(a){case"weather":this.singleLayers.weather=c=new google.maps.weather.WeatherLayer;break;case"clouds":this.singleLayers.clouds=c=new google.maps.weather.CloudLayer;break;case"traffic":this.singleLayers.traffic=c=new google.maps.TrafficLayer;break;case"transit":this.singleLayers.transit=c=new google.maps.TransitLayer;break;case"bicycling":this.singleLayers.bicycling=c=new google.maps.BicyclingLayer;break;case"panoramio":this.singleLayers.panoramio=c=new google.maps.panoramio.PanoramioLayer,c.setTag(b.filter),delete b.filter,b.click&&google.maps.event.addListener(c,"click",function(a){b.click(a),delete b.click});break;case"places":if(this.singleLayers.places=c=new google.maps.places.PlacesService(this.map),b.search||b.nearbySearch||b.radarSearch){var d={bounds:b.bounds||null,keyword:b.keyword||null,location:b.location||null,name:b.name||null,radius:b.radius||null,rankBy:b.rankBy||null,types:b.types||null};b.radarSearch&&c.radarSearch(d,b.radarSearch),b.search&&c.search(d,b.search),b.nearbySearch&&c.nearbySearch(d,b.nearbySearch)}if(b.textSearch){var e={bounds:b.bounds||null,location:b.location||null,query:b.query||null,radius:b.radius||null};c.textSearch(e,b.textSearch)}}if(void 0!==c)return"function"==typeof c.setOptions&&c.setOptions(b),"function"==typeof c.setMap&&c.setMap(this.map),c},j.prototype.removeLayer=function(a){if("string"==typeof a&&void 0!==this.singleLayers[a])this.singleLayers[a].setMap(null),delete this.singleLayers[a];else for(var b=0;b<this.layers.length;b++)if(this.layers[b]===a){this.layers[b].setMap(null),this.layers.splice(b,1);break}};var k,l;return j.prototype.getRoutes=function(b){switch(b.travelMode){case"bicycling":k=google.maps.TravelMode.BICYCLING;break;case"transit":k=google.maps.TravelMode.TRANSIT;break;case"driving":k=google.maps.TravelMode.DRIVING;break;default:k=google.maps.TravelMode.WALKING}l="imperial"===b.unitSystem?google.maps.UnitSystem.IMPERIAL:google.maps.UnitSystem.METRIC;var c={avoidHighways:!1,avoidTolls:!1,optimizeWaypoints:!1,waypoints:[]},d=a(c,b);d.origin=/string/.test(typeof b.origin)?b.origin:new google.maps.LatLng(b.origin[0],b.origin[1]),d.destination=/string/.test(typeof b.destination)?b.destination:new google.maps.LatLng(b.destination[0],b.destination[1]),d.travelMode=k,d.unitSystem=l,delete d.callback,delete d.error;var e=[];(new google.maps.DirectionsService).route(d,function(a,c){if(c===google.maps.DirectionsStatus.OK){for(var d in a.routes)a.routes.hasOwnProperty(d)&&e.push(a.routes[d]);b.callback&&b.callback(e,a,c)}else b.error&&b.error(a,c)})},j.prototype.removeRoutes=function(){this.routes.length=0},j.prototype.getElevations=function(d){d=a({locations:[],path:!1,samples:256},d),d.locations.length>0&&d.locations[0].length>0&&(d.locations=c(b([d.locations],f,!1)));var e=d.callback;delete d.callback;var g=new google.maps.ElevationService;if(d.path){var h={path:d.locations,samples:d.samples};g.getElevationAlongPath(h,function(a,b){e&&"function"==typeof e&&e(a,b)})}else delete d.path,delete d.samples,g.getElevationForLocations(d,function(a,b){e&&"function"==typeof e&&e(a,b)})},j.prototype.cleanRoute=j.prototype.removePolylines,j.prototype.renderRoute=function(b,c){var d,e="string"==typeof c.panel?document.getElementById(c.panel.replace("#","")):c.panel;c.panel=e,c=a({map:this.map},c),d=new google.maps.DirectionsRenderer(c),this.getRoutes({origin:b.origin,destination:b.destination,travelMode:b.travelMode,waypoints:b.waypoints,unitSystem:b.unitSystem,error:b.error,avoidHighways:b.avoidHighways,avoidTolls:b.avoidTolls,optimizeWaypoints:b.optimizeWaypoints,callback:function(a,b,c){c===google.maps.DirectionsStatus.OK&&d.setDirections(b)}})},j.prototype.drawRoute=function(a){var b=this;this.getRoutes({origin:a.origin,destination:a.destination,travelMode:a.travelMode,waypoints:a.waypoints,unitSystem:a.unitSystem,error:a.error,avoidHighways:a.avoidHighways,avoidTolls:a.avoidTolls,optimizeWaypoints:a.optimizeWaypoints,callback:function(c){if(c.length>0){var d={path:c[c.length-1].overview_path,strokeColor:a.strokeColor,strokeOpacity:a.strokeOpacity,strokeWeight:a.strokeWeight};a.hasOwnProperty("icons")&&(d.icons=a.icons),b.drawPolyline(d),a.callback&&a.callback(c[c.length-1])}}})},j.prototype.travelRoute=function(a){if(a.origin&&a.destination)this.getRoutes({origin:a.origin,destination:a.destination,travelMode:a.travelMode,waypoints:a.waypoints,unitSystem:a.unitSystem,error:a.error,callback:function(b){if(b.length>0&&a.start&&a.start(b[b.length-1]),b.length>0&&a.step){var c=b[b.length-1];if(c.legs.length>0)for(var d,e=c.legs[0].steps,f=0;d=e[f];f++)d.step_number=f,a.step(d,c.legs[0].steps.length-1)}b.length>0&&a.end&&a.end(b[b.length-1])}});else if(a.route&&a.route.legs.length>0)for(var b,c=a.route.legs[0].steps,d=0;b=c[d];d++)b.step_number=d,a.step(b)},j.prototype.drawSteppedRoute=function(a){var b=this;if(a.origin&&a.destination)this.getRoutes({origin:a.origin,destination:a.destination,travelMode:a.travelMode,waypoints:a.waypoints,error:a.error,callback:function(c){if(c.length>0&&a.start&&a.start(c[c.length-1]),c.length>0&&a.step){var d=c[c.length-1];if(d.legs.length>0)for(var e,f=d.legs[0].steps,g=0;e=f[g];g++){e.step_number=g;var h={path:e.path,strokeColor:a.strokeColor,strokeOpacity:a.strokeOpacity,strokeWeight:a.strokeWeight};a.hasOwnProperty("icons")&&(h.icons=a.icons),b.drawPolyline(h),a.step(e,d.legs[0].steps.length-1)}}c.length>0&&a.end&&a.end(c[c.length-1])}});else if(a.route&&a.route.legs.length>0)for(var c,d=a.route.legs[0].steps,e=0;c=d[e];e++){c.step_number=e;var f={path:c.path,strokeColor:a.strokeColor,strokeOpacity:a.strokeOpacity,strokeWeight:a.strokeWeight};a.hasOwnProperty("icons")&&(f.icons=a.icons),b.drawPolyline(f),a.step(c)}},j.Route=function(a){this.origin=a.origin,this.destination=a.destination,this.waypoints=a.waypoints,this.map=a.map,this.route=a.route,this.step_count=0,this.steps=this.route.legs[0].steps,this.steps_length=this.steps.length;var b={path:new google.maps.MVCArray,strokeColor:a.strokeColor,strokeOpacity:a.strokeOpacity,strokeWeight:a.strokeWeight};a.hasOwnProperty("icons")&&(b.icons=a.icons),this.polyline=this.map.drawPolyline(b).getPath()},j.Route.prototype.getRoute=function(a){var b=this;this.map.getRoutes({origin:this.origin,destination:this.destination,travelMode:a.travelMode,waypoints:this.waypoints||[],error:a.error,callback:function(){b.route=e[0],a.callback&&a.callback.call(b)}})},j.Route.prototype.back=function(){if(this.step_count>0){this.step_count--;var a=this.route.legs[0].steps[this.step_count].path;for(var b in a)a.hasOwnProperty(b)&&this.polyline.pop()}},j.Route.prototype.forward=function(){if(this.step_count<this.steps_length){var a=this.route.legs[0].steps[this.step_count].path;for(var b in a)a.hasOwnProperty(b)&&this.polyline.push(a[b]);this.step_count++}},j.prototype.checkGeofence=function(a,b,c){return c.containsLatLng(new google.maps.LatLng(a,b))},j.prototype.checkMarkerGeofence=function(a,b){if(a.fences)for(var c,d=0;c=a.fences[d];d++){var e=a.getPosition();this.checkGeofence(e.lat(),e.lng(),c)||b(a,c)}},j.prototype.toImage=function(a){var a=a||{},b={};if(b.size=a.size||[this.el.clientWidth,this.el.clientHeight],b.lat=this.getCenter().lat(),b.lng=this.getCenter().lng(),this.markers.length>0){b.markers=[];for(var c=0;c<this.markers.length;c++)b.markers.push({lat:this.markers[c].getPosition().lat(),lng:this.markers[c].getPosition().lng()})}if(this.polylines.length>0){var d=this.polylines[0];b.polyline={},b.polyline.path=google.maps.geometry.encoding.encodePath(d.getPath()),b.polyline.strokeColor=d.strokeColor,b.polyline.strokeOpacity=d.strokeOpacity,b.polyline.strokeWeight=d.strokeWeight}return j.staticMapURL(b)},j.staticMapURL=function(a){function b(a,b){if("#"===a[0]&&(a=a.replace("#","0x"),b)){if(b=parseFloat(b),0===(b=Math.min(1,Math.max(b,0))))return"0x00000000";b=(255*b).toString(16),1===b.length&&(b+=b),a=a.slice(0,8)+b}return a}var c,d=[],e=("file:"===location.protocol?"http:":location.protocol)+"//maps.googleapis.com/maps/api/staticmap";a.url&&(e=a.url,delete a.url),e+="?";var f=a.markers;delete a.markers,!f&&a.marker&&(f=[a.marker],delete a.marker);var g=a.styles;delete a.styles;var h=a.polyline;if(delete a.polyline,a.center)d.push("center="+a.center),delete a.center;else if(a.address)d.push("center="+a.address),delete a.address;else if(a.lat)d.push(["center=",a.lat,",",a.lng].join("")),delete a.lat,delete a.lng;else if(a.visible){var i=encodeURI(a.visible.join("|"));d.push("visible="+i)}var j=a.size;j?(j.join&&(j=j.join("x")),delete a.size):j="630x300",d.push("size="+j),a.zoom||a.zoom===!1||(a.zoom=15);var k=!a.hasOwnProperty("sensor")||!!a.sensor;delete a.sensor,d.push("sensor="+k);for(var l in a)a.hasOwnProperty(l)&&d.push(l+"="+a[l]);if(f)for(var m,n,o=0;c=f[o];o++){m=[],c.size&&"normal"!==c.size?(m.push("size:"+c.size),delete c.size):c.icon&&(m.push("icon:"+encodeURI(c.icon)),delete c.icon),c.color&&(m.push("color:"+c.color.replace("#","0x")),delete c.color),c.label&&(m.push("label:"+c.label[0].toUpperCase()),delete c.label),n=c.address?c.address:c.lat+","+c.lng,delete c.address,delete c.lat,delete c.lng;for(var l in c)c.hasOwnProperty(l)&&m.push(l+":"+c[l]);m.length||0===o?(m.push(n),m=m.join("|"),d.push("markers="+encodeURI(m))):(m=d.pop()+encodeURI("|"+n),d.push(m))}if(g)for(var o=0;o<g.length;o++){var p=[];g[o].featureType&&p.push("feature:"+g[o].featureType.toLowerCase()),g[o].elementType&&p.push("element:"+g[o].elementType.toLowerCase());for(var q=0;q<g[o].stylers.length;q++)for(var r in g[o].stylers[q]){var s=g[o].stylers[q][r];"hue"!=r&&"color"!=r||(s="0x"+s.substring(1)),p.push(r+":"+s)}var t=p.join("|");""!=t&&d.push("style="+t)}if(h){if(c=h,h=[],c.strokeWeight&&h.push("weight:"+parseInt(c.strokeWeight,10)),c.strokeColor){var u=b(c.strokeColor,c.strokeOpacity);h.push("color:"+u)}if(c.fillColor){var v=b(c.fillColor,c.fillOpacity);h.push("fillcolor:"+v)}var w=c.path;if(w.join)for(var x,q=0;x=w[q];q++)h.push(x.join(","));else h.push("enc:"+w);h=h.join("|"),d.push("path="+encodeURI(h))}var y=window.devicePixelRatio||1;return d.push("scale="+y),d=d.join("&"),e+d},j.prototype.addMapType=function(a,b){if(!b.hasOwnProperty("getTileUrl")||"function"!=typeof b.getTileUrl)throw"'getTileUrl' function required.";b.tileSize=b.tileSize||new google.maps.Size(256,256);var c=new google.maps.ImageMapType(b);this.map.mapTypes.set(a,c)},j.prototype.addOverlayMapType=function(a){if(!a.hasOwnProperty("getTile")||"function"!=typeof a.getTile)throw"'getTile' function required.";var b=a.index;delete a.index,this.map.overlayMapTypes.insertAt(b,a)},j.prototype.removeOverlayMapType=function(a){this.map.overlayMapTypes.removeAt(a)},j.prototype.addStyle=function(a){var b=new google.maps.StyledMapType(a.styles,{name:a.styledMapName});this.map.mapTypes.set(a.mapTypeId,b)},j.prototype.setStyle=function(a){this.map.setMapTypeId(a)},j.prototype.createPanorama=function(a){return a.hasOwnProperty("lat")&&a.hasOwnProperty("lng")||(a.lat=this.getCenter().lat(),a.lng=this.getCenter().lng()),this.panorama=j.createPanorama(a),this.map.setStreetView(this.panorama),this.panorama},j.createPanorama=function(b){var c=h(b.el,b.context);b.position=new google.maps.LatLng(b.lat,b.lng),delete b.el,delete b.context,delete b.lat,delete b.lng;for(var d=["closeclick","links_changed","pano_changed","position_changed","pov_changed","resize","visible_changed"],e=a({visible:!0},b),f=0;f<d.length;f++)delete e[d[f]];for(var g=new google.maps.StreetViewPanorama(c,e),f=0;f<d.length;f++)!function(a,c){b[c]&&google.maps.event.addListener(a,c,function(){b[c].apply(this)})}(g,d[f]);return g},j.prototype.on=function(a,b){return j.on(a,this,b)},j.prototype.off=function(a){j.off(a,this)},j.prototype.once=function(a,b){return j.once(a,this,b)},j.custom_events=["marker_added","marker_removed","polyline_added","polyline_removed","polygon_added","polygon_removed","geolocated","geolocation_failed"],j.on=function(a,b,c){if(j.custom_events.indexOf(a)==-1)return b instanceof j&&(b=b.map),google.maps.event.addListener(b,a,c);var d={handler:c,eventName:a};return b.registered_events[a]=b.registered_events[a]||[],b.registered_events[a].push(d),d},j.off=function(a,b){j.custom_events.indexOf(a)==-1?(b instanceof j&&(b=b.map),google.maps.event.clearListeners(b,a)):b.registered_events[a]=[]},j.once=function(a,b,c){if(j.custom_events.indexOf(a)==-1)return b instanceof j&&(b=b.map),google.maps.event.addListenerOnce(b,a,c)},j.fire=function(a,b,c){if(j.custom_events.indexOf(a)==-1)google.maps.event.trigger(b,a,Array.prototype.slice.apply(arguments).slice(2));else if(a in c.registered_events)for(var d=c.registered_events[a],e=0;e<d.length;e++)!function(a,b,c){a.apply(b,[c])}(d[e].handler,c,b)},j.geolocate=function(a){var b=a.always||a.complete;navigator.geolocation?navigator.geolocation.getCurrentPosition(function(c){a.success(c),b&&b()},function(c){a.error(c),b&&b()},a.options):(a.not_supported(),b&&b())},j.geocode=function(a){this.geocoder=new google.maps.Geocoder;var b=a.callback;a.hasOwnProperty("lat")&&a.hasOwnProperty("lng")&&(a.latLng=new google.maps.LatLng(a.lat,a.lng)),delete a.lat,delete a.lng,delete a.callback,this.geocoder.geocode(a,function(a,c){b(a,c)})},"object"==typeof window.google&&window.google.maps&&(google.maps.Polygon.prototype.getBounds||(google.maps.Polygon.prototype.getBounds=function(a){for(var b,c=new google.maps.LatLngBounds,d=this.getPaths(),e=0;e<d.getLength();e++){b=d.getAt(e);for(var f=0;f<b.getLength();f++)c.extend(b.getAt(f))}return c}),google.maps.Polygon.prototype.containsLatLng||(google.maps.Polygon.prototype.containsLatLng=function(a){var b=this.getBounds();if(null!==b&&!b.contains(a))return!1;for(var c=!1,d=this.getPaths().getLength(),e=0;e<d;e++)for(var f=this.getPaths().getAt(e),g=f.getLength(),h=g-1,i=0;i<g;i++){var j=f.getAt(i),k=f.getAt(h);(j.lng()<a.lng()&&k.lng()>=a.lng()||k.lng()<a.lng()&&j.lng()>=a.lng())&&j.lat()+(a.lng()-j.lng())/(k.lng()-j.lng())*(k.lat()-j.lat())<a.lat()&&(c=!c),h=i}return c}),google.maps.Circle.prototype.containsLatLng||(google.maps.Circle.prototype.containsLatLng=function(a){return!google.maps.geometry||google.maps.geometry.spherical.computeDistanceBetween(this.getCenter(),a)<=this.getRadius()}),google.maps.Rectangle.prototype.containsLatLng=function(a){return this.getBounds().contains(a)},google.maps.LatLngBounds.prototype.containsLatLng=function(a){return this.contains(a)},google.maps.Marker.prototype.setFences=function(a){this.fences=a},google.maps.Marker.prototype.addFence=function(a){this.fences.push(a)},google.maps.Marker.prototype.getId=function(){return this.__gm_id}),Array.prototype.indexOf||(Array.prototype.indexOf=function(a){if(null==this)throw new TypeError;var b=Object(this),c=b.length>>>0;if(0===c)return-1;var d=0;if(arguments.length>1&&(d=Number(arguments[1]),d!=d?d=0:0!=d&&d!=1/0&&d!=-(1/0)&&(d=(d>0||-1)*Math.floor(Math.abs(d)))),d>=c)return-1;for(var e=d>=0?d:Math.max(c-Math.abs(d),0);e<c;e++)if(e in b&&b[e]===a)return e;return-1}),j});
//# sourceMappingURL=gmaps.min.js.map
