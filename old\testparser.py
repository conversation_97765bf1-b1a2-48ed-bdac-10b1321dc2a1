import analyzer as p
import time, sys

logfilename = '2021_01_04_02_09_28240232078.log'
#logfilename = 'server.log.7'
print 'Analyzing Log : ' + logfilename
p.main_analyzer(logfilename, False)
sys.exit()

st = time.time()
for x in range(9):
    logfilename = 'server.log.' + str(x+1)
    print 'Analyzing Log : ' + logfilename
    p.main_analyzer(logfilename, False)
    print '========================================================'
et = time.time()
print 'Took ' + str(et -st) + ' seconds to process 9 files'
sys.exit()

#import os, logging, shlex
#import analyzer as p
#import time, sys
#from logging.handlers import RotatingFileHandler
#import subprocess
#logging.basicConfig(handlers=[RotatingFileHandler(filename='analyzer.log',
#                     mode='w', maxBytes=512000, backupCount=4)], level=debug_level,
#                     format='"%(asctime)s %(threadName)s %(name)s [%(levelname)s]  %(message)s"', 
#                    datefmt='%m/%d/%Y%I:%M:%S %p')
#
#logger = logging.getLogger('my_logger')
#
#logger.info('Starting Business....')
#st = time.time()
#
#cmd = 'ls -l *.log.*[0-9]'
#ls = subprocess.Popen(shlex.split(cmd), stdout=subprocess.PIPE, )
#grep = subprocess.Popen(["grep", "-v", "/$"], stdin=ls.stdout, stdout=subprocess.PIPE, )
#endOfPipe = grep.stdout
#print endOfPipe
#sys.exit()
#
#for line in endOfPipe:
#    print (line)
#    
#filelist = []
#for root, dirs, files in os.walk("/root/lincoln"):
#    for filename in files:
#        filelist.append(1)
#        logfilename = '/root/lincoln/'+str(filename)
#        logger.info('Analyzing Log : ' + logfilename)
#        #try:
#        #    p.main_analyzer(logfilename, False)
#        #except Exception as e:
#        #    logger.info('Failed on logfile : ' + logfilename + ' due to ' + str(e)) 
#et = time.time()
#logger.info('Took ' + str(et -st) + ' seconds to process ' + str(filelist) + ' files')
#logger.info('Ended Business....')            
#