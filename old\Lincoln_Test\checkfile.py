import os, sys, paramiko, socket, tqdm


try:
    from tqdm import tqdm
except ImportError:
    class TqdmWrap(object):
        # tqdm not installed - construct and return dummy/basic versions
        def __init__(self, *a, **k):
            pass

        def viewBar(self, a, b):
            # original version
            res = a / int(b) * 100
            sys.stdout.write('\rDownloading: %.2f %%' % (res))
            sys.stdout.write('\n')
            sys.stdout.flush()

        def __enter__(self):
            return self

        def __exit__(self, *a):
            return False
else:
    class TqdmWrap(tqdm):
        def viewBar(self, a, b):
            self.total = int(b)
            self.update(int(a - self.n))  # update pbar with increment


def ssh_ctrl(ip, user, password,cmd):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        ssh.connect(hostname=ip, username=user, password=password, timeout=5, compress = True,look_for_keys=False, allow_agent=False)
    except (socket.error,paramiko.AuthenticationException,paramiko.SSHException) as message:
        print("ERROR: SSH connection to "+ip+" failed: " +str(message))
        return None

    stdin, stdout, ssh_stderr = ssh.exec_command(cmd)
    out = stdout.read()
    error = ssh_stderr.read()
    if not stdout.channel.recv_exit_status() == 0:
        oknot = 'ERROR'	
        app.logger.error('ERROR : ' + str(error))
        return None	  

    stdin.flush()
    ssh.close()
    return out			
def ssh_logs_download(ip, user, password,localfilename,remotepathfilename):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        ssh.connect(hostname=ip, username=user, password=password, timeout=5, compress = True,look_for_keys=False, allow_agent=False)
    except (socket.error,paramiko.AuthenticationException,paramiko.SSHException) as message:
        print ("ERROR: SSH connection to "+ip+" failed: " +str(message))
        sys.exit(1)

    sftp = ssh.open_sftp()
    #sftp = ssh.open_sftp()
    with TqdmWrap(ascii=True, unit='b', unit_scale=True) as pbar:
        sftp.get(remotepathfilename,localfilename, callback=pbar.viewBar)
    #cbk, pbar = tqdmWrapViewBar(ascii=True, unit='b', unit_scale=True)
    #sftp.get(remotepathfilename,localfilename,callback=cbk)
    #time.sleep(2)	
    sftp.close()
    #time.sleep(2)	
    ssh.close()

#f = open('output.txt','r')
#k = f.readlines()
#nlist = []
#for file in k:
#    if 'dataprotection' in file.lower() or 'acm' in file.lower():
#        nlist.append(file)
#        
#o, t = []  , []      
#print 'Found ' + str(len(nlist)) + ' files'
#for i, e in enumerate(nlist):
#    if i < 2600:
#        o.append(e)
#    else:
#        t.append(e)
#   
#j = open('o.txt', 'w')
#for e in o:
#    j.write(str(e))
#j.close()
#
#m = open('tt.txt', 'w')
#for ek in t:    
#    m.write(str(ek))
#m.close()   
#print 'Found ' + str(len(o)) + ' files in o'
#print 'Found ' + str(len(t)) + ' files in t'    


f = open('o.txt', 'r')
m = f.readlines()
f.close()

ssh = paramiko.SSHClient()
ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
try:
    ssh.connect(hostname='lincoln.avamar.com', username='admin', password='changeme', timeout=5, compress = True,look_for_keys=False, allow_agent=False)
except (socket.error,paramiko.AuthenticationException,paramiko.SSHException) as message:
    print ("ERROR: SSH connection to "+ip+" failed: " +str(message))
    sys.exit()
sftp = ssh.open_sftp()
for idx, eachremotefile in enumerate(m):
    try:    
        with TqdmWrap(ascii=True, unit='b', unit_scale=True) as pbar:
            sftp.get(eachremotefile.strip(),eachremotefile.replace('/', '').strip(), callback=pbar.viewBar)     
    except:
        pass        

sftp.close()
ssh.close()        