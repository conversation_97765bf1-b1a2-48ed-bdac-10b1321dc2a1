import os
import json
import requests
from datetime import datetime

class AzureAI:
    def __init__(self):
        self.endpoint = os.getenv('AZURE_OPENAI_ENDPOINT')
        self.api_key = os.getenv('AZURE_OPENAI_KEY')
        self.api_version = os.getenv('AZURE_OPENAI_VERSION', '2023-12-01-preview')
        self.deployment_name = os.getenv('AZURE_OPENAI_DEPLOYMENT', 'gpt-4')
    
    def generate_thread_summary(self, thread_name, thread_stack, thread_owner, thread_activity):
        system_prompt = """You are an expert IDPA (Integrated Data Protection Appliance) log analyst with deep knowledge of Dell EMC data protection systems including Avamar, Data Domain, vCenter, ESX, DPA, DPC, DPS, LDAP, and CDRA components.

Your task is to analyze error thread stacks and provide concise, actionable summaries for technical support engineers.

Focus on:
- Root cause identification
- Impact assessment  
- Recommended next steps
- Urgency level

Keep responses under 150 words and technically precise."""

        user_prompt = f"""Analyze this IDPA error thread:

Thread Name: {thread_name}
Component Owner: {thread_owner}
Thread Activity: {thread_activity}

Error Stack:
{chr(10).join(thread_stack[:10])}

Provide a concise technical summary with root cause and recommended actions."""

        headers = {
            'Content-Type': 'application/json',
            'api-key': self.api_key
        }
        
        data = {
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "max_tokens": 200,
            "temperature": 0.3
        }
        
        url = f"{self.endpoint}/openai/deployments/{self.deployment_name}/chat/completions?api-version={self.api_version}"
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            return f"AI analysis unavailable: {str(e)}"