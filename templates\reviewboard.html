<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />

<center>
<title>LAFI : Review Board</title>
</center>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<head>

  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<link href='https://fonts.googleapis.com/css?family=Raleway' rel='stylesheet'>
<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'>
<style type="text/css">

table{
  margin: 0 auto;
  width: 100%;
  clear: both;
  border-collapse: collapse;
  table-layout: fixed; 
  word-wrap:break-word;
}
table td {overflow:hidden; width:200px; word-wrap:break-word;}

.button {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    
    padding: 1%;
    background: #33568e;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}
.button1 {
  background-color: #6A5ACD;
  color: white;
  font-size: 11px;  
  border: 2px solid #40E0D0;
}

.button1:hover {
  background-color: #40E0D0;
  color: white;
}
.button6 {  
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width:100%;
    padding: 5%;
    background: #4CAF50;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: white;
	font-family: 'Montserrat';  
}

.button6:hover {
  background-color: #008CBA;
  color: white;
}
.button2 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width:100%;
    padding: 5%;
    background: #DC143C;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}
.button2:hover {
  background-color: #008CBA;
  color: white;
}

.button5:hover {
  background-color: #008CBA;
  color: white;
}
.button5 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width:100%;
    padding: 3%;
    background: #33568e;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}


.button3 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    padding: 3%;
    background: #0dbaab;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}

.button2:hover {
  background-color: #008CBA;
  color: white;
}
.modal-dialog-full-width {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width:none !important;

}

.modal-content-full-width  {
    height: auto !important;
    min-height: 100% !important;
    border-radius: 0 !important;
    background-color: #FFFAFA !important 
}

.modal-header-full-width  {
    border-bottom: 1px solid #33568e !important;
}

.modal-footer-full-width  {
    border-top: 1px solid #33568e !important;
}

/* BELOW IS OUR ORIGINAL CODE */
        h3 span {
            font-size: 22px;
        }
        h3 input.search-input {
            width: 300px;
            margin-left: auto;
            float: right
        }
        .mt32 {
            margin-top: 32px;
        }
.card {
  /* Add shadows to create the "card" effect */
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  transition: 0.3s;
}

/* On mouse-over, add a deeper shadow */
.card:hover {
  box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
}

/* Add some padding inside the card container */
.container {
  padding: 2px 16px;
}

.container2 {
  padding: 2px 100px;
}
.alert {
    padding: 1em;
    background: yellow;
}

.form-style-7{
    font: 95% Arial, Helvetica, sans-serif;
    max-width: 1200px;
    margin: 10px auto;
    padding: 16px;
    background: white;
    font-family: 'Montserrat';
	
}
.form-style-7 h1{
    background: #0dbaab;
    padding: 20px 0;
    font-size: 200%;
    font-weight: 300;
    text-align: center;
    color: #fff;
    margin: -16px -16px 16px -16px;
   font-family: 'Montserrat';
}

.form-style-8 h1{
    background: #DCDCDC;
    padding: 5px 0;
    font-size: 200%;
    font-weight: 300;
    text-align: center;
    color: black;
    margin: auto;
   font-family: 'Montserrat';
}

.form-style-6{
    font: 95% Arial, Helvetica, sans-serif;
    max-width: 400px;
    margin: 10px auto;
    padding: 16px;
    background: white;
    font-family: 'Montserrat';
	
}
.form-style-6 h1{
    background: #0dbaab;
    padding: 20px 0;
    font-size: 140%;
    font-weight: 300;
    text-align: center;
    color: #fff;
    margin: -16px -16px 16px -16px;
   font-family: 'Montserrat';
}


.form-style-6 input[type="text"],
.form-style-6 input[type="date"],
.form-style-6 input[type="datetime"],
.form-style-6 input[type="email"],
.form-style-6 input[type="number"],
.form-style-6 input[type="search"],
.form-style-6 input[type="time"],
.form-style-6 input[type="url"],
.form-style-6 input[type="password"],
.form-style-6 textarea,
.form-style-6 select 
{
    -webkit-transition: all 0.30s ease-in-out;
    -moz-transition: all 0.30s ease-in-out;
    -ms-transition: all 0.30s ease-in-out;
    -o-transition: all 0.30s ease-in-out;
    outline: none;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    background: #fff;
    margin-bottom: 4%;
    border: 1px solid #ccc;
    padding: 3%;
    color: #555;
    font: 95% Arial, Helvetica, sans-serif;
	font-family: 'Montserrat';
}
.form-style-6 input[type="text"]:focus,
.form-style-6 input[type="date"]:focus,
.form-style-6 input[type="datetime"]:focus,
.form-style-6 input[type="email"]:focus,
.form-style-6 input[type="number"]:focus,
.form-style-6 input[type="search"]:focus,
.form-style-6 input[type="time"]:focus,
.form-style-6 input[type="url"]:focus,
.form-style-6 textarea:focus,
.form-style-6 select:focus
{
    box-shadow: 0 0 5px #43D1AF;
    padding: 3%;
    border: 1px solid #43D1AF;
	font-family: 'Montserrat';
}

.form-style-6 input[type="submit"],
.form-style-6 input[type="button"]{
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    padding: 3%;
    background: #33568e;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}
.form-style-6 input[type="submit"]:hover,
.form-style-6 input[type="button"]:hover{
    background: #2EBC99;
}
body {
    font-family: 'Montserrat';font-size: 15px;
}
.hide{
display:none;
}

.loge {
    border: 1px solid;
    padding: 20px; 
    width: 1200px;
    resize: both;
    overflow: auto;
	text-align: left
}
.other_details{
    
    padding: 20px; 
    width: 1200px;
    resize: both;

	text-align: left;
	
}
</style>

<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-F72SBSX0JW"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-F72SBSX0JW');
</script>
</head>
<body>
<center>
<br><br>
<div class="form-style-7">
<h1>Welcome to IDPA ERROR(s) Database</h1>
</div>
<h3><font color="red">Unauthorized Access will not be Tolerated !!</font></h3>
<center>
<div class="container">
<a href="logout">
<button class="button button1">If this happened by mistake, Logout now</button>
</a>
<br><br>


<p align="left">
<b><u>GUIDELINES:</u></b><br>
Welcome to IDPA Error(s) Review Dashboard. Click View button to view guidelines on using this platform.<br><br>
<button onclick="myFunction()">View Guidelines</button>
<br>
</p>



<div id="myDIV" style="display:none">
<br><br>
<p align="left">
1. Here you will be able to review errors by marking them valid or invalid, add possible solution as support or engineering comments and much more.<br>
2. Please remember to always first click on BACKUP DATABASE to create a copy of DB before making any changes so that in case anything goes wrong, we'll be able to get back to state where we started.<br>
3. Remember, in case of any confusion with the error message, you can download the reference logfile to better understand that error message and eliminate or add solution to it.<br>
4. At the same time, keep a note that entering the difference between start and end range greater than 100 will cause page to take huge amount of time to load, therefore, it is advisable to have range values in an increment of 50 or so.<br>
5. Finally, you would see that each statement has a save button next to it, instead of multiple statements saved together, this is to avoid the classic deadlock situation in the database so that if two of you are editing the same error message, then it doesn't effect other error messages. <br><br>
Now that you understand how to use this platform, Let's start by setting the range of errors that you would like to review today. You can input any value but remember, it should be less than the total numbers of errors in database. Let's say if the total number of errors in database is 5000, then you can enter something as '100' to '150' or '4950' to '5000'. <br>
</p>
<br><br>
</div>




<br>
<div class="form-style-8">
<h1>Database Statistics</h1>
</div>
<br>
  <div class="card-columns">
    <div class="card bg-light">
      <div class="card-body text-center">
        <p class="card-text"><b>Total Errors in DB : {{totalerrors}}</b></p>
      </div>
    </div>
<!--
    <div class="card bg-light">
      <div class="card-body text-center">
        <p class="card-text"><b>Valid Errors Found  : {{totalvaliderrors}}</b></p>
      </div>
    </div>
    <div class="card bg-light">
      <div class="card-body text-center">
        <p class="card-text"><b>Errors with Solution  : {{errors_updated}}</b></p>
      </div>
    </div>
    <div class="card bg-light">
      <div class="card-body text-center">
        <p class="card-text"><b>Errors without Solution  : {{errors_left_to_update}}</b></p>
      </div>
    </div>
-->	
    <div class="card bg-light">
      <div class="card-body text-center">
        <p class="card-text"><b>Errors Added (last 24hrs) : {{errors_added_last_24hrs}}</b></p>
      </div>
    </div>
	
	
    <div class="card bg-light">
      <div class="card-body text-center">
        <p class="card-text"><b>Errors Updated (last 24hrs) : {{errors_updated_last_24hrs}}</b></p>
      </div>
    </div> 	
	
  </div>
  



<br><br>
<div class="form-style-8">
<h1>Top 3 Contributors</h1>
</div>
<br>
<div class="card-columns">
{% for h in hero_dict %}
    <div class="card bg-light">
      <div class="card-body text-center">
        <p class="card-text"><b>{{h[0][0]}} : {{h[1]}}</b></p>
      </div>
    </div>
{% endfor %}	
</div>

<br><br>

<div class="form-style-8">
<h1>Database Controls</h1>
</div>
<br>
Let's start by <a href="backup_db">taking backup of Database</a>

 
<table>
<tr>

<td>
<form method=POST action="reviewboard">
<br><br>
<strong>
<p align="left">Start by selecting Database Listing Range : <br><br>
Start Range : &nbsp;&nbsp;
<input name='list_id_start' type="text" value="{{list_start}}"></p>
<p align="left">End Range : &nbsp;&nbsp;
<input name='list_id_end' type="text" value="{{list_end}}"></p>
</strong>
</a>
<p align="left"><input type="submit" value='Fetch'></p>
</form>
</td>

<td>
<form method=POST action="reviewboard">
<strong>
<p align="left">Alternatively, Search for an Error in Database : 
<br><br>
{% if search_text == 'Enter text to search' %}
<input name='search_text' type="text" size="50">
{% else %}
<input name='search_text' type="text" size="50" value="{{search_text}}">
{% endif %}
</p>
</strong>
</a>
<p align="left"><input type="submit" value='Search' ></p>
</form>

{% if search_data_found_text %}
<br><br>
<b><font color="Red">{{search_data_found_text}}</font></b>
{% endif %}
</td>

</tr>
</table>





{% with messages = get_flashed_messages() %}
  {% if messages %}
    <ul class=flashes>
    {% for message in messages %}
	{%if 'error' in message or 'ERROR' in message %}
      <br><br><li><b><font color="red">{{ message }}</font></b></li>
	 {%else%}
      <br><br><li><b><font color="green">Update : {{ message }}</font></b></li>
     {%endif%}	 
    {% endfor %}
    </ul>
  {% endif %}
 <br><br><br>
{% endwith %}
{% block body %}{% endblock %}







</div>
</center> 


{% if scrolltoview %}
<script>
    $(function() {
       $("html, body").animate({ scrollTop: $("#{{scrolltoview}}").offset().top }, 500);
    });
</script>
{% endif %}



<script>
function scrolltoitem(item) {
        var elmnt = document.getElementById(item);	
        elmnt.scrollIntoView();
}
</script>

<script>
function myFunction() {
  var x = document.getElementById("myDIV");
  if (x.style.display === "none") {
    x.style.display = "block";
  } else {
    x.style.display = "none";
  }
}
</script>

<br><br>
<div class="form-style-8">
<h1>Database Listing</h1>
</div>

<!-- TABLE DIV START -->	
<div class="content mt-3">
<!-- TABLE START -->
<table id="bootstrap-data-table" class="table table-striped table-bordered dataTable no-footer" role="grid" aria-describedby="bootstrap-data-table_info">

    <colgroup>
       <col span="1" style="width: 2%;">
       <col span="1" style="width: 3%;">
       <col span="1" style="width: 10%;">
       <col span="1" style="width: 3%;">
       <col span="1" style="width: 7%;">
       <col span="1" style="width: 7%;">
       <col span="1" style="width: 7%;">
       <col span="1" style="width: 7%;">	   
       <col span="1" style="width: 7%;">
       <col span="1" style="width: 7%;">	   
       <col span="1" style="width: 4%;">		   
    </colgroup>


<!-- TABLE HEAD START-->
<thead class="sorting_desc">
                <tr role="row">
                    <th>ID</th>
                    <th>UPDATED</th>
                    <th>ERROR</th>
                    <th>VALID</th>
                    <th>UPDATED BY</th>
                    <th>JIRA_BUG</th>
                    <th>S360_KB</th>
                    <th>LIGHTNING_KB</th>					
                    <th>SUPPORT_COMMENT</th>
                    <th>ENGG_COMMENT</th>
                    <th>SAVE DATABASE</th>					
                </tr>
</thead>
<!-- TABLE HEAD END -->
<!-- TBODY START -->			
<tbody>
			{% for x in data %}
			
			<tr role="row" class="odd" >
			<td class="sorting_1">{{x[0]}}</td>
			{% if x[3] %}
			<td class="sorting_1">{{x[3].split('.')[0]}}</td>
			{% else %}
		    <td class="sorting_1">{{x[3]}}</td>
			{% endif %}
			<td class="sorting_1"><b><font color="red">{{x[5]}}</font></b></td>
			<td class="sorting_1">
			<select id='validity_{{x[0]}}' >
            {%if 'True' in x[6] %}			
			<option value="True" selected="selected">True</option>			
			<option value="False">False</option>
			{%else%}
			<option value="False" selected="selected">False</option>			
			<option value="True">True</option>			
			{%endif%}
			</select>
			</td>
			
			<!--<td class="sorting_1"><input id='username_{{x[0]}}' type="text" value="{{x[8]}}"></td>			
			<td class="sorting_1"><input id='bugref_{{x[0]}}' type="text" value="{{x[9]}}"></td>
			<td class="sorting_1"><input id='kbref_{{x[0]}}' type="text" value="{{x[10]}}"></td>-->
			
			
			<td class="sorting_1"><textarea id='username_{{x[0]}}' rows="1" wrap="soft">{{x[8]}}</textarea></td>
			<td class="sorting_1"><textarea id='bugref_{{x[0]}}' rows="1" wrap="soft">{{x[9]}}</textarea></td>
			<td class="sorting_1"><textarea id='kbref_{{x[0]}}' rows="1" wrap="soft">{{x[10]}}</textarea></td>			
			<td class="sorting_1"><textarea id='lkb_{{x[0]}}' rows="1" wrap="soft">{{x[11]}}</textarea></td>						
			
			<!--<td class="sorting_1"><input id='sc_{{x[0]}}' type="text" value="{{x[11]}}"></td>
		        <td class="sorting_1"><input id='ec_{{x[0]}}' type="text" value="{{x[12]}}"></td>-->
        
			<td class="sorting_1"><textarea id='sc_{{x[0]}}' rows="7" wrap="soft">{{x[12]}}</textarea></td>
		    <td class="sorting_1"><textarea id='ec_{{x[0]}}' rows="7" wrap="soft">{{x[13]}}</textarea></td>
			

            <td class="sorting_1">
			<a onclick="this.href='edit_db/{{x[0]}}/' + document.getElementById('validity_{{x[0]}}').value + '/' + $('textarea#bugref_{{x[0]}}').val() + '/' + $('textarea#kbref_{{x[0]}}').val() + '/' + $('textarea#lkb_{{x[0]}}').val() + '/' + $('textarea#sc_{{x[0]}}').val().replace(/\//g, '+') + '/' + $('textarea#ec_{{x[0]}}').val() + '/' + $('textarea#username_{{x[0]}}').val() + '/{{list_start}}/{{list_end}}'">
			
			
			<!-- 			<a onclick="this.href='edit_db/{{x[0]}}/' + document.getElementById('validity_{{x[0]}}').value + '/' + document.getElementById('bugref_{{x[0]}}').value + '/' + document.getElementById('kbref_{{x[0]}}').value + '/' + document.getElementById('sc_{{x[0]}}').value + '/' + document.getElementById('ec_{{x[0]}}').value + '/' + document.getElementById('username_{{x[0]}}').value + '/{{list_start}}/{{list_end}}'">
			<button class="button button6">SAVE</button>
			</a> -->
			
			
			<button class="button button6" id="{{x[0]}}" onclick="scrolltoitem({{x[0]}})">SAVE</button>
			</a> 
			<br><br>
            <a href="download_ref_logs/{{x[1]}}">
			<button class="button button1">Download Refrence Logfile</button>
			</a>
			</td>			

<!-- TD END -->
</tr>
{% endfor %}
</tbody>
<!-- TBODY END -->
</table>
<!-- TABLE END -->
</div>
<!-- TABLE DIV END -->





















 


<br><br>
<a href="/"><button class="button button1">BACK HOME</button></a>

<br><br>
</center>





    <script>
        (function(document) {
            'use strict';

            var TableFilter = (function(myArray) {
                var search_input;

                function _onInputSearch(e) {
                    search_input = e.target;
                    var tables = document.getElementsByClassName(search_input.getAttribute('data-table'));
                    myArray.forEach.call(tables, function(table) {
                        myArray.forEach.call(table.tBodies, function(tbody) {
                            myArray.forEach.call(tbody.rows, function(row) {
                                var text_content = row.textContent.toLowerCase();
                                var search_val = search_input.value.toLowerCase();
                                row.style.display = text_content.indexOf(search_val) > -1 ? '' : 'none';
                            });
                        });
                    });
                }

                return {
                    init: function() {
                        var inputs = document.getElementsByClassName('search-input');
                        myArray.forEach.call(inputs, function(input) {
                            input.oninput = _onInputSearch;
                        });
                    }
                };
            })(Array.prototype);

            document.addEventListener('readystatechange', function() {
                if (document.readyState === 'complete') {
                    TableFilter.init();
                }
            });

        })(document);
    </script>



<script src="https://cdn.datatables.net/fixedcolumns/3.3.2/js/dataTables.fixedColumns.min.js"></script>
<script src="https://cdn.datatables.net/fixedheader/3.1.7/js/dataTables.fixedHeader.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.6/js/responsive.bootstrap.min.js"></script>
    <script src="{{url_for('static', filename='assets/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/datatables.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/dataTables.bootstrap.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/dataTables.buttons.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.bootstrap.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/jszip.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/pdfmake.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/vfs_fonts.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.html5.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.print.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.colVis.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/datatables-init.js')}}"></script>








</body>
</html>