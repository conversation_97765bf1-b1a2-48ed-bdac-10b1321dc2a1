'''
#IDPA % rules
############           %1(IDPA_VAPP:CONFIG:IN_PROGRESS)   : 
######################################################################################################

#IP address validation, Executing VCSA deployment task, 
############           %1 (DATA_DOMAIN:DEPLOY:IN_PROGRESS)
######################################################################################################

#DD OVF deployed successfully, Ddve VM deployment successfully
############           %3 (DATA_DOMAIN:DEPLOY:IN_PROGRESS, IDPA_VAPP:CONFIG:IN_PROGRESS)   : 
######################################################################################################

#Adding Nic to DDVE with portgroup name: DP-appliance-mgmt, Assigning Portgroup to all NIC of DDVE, Reconfig task initiated for VM DDVE, Portgroup assigned to all NIC of DDVE, expandDDVEDataDisks Expanding data disks for DDVE, Reconfig task completed for VM DDVE with status success, Device configuration is created for 1st DS, expandDDVEDataDisks  Device configuration is created for 2nd DS
#First attempt to connect to vCenter
#############          %5   
######################################################################################################

#DeployDdveTask run-->DDVE deployement file created
#############          %9   :
######################################################################################################

#DDVE deployed successfully, Second attempt to connect to vCenter, Third Attempt 
#getServiceInstanceWithRetries--> Able to connect to vCenter
#VCSA deployment is completed
#Executing VCSA configuration task
#Trying to assign administartor privileges to root user on VCSA = Administrator privileges assigned successfully
#Trying to set root user account expiry to never on vCenter server:**************
#License assigned successfully
#%10  :

#Datacenter created successfully. DPappliance
#Trying to get sslthumbprint for ESX:**************
#echo -n | openssl s_client -connect **************:443 2> /dev/null | openssl x509 -noout -fingerprint -sha1
#Successfully added the ESXi host [**************] in datacenter [DPappliance] of vCenter server appliance [**************]
#Check vSphere Profile-Driven Storage Service is not running 1 attempt
#vSphere Profile-Driven Storage Service:vmware-sps is running
#DeployAndConfigVcsaTask run--> VCSA configuration task is completed
#%11  :

#idpavapp.ConfigIdpaVappTask: IDPA mod   is 4400
#Successfully set NTP Server 
#ntp.server.set --servers *************** on vcsa
#timesync.set --mode NTP
#sh  l ntpdate -u ***************
#%12  :


#rm /etc/localtime
#Successfully changed timezone on ACM.
#rm /etc/localtime; ln -s /usr/share/zoneinfo/America/New_York /etc/localtime on VM: DataProtection-VCSA
#bash -c sed -i 's/^OPENLDAP_START_LDAPI=.*/OPENLDAP_START_LDAPI="yes"/g' /etc/sysconfig/openldap
#bash -c sed -i 's/^OPENLDAP_CONFIG_BACKEND=.*/OPENLDAP_CONFIG_BACKEND="ldap"/g' /etc/sysconfig/openldap
#bash -c mv /etc/openldap/slapd.conf /etc/openldap/slapd.conf.org
#bash -c touch /etc/openldap/slapd.conf
#bash -c rm -rf /etc/openldap/slapd.d/*
#bash -c slaptest -f /etc/openldap/slapd.conf -F /etc/openldap/slapd.d
#bash -c sed -i 's/^olcAccess.*/olcAccess: {0}to * by dn.exact=gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth manage by * break/g' /etc/openldap/slapd.d/cn\=config/olcDatabase\=\{0\}config.ldif 
#bash -c sed -i 's/^olcAccess.*/olcAccess: {0}to * by dn.exact=gidNumber=0+uidNumber=0,cn=peercred,cn=external,cn=auth manage by * break/g' /etc/openldap/slapd.d/cn\=config/olcDatabase\=\{0\}config.ldif
#bash -c chown -R ldap. /etc/openldap/slapd.d
#bash -c chown -R ldap. /var/lib/ldap
#bash -c chmod -R 700 /etc/openldap/slapd.d
#bash -c systemctl start slapd
#bash -c systemctl enable slapd
#ln -s '/usr/lib/systemd/system/slapd.service' '/etc/systemd/system/multi-user.target.wants/slapd.service'
#initializeOpenLdapServer-->Successfully initialized OpenLdap Server.
#Successfully created /etc/openldap/idpa_files/chrootpw.ldif
#bash -c ldapadd -Y EXTERNAL -H ldapi:/// -f /etc/openldap/idpa_files/chrootpw.ldif
#bash -c ldapadd -Y EXTERNAL -H ldapi:/// -f /etc/openldap/schema/core.ldif
#bash -c ldapadd -Y EXTERNAL -H ldapi:/// -f /etc/openldap/schema/cosine.ldif
#bash -c ldapadd -Y EXTERNAL -H ldapi:/// -f /etc/openldap/schema/nis.ldif 
#bash -c ldapadd -Y EXTERNAL -H ldapi:/// -f /etc/openldap/schema/inetorgperson.ldif
#bash -c ldapadd -Y EXTERNAL -H ldapi:/// -f /etc/openldap/schema/ppolicy.ldif
#importSchema-->Successfully import OpenLDAP Schema
#bash -c ldapadd -Y EXTERNAL -H ldapi:/// -f /etc/openldap/idpa_files/backend.ldif
#createBackendLdif--> Successfully setup backend of OpenLdap Server.
#Successfully created OpenLDAP Basedomain on ACM
#Set the password policy module
#Set the password policy config
#Set the password policy defaults
#openssl genrsa -des3 -out /etc/ssl/private/server.key
#openssl rsa -in /etc/ssl/private/server.key -out /etc/ssl/private/server.key
#openssl req -new -days 3650 -key /etc/ssl/private/server.key -out /etc/ssl/private/server.csr
#openssl x509 -in /etc/ssl/private/server.csr -out /etc/ssl/private/server.crt -req -signkey /etc/ssl/private/server.key -days 3650
#Created certificates for Secure OpenLdap
#Successfully created group and user
#Successfully saved LDAP configuration
#%13  :





#AVAMAR:DEPLOY
#%14  :

#Resource pool dataDomain is not present
#vApp create successfully. vApp Name DataDomain
#%15  :

#Deploying AVE ovf with given parameters
#entity DDVE moved sucvcessfully into Virtual App DataDomain
#All DD VM moved into DD vApp
#DD VM configured successfully
##%16  :
#
#Ovf read successfully
##%17  :
#
#DDVE VM Guest OS is running
#Assigning IP Address to DDVE
#OVF deployed successfully  #AVE
##%18  :
#
#Successfully assigned port groups to all nics
#Setting strip unit 256 on DDVE
#Added ave data disk
##%19  :
#
#Overall progress percentage = 20
##%20  :
#
#vApp create successfully. vApp Name Avamar
#All Avamar VM moved into Avamar vApp
#Avamar VM configured successfully
##%21
#
#Setting PSNT accepted on DDVE
#
#The Domainname is: #DDNAME
#ddadapter.ConfigDataDomainTask: Adding license
#Successfully executed:   icense update
#Found cloud tier license on DataDomain #optional
##%23
#
#
#Configured cloud tier successfully
#Avamar isConfigPackageReady succeeded
##%24
#
#Executing Avamar config server task
#Executing deploy DPC task
#Getting Data Protection Central configuration
#Successfully retrieved LDAP configuration
#Setting root-password and admin-password on DPC
#Confugured active tier successfully
##%25
#
#Execution of deploy DPC server task completed
#Moving Data Protection Central virtual machines into virtual app
##%28
#
#Entity DPC moved sucvcessfully into Virtual App DataProtectionCentral
##%29
#
#
#DPC vApp is powered on validating ping
#Execution of deploy DPC Vapp task completed
##%30
#
#Waiting for DPC services to start
#Successfully executed: filesys enable
##%31
#
#
#Adding user: DDBoostUser
#Successfully updated flag to not to sync VM: DPADatastoreServer with host
#Enable DDBoost call executed
#Enable nfs called executed
#Add DPA Agent call executed
#Enable admin access call executed
##%33
#
#Configure DD support call executed
#Set timezone call executed
#Set mail server call executed
#Successfully executed ntp enable
#Set ntp server call executed
##%34
#
#
#Configure primary EMC connect call executed

################################################################################################
################################################################################################
################################################################################################
################################################################################################

'''
from flask import Flask, session, render_template, request, redirect, g, url_for, Response, send_file
from flask_cors import CORS, cross_origin
from flask_restful import Resource, Api
import os
import sqlite3
import time
import subprocess
import re
from time import gmtime, strftime
import process_file
from flask import Flask, render_template, request
from flask_uploads import UploadSet, configure_uploads, IMAGES
import re, os, sys, time, datetime
import fnmatch
from collections import deque
from werkzeug import secure_filename
from random import randint
import datetime
from flask import flash
import paramiko
from werkzeug.exceptions import HTTPException, NotFound
import time
import jinja2
env = jinja2.Environment()
env.globals.update(zip=zip)


now = datetime.datetime.now()

app = Flask(__name__)
api = Api(app)
CORS(app)
app.secret_key = b'_5#y2L"F4Q8z\n\xec]/'
#app.jinja_env.filters['zip'] = zip
f1 = open('SERVER_LOG.log','a')

UPLOAD_FOLDER = 'Uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
ALLOWED_EXTENSIONS = set(['log', 'txt', '1','2','3','4','5','6','7','8','9'])

try:
    from tqdm import tqdm
except ImportError:
    class TqdmWrap(object):
        # tqdm not installed - construct and return dummy/basic versions
        def __init__(self, *a, **k):
            pass

        def viewBar(self, a, b):
            # original version
            res = a / int(b) * 100
            sys.stdout.write('\rDownloading: %.2f %%' % (res))
            sys.stdout.write('\n')
            sys.stdout.flush()

        def __enter__(self):
            return self

        def __exit__(self, *a):
            return False
else:
    class TqdmWrap(tqdm):
        def viewBar(self, a, b):
            self.total = int(b)
            self.update(int(a - self.n))  # update pbar with increment
			
			
def ssh_logs_download(ip, user, password,localfilename,remotepathfilename):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        ssh.connect(hostname=ip, username=user, password=password, timeout=5, compress = True,look_for_keys=False, allow_agent=False)
    except (socket.error,paramiko.AuthenticationException,paramiko.SSHException) as message:
        print "ERROR: SSH connection to "+ip+" failed: " +str(message)
        sys.exit(1)

    sftp = ssh.open_sftp()
    #sftp = ssh.open_sftp()
    with TqdmWrap(ascii=True, unit='b', unit_scale=True) as pbar:
        sftp.get(remotepathfilename,localfilename, callback=pbar.viewBar)
    #cbk, pbar = tqdmWrapViewBar(ascii=True, unit='b', unit_scale=True)
    #sftp.get(remotepathfilename,localfilename,callback=cbk)
    #time.sleep(2)	
    sftp.close()
    #time.sleep(2)	
    ssh.close()
def ssh_ctrl_with_error_read_support(ip, user, password,cmd):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        ssh.connect(hostname=ip, username=user, password=password, timeout=5, compress = True,look_for_keys=False, allow_agent=False)
    except (socket.error,paramiko.AuthenticationException,paramiko.SSHException) as message:
        print "ERROR: SSH connection to "+ip+" failed: " +str(message)
        sys.exit(1)

    stdin, stdout, ssh_stderr = ssh.exec_command(cmd)
    
    error_found = "Null"
    out = stdout.read()
    #blow = ssh_stderr.read()
    if not stdout.channel.recv_exit_status() == 0:
        #print 'ERROR : ' , ssh_stderr.read()
        error_found = ssh_stderr.read()
    #print out
    stdin.flush()
    #time.sleep(2)	
    ssh.close()
    return out, error_found

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/download', methods=['POST'])
def download():
    filename = request.form['file_to_download']
    print filename	
    return send_file('Uploads\\'+filename,                       
                             attachment_filename=filename,
                             as_attachment=True)
    uploads = os.path.join(current_app.root_path, app.config['UPLOAD_FOLDER'])
    return send_from_directory(directory=uploads, filename=filename)		   
		   
@app.route('/') #HOMEPAGE
def login():
    if request.method == 'GET':
        return render_template('index.html')

@app.route('/lincoln_process', methods=['GET', 'POST'])
def lincoln_process():
    if request.method == 'POST':		
        main_data = request.form['file_name']	
        filename = main_data.split(',')[0] #selected filename by user
        lincoln_path = main_data.split(',')[1] #lincoln full path
        new_file_name = now.strftime("%Y_%m_%d_%H_%M")+'_'+str(randint(0, 91212121212))+'.log'	 #dummy file name
        ssh_logs_download('lincoln.avamar.com', 'admin', 'changeme','Uploads/'+new_file_name,lincoln_path+'/'+filename)        
        logs_excerpt, total_attempts_made, failure_observed_at, cause , resolution = process_file.process('Uploads/'+new_file_name)
        print >>f1, '================================'	
        print >>f1, 'Filename            : ' + str(new_file_name)	
        print >>f1, 'Current time        : ' + str(now.strftime("%Y_%m_%d_%H_%M"))		
        print >> f1, 'Total Attempts Made : ' + str(total_attempts_made)
        print >> f1, 'Failure Obser at    : ' + str(failure_observed_at)
        print >> f1, 'Cause               : ' + str(cause)
        print >> f1, 'Resolution          : ' + str(resolution)		
        #print >> f1, 'Logs Excerpt        : ' + str(logs_excerpt)					
	
        print >>f1, '================================'		
        f1.flush()
        if cause == 'Undefined': #we are not deleting the file and letting it sit so that we can analyze it later for issues in it
            f1.flush()			
            return render_template('add_file.html')    
        if total_attempts_made == 0:
                return render_template('no_issues.html')            		
        try:			
            if int(failure_observed_at) == 0:
                f1.flush()				
                #os.remove('Uploads/'+new_file_name)								
                return render_template('no_issues.html')
        except:#this mean we are using a custom elif statement from process_file so return normally
            f1.flush()			
            #os.remove('Uploads/'+new_file_name)							
            return render_template('processed_file.html', main_data = zip(logs_excerpt,failure_observed_at,cause,resolution) ,logs_excerpt = logs_excerpt, total_attempts_made=total_attempts_made, failure_observed_at= failure_observed_at, cause=cause , resolution=resolution, filename=filename,lincoln_path=lincoln_path,new_file_name=new_file_name) 						                			
       
        f1.flush()
        #return render_template('processed_file.html', logs_excerpt = logs_excerpt, total_attempts_made=total_attempts_made, failure_observed_at= failure_observed_at, cause=cause , resolution=resolution,filename=filename,lincoln_path=lincoln_path,new_file_name=new_file_name)	
        return render_template('processed_file.html', main_data = zip(logs_excerpt,failure_observed_at,cause,resolution), total_attempts_made=total_attempts_made, lincoln_path=lincoln_path, filename=filename, new_file_name=new_file_name)									
			
@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        if 'file' not in request.files:
            if request.form['lincoln_path']:
                lincoln_path = request.form['lincoln_path']

                try:				
                    print str(lincoln_path.split('/home/<USER>/logs/')[1])				
                    if len(str(lincoln_path.split('/home/<USER>/logs/')[1])) > 0:
                        last_path_part = lincoln_path.split('/home/<USER>/logs/')[1]					
                        if last_path_part.split('/')[0].isdigit():
                            file_list_obj = []						
                            dir_list = ssh_ctrl_with_error_read_support('lincoln.avamar.com', 'admin', 'changeme','ls '+lincoln_path)
                            file_list = dir_list[0].split('\n')
                            #print file_list							
                            for file in file_list:	
							 
                                if ('.log' in file and 'server' in file):
                                    file_list_obj.append(file) #contains the list of server.log files
                            if len(file_list_obj) > 0:	
                                #print file_list_obj									
                                return render_template('select_file.html', file_list_obj=file_list_obj, lincoln_path=lincoln_path)								
                            else:										
                                flash('No valid server.log found in path')
                                return render_template('index.html')                       
                        else:
                            flash('Invalid Path to Lincoln Given')
                            return render_template('index.html')                							
                    else:
                        flash('Please select the File First')
                        return render_template('index.html')                        					
                except:
                    flash('Exception : Invalid path to lincoln given. \nCould be either due to :\n1) You are not using SR_Number in path\n2) You are using "~" sign')
                    return render_template('index.html')                    				

            flash('Please select the File First')
            return render_template('index.html')
        file = request.files['file']
        new_file_name = now.strftime("%Y_%m_%d_%H_%M")+'_'+str(randint(0, 91212121212))+'.log'	  		
        if file.filename == '':
            flash('No selected file')
            return render_template('index.html')
        if file and allowed_file(file.filename):
	
            filename = secure_filename(new_file_name)
            file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))			
            logs_excerpt, total_attempts_made, failure_observed_at, cause , resolution = process_file.process('Uploads/'+new_file_name)
            print >>f1, '================================'	
            print >>f1, 'Filename            : ' + str(new_file_name)	
            print >>f1, 'Current time        : ' + str(now.strftime("%Y_%m_%d_%H_%M"))					
            print >> f1, 'Total Attempts Made : ' + str(total_attempts_made)
            print >> f1, 'Failure Obser at    : ' + str(failure_observed_at)
            print >> f1, 'Cause               : ' + str(cause)
            print >> f1, 'Resolution          : ' + str(resolution)		
            #print >> f1, 'Logs Excerpt        : ' + str(logs_excerpt)

            print  '================================'	
            print  'Filename            : ' + str(new_file_name)	
            print  'Current time        : ' + str(now.strftime("%Y_%m_%d_%H_%M"))					
            print  'Total Attempts Made : ' + str(total_attempts_made)
            print  'Failure Obser at    : ' + str(failure_observed_at)
            print  'Cause               : ' + str(cause)
            print  'Resolution          : ' + str(resolution)				
	
            print >>f1, '================================'			
            f1.flush()			
            if cause == 'Undefined': #we are not deleting the file and letting it sit so that we can analyze it later for issues in it
                f1.flush()			
                return render_template('add_file.html')     			
            try:			
                if int(failure_observed_at) == 0:
                    f1.flush()				
                    return render_template('no_issues.html')
            except:#this mean we are using a custom elif statement from process_file so return normally
                f1.flush()			
                return render_template('processed_file.html', main_data = zip(logs_excerpt,failure_observed_at,cause,resolution), total_attempts_made=total_attempts_made, filename=filename,new_file_name=new_file_name)				
       
            f1.flush()
            
            return render_template('processed_file.html', main_data = zip(logs_excerpt,failure_observed_at,cause,resolution), total_attempts_made=total_attempts_made, filename=filename,new_file_name=new_file_name)							
        else:
            flash('Invalid File Extension(only "log and txt")')		
            return render_template('index.html')			
	  
@app.route('/admin', methods=['GET', 'POST'])
def admin_activity():
    current_time = time.time()
    deleted_file = []
    for f in os.listdir('Uploads'):
        #print f
        creation_time = os.path.getctime('Uploads/'+f)
        if (current_time - creation_time) // (24 * 3600) >= 7:
            os.unlink('Uploads/'+f)
            print('{} removed'.format('Uploads/'+f))
            deleted_file.append('Uploads/'+f)
    if deleted_file:
        print 'Files Deleted'
    else:
        print 'No File to delete'	
    return render_template('index.html')		

		
			
if __name__ == '__main__':
    app.run(host='0.0.0.0', port='80', debug=True, threaded=True)
	
	
	
	
'''
try:	
    line_num = 0
    search_phrase = "Are there critical components failed = true"
    if search_phrase in file_part_1:
        print 'its there'    
    #Creating a list to store all events
    all_failure_events = []
    
    #B  ow code will find the line containing the above string
    for line in file_part_1:
        line_num += 1
        if line.find(search_phrase) >= 0:
            found_line = line_num
            all_failure_events.append(found_line)		
    #This has the first ever event that happened information
    print found_line
except Exception as e:
    exc_type, exc_obj, exc_tb = sys.exc_info()
    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
    if str(exc_type) == "<type 'exceptions.NameError'>":
        print 'No Critical Component Failure Found'	
    else:		
        print(exc_type, fname, exc_tb.tb_lineno)
        print 'Exception Occurred while reading the input file'
	



try:	
    #Read the File contents
    f = open(filename, 'r')
    line_num = 0
    search_phrase = "Are there critical components failed = true"
    
    #Creating a list to store all events
    all_failure_events = []
    
    #B  ow code will find the line containing the above string
    for line in f.readlines():
        line_num += 1
        if line.find(search_phrase) >= 0:
            found_line = line_num
            all_failure_events.append(found_line)		
    #This has the first ever event that happened information
    #print found_line
    #print all_failure_events
    f = open(filename, 'r')
    for i, line in enumerate(f):
        if i == found_line - 4: #Finding the overall percentage where it failed
            first_event_line = str(line.split('Overall progress percentage = ')[1])
            print 'First Workflow Failure Observed at : ' + first_event_line
            print '------------------------'			
    f.close()
	#Checking if there are any other attempts made where workflow attempts were made and it was more than the first attempt failure
    for x in all_failure_events:
        f = open(filename, 'r')
        for i, line in enumerate(f):		
            if i == x - 4: #Finding the overall percentage where it failed
                other_lines = str(line.split('Overall progress percentage = ')[1])			
                if int(other_lines) > int(first_event_line):				
                    print 'Workflow also failed at : ' + str(other_lines)
                    print 'This might be due to multiple attempts but at least a progress was made'
        f.close()					 
		
except Exception as e:
    exc_type, exc_obj, exc_tb = sys.exc_info()
    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
    if str(exc_type) == "<type 'exceptions.NameError'>":
        print 'No Critical Component Failure Found'	
    else:		
        print(exc_type, fname, exc_tb.tb_lineno)
        print 'Exception Occurred while reading the input file'

#Put this in Try and Except and check if we have firs_event only then process b  ow...		
if int(first_event_line) == 13:	
    stages_for_13 = ['Successfully changed timezone on ACM', 'initializeOpenLdapServer-->Successfully initialized OpenLdap Server.', 'importSchema-->Successfully import OpenLDAP Schema','createBackendLdif--> Successfully setup backend of OpenLdap Server.','Successfully created OpenLDAP Basedomain on ACM','Set the password policy module','Set the password policy config','Set the password policy defaults','Created certificates for Secure OpenLdap','Successfully created group and user','Successfully saved LDAP configuration']
    for stage in stages_for_13:	
        #if 'Successfully changed timezone on ACM' not in open(filename).read():
        #    print 'Timezone found'
        if stage not in open(filename).read():
            print 'Stage not Found : ' + str(stage)		

if "Setting appliance state back to ESRS_ACM_CONFIGURED" in line:
    print 'Rollback attempt was made'		
    #f = open(filename, 'r')
    #for line in f:	
    #    if 'Successfully changed timezone on ACM' in line:
    #        print 'Failed configuring Timezone on ACM'
    #    elif 'initializeOpenLdapServer-->Successfully initialized OpenLdap Server.' in line:
    #          print 'Failed to Initialize OpenLdap Server'		
    #    elif 'importSchema-->Successfully import OpenLDAP Schema' in line:
    #          print 'Failed to import OpenLDAP Schema'		
    #    elif 'createBackendLdif--> Successfully setup backend of OpenLdap Server.' in line:
    #          print 'Failed to Successfully setup backend of OpenLdap Server'		
    #    elif 'Successfully created OpenLDAP Basedomain on ACM' in line:
    #          print 'Failed to create OpenLDAP Basedomain on ACM'		
    #    elif 'Set the password policy module' in line:
    #          print 'Failed to Set the password policy module'		
    #    elif 'Set the password policy config' in line:
    #          print 'Failed to Set the password policy config'		
    #    elif 'Set the password policy defaults' in line:
    #          print 'Failed to Set the password policy defaults'		
    #    elif 'Created certificates for Secure OpenLdap' in line:
    #          print 'Failed to Create certificates for Secure OpenLdap'		
    #    elif 'Successfully created group and user' in line:
    #          print 'Failed to Successfully created group and user'		
    #    elif 'Successfully saved LDAP configuration' in line:			
    #        print 'Failed to Successfully saved LDAP configuration'	
        #print '------------------------------'			
        			
		
sys.exit()
#####################################################################################
hostname_counter = 0 
for i, line in enumerate(f):
    #if hostname_counter == 0:
        if "Are there critical components failed = true":
            print 'Failure Found at : ' + str(i+1)
            hostname_counter = hostname_counter + 1		



    #if "IDPA mod   is 4400" in line:
    #    print '============================'	
    #    print 'This is IDPA 2.2 Setup'
    #if "Reverse lookup successful.. Hostname:" in line:
    #    if hostname_counter == 0:	
    #        print line.split('Reverse lookup successful.. Hostname: ')[1]
    #        hostname_counter = hostname_counter + 1			
    #   		
    #if "Overall progress percentage" in line:
    #    last_completed_percentage = line.split('Overall progress percentage = ')[1]
    #if "Successfully deployed OVF for VM: DataProtection-VCSA" in line:
    #    print 'VCSA Deployed Successfully'	
    #if "Able to connect to vCenter" in line:
    #    print 'VCSA Connected'	
    #if "Unable to connect to vCenter in " in line:
    #    if line.split('Unable to connect to vCenter in ')[1].split('attempt')[0] > 2:
    #        print 'Unable to connect to VC in 3 attempts'	
    #if "100 for action : DATA_DOMAIN:DEPLOY" in line:
    #    print 'DDVE Deployed Successfully'	
    #if "Trying to set root user account expiry to never on vCenter server" in line: #vcenter ip
    #    print line.split('Trying to set root user account expiry to never on vCenter server:')[1].strip()
    #if "Successfully added the ESXi host" in line: #Esxi IP
    #    print line.split('Successfully added the ESXi host ')[1].split(']')[0].strip('[')
    #if "Successfully set NTP Server" in line:
    #    print 'Successfully set NTP Server : ' + str(line.split('Successfully set NTP Server ')[1]	)
'''