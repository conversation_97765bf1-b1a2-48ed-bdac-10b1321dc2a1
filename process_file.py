import re, os, sys, time, datetime
import fnmatch
from collections import deque

import percentage_failure

#First thing first, finding the failure point first.
#For this, we'll look at the first ever critical component failure event and also the very last one.
def process(filename):
    #Master List  
    
    all_file_logs_list = []
    failure_observed_at_list = []
    resolution_list = []
    cause_list = []	
    	
    total_attempts_made = 0
    failure_observed_at = 0	
    cause = 0
    resolution = 0	
     
    #Splitting the file first and foremost to avoid multiple attempts confusion
    match_string = '-restadapter.ConfigInfoService: Populating network settings'
    try:
        myfile = open(filename).read()
    except:
        print('File not found')
        return -1	

    total_file_parts = myfile.split(match_string)

    
    for x in range(len(total_file_parts)): #iterating each attempt to find out the problem

        save_50_lines = [] #opening a list item
        file_part_1 = total_file_parts[x] #Grabbing each segment of the file and identifying the problem at that section
        file_part_1_list = 	file_part_1.split('\n')	
        #if x == 4:
        #    print file_part_1_list
        indices = [i for i, s in enumerate(file_part_1_list) if 'Are there critical components failed = true' in s]
        print('Attempt#'+str(x) + 'Indices : ' + str(indices))
        if indices:
            sub_file_part_1_list = []
            total_attempts_made = total_attempts_made + 1 #New method to accurately find out the total number of failures in a given log file
            for i in indices:
                break_string = file_part_1_list[i-3] #Using 3 because we want to strip the text right at the ERROR spot and get the Overall Percentage where it failed
                #if x == 3:					
                #    print 'FILE LINES PRINTING'
                #    print file_part_1_list[i-25]				
                #    print file_part_1_list[i-24]								
                #    print file_part_1_list[i-23]								
                #    print file_part_1_list[i-22]								
                #    print file_part_1_list[i-21]												
                #    print file_part_1_list[i-20]								
                #    print file_part_1_list[i-19]								
                #    print file_part_1_list[i-18]								
                #    print file_part_1_list[i-17]												
                main_line_becomes = i - 40 #we are going 20 steps back in the file list to avoid reverse order printing				
                for yy in range(30): #This is our log excerpt
                    try:
                        save_50_lines.append(file_part_1_list[main_line_becomes+yy])					
                    except:
                        pass					
                    try:
                        save_50_lines.append(file_part_1_list[main_line_becomes-yy])					
                    except:
                        pass											
                percentage_where_workflow_failed = break_string.split('Overall progress percentage = ')[1]
				
				#Further split the file, new mechanism
				#In this new mechanism, we are just putting last 50 lines from the File_part_1 to the sub_file_part to avoid any duplicate error founds
                for rr in reversed(range(50)):
                    sub_file_part_1_list.append(file_part_1_list[i-rr])

                sub_file_part_1	= "\n".join(sub_file_part_1_list)
				
				
                ##Further split the file only till this point
                #sub_file_part_1 = file_part_1.split(break_string)[0]
                #if x == 2:				
                #    print 'SUB FILE PRINTING'				
                #    print sub_file_part_1				 
                ##print sub_file_part_1[1]
                ##print sub_file_part_1[2]
                ##print sub_file_part_1[3]				
                break	
            
            #Create Subfile List
            failure_observed_at	= str(percentage_where_workflow_failed)
		
            #sub_file_part_1_list = sub_file_part_1.split('\n')
            
           
            
            #Printing the MessageBox with information
            cause, resolution = percentage_failure.failed_at_percentage(percentage_where_workflow_failed, sub_file_part_1, sub_file_part_1_list)
            print('failure_observed_at : ' + str(failure_observed_at))
            #print 'Cause      : ' + str(cause)
            #print 'Resolution : ' + str(resolution)			
            #Pressing information in the Lists for Jinja Display
            all_file_logs_list.append(save_50_lines)
            cause_list.append(cause)
            failure_observed_at_list.append(failure_observed_at)
            resolution_list.append(resolution)				
            print('--------------------------------------------------')
            
        elif 'ApplianceException occured while configuring' in file_part_1:#DPA License specific issue
            print('Using Second Workflow Method')
            save_50_lines = [] #opening a list item	
            failure_observed_at = 'Undefined, Maybe Trimmed file to calculate correct'			
            cause	= 'DPA License Issue'
            resolution = 'This could be due to incorrect License File supplied or DPA License files has issues such as "#" in the address parameter or extra space or maybe incorrect License Type'			
            for idx, line in enumerate(file_part_1_list):  
                if 'ApplianceException occured while configuring' in line:
                    print('Failure Index is : ' + str(idx))
                              				
        
                    try:
                        for x in range(20):
                                save_50_lines.append(file_part_1_list[idx - x])			
                                save_50_lines.append(file_part_1_list[idx + x])
                    except:			
                        pass
            #Pressing information in the Lists for Jinja Display
            all_file_logs_list.append(save_50_lines)
            cause_list.append(cause)
            failure_observed_at_list.append(failure_observed_at)
            resolution_list.append(resolution)						
        else:
            save_50_lines = [] #opening a list item	
            failure_observed_at = 0			
            cause = 0
            resolution = 0                   		
						
    print('Total : ' + str(total_attempts_made))
    return  all_file_logs_list,  total_attempts_made  , failure_observed_at_list, cause_list , resolution_list  	