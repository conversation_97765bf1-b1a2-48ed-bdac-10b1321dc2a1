3.5.4 (Bleeding Edge)
5b0bb53fcc647ea13b30aaebe89bc18983c4a1b2
o:Sass::Tree::RootNode:@children[o:Sass::Tree::MixinDefNode:
@nameI"box-shadow:ET:
@args[ :@splato:!Sass::Script::Tree::Variable;I"shadow;	T:@underscored_nameI"shadow;	T:
@linei:@source_rangeo:Sass::Source::Range	:@start_poso:Sass::Source::Position;i:@offseti:
@end_poso;;i;i:
@fileI"&bootstrap/mixins/_box-shadow.scss;	T:@importero: Sass::Importers::Filesystem:
@rootI"G/var/www/html/products/html/admintemplates/sufee-admin/assets/scss;	T:@real_rootI"G/var/www/html/products/html/admintemplates/sufee-admin/assets/scss;	T:@same_name_warningso:Set:
@hash} F:@filename@:
@options{ ;[u:Sass::Tree::IfNodeu[o:!Sass::Script::Tree::Variable:
@nameI"enable-shadows:ET:@underscored_nameI"enable_shadows;T:
@linei:@source_rangeo:Sass::Source::Range	:@start_poso:Sass::Source::Position;	i:@offseti:
@end_poso;
;	i;i:
@fileI"&bootstrap/mixins/_box-shadow.scss;T:@importero: Sass::Importers::Filesystem:
@rootI"G/var/www/html/products/html/admintemplates/sufee-admin/assets/scss;T:@real_rootI"G/var/www/html/products/html/admintemplates/sufee-admin/assets/scss;T:@same_name_warningso:Set:
@hash} F:@filename@:
@options{ 0[o:Sass::Tree::PropNode;[I"box-shadow;T:@value[o; ;I"shadow;T;I"shadow;T;	i;
o;	;o;
;	i;i;o;
;	i;i;@;@
;@;@:
@tabsi :@prop_syntax:new:@children[ ;0;@;	i;
o;	;o;
;	i;i
;o;
;	i;i;@;@
:@name_source_rangeo;	;@ ;o;
;	i;i;@;@
:@value_source_rangeo;	;o;
;	i;i;@!;@;@
;0;@;i;o;	;o;;i;i;o;;i;i$;@;@:@has_childrenT;0;@:@templateI"\@mixin box-shadow($shadow...) {
  @if $enable-shadows {
    box-shadow: $shadow;
  }
}
;	T;i;o;	;o;;i;i;o;;i;i;@;@; T