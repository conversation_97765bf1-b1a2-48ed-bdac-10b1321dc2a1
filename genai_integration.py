import openai
import json
from datetime import datetime
import logging

class LogAnalysisAI:
    def __init__(self, api_key=None):
        """Initialize GenAI integration for log analysis"""
        self.client = openai.OpenAI(api_key=api_key) if api_key else None
        self.logger = logging.getLogger(__name__)
    
    def analyze_error_pattern(self, error_messages):
        """Analyze error patterns and suggest root causes"""
        if not self.client:
            return {"error": "OpenAI client not configured"}
        
        prompt = f"""
        Analyze these IDPA/Data Protection log errors and provide:
        1. Root cause analysis
        2. Suggested resolution steps
        3. Severity level (Critical/High/Medium/Low)
        4. Component affected (Avamar/Data Domain/vCenter/ESX/DPA/DPC/DPS/LDAP/CDRA)
        
        Errors:
        {chr(10).join(error_messages[:10])}  # Limit to first 10 errors
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000
            )
            return {"analysis": response.choices[0].message.content}
        except Exception as e:
            self.logger.error(f"AI analysis failed: {e}")
            return {"error": str(e)}
    
    def generate_thread_summary(self, thread_info):
        """Generate intelligent thread summary"""
        thread_name, error_count, thread_stack, start_time, end_time, age, activity, owner = thread_info[:8]
        
        prompt = f"""
        Summarize this IDPA log thread in 2-3 sentences:
        
        Thread: {thread_name}
        Owner: {owner}
        Activity: {activity}
        Duration: {age}
        Error Count: {error_count}
        
        Sample errors:
        {chr(10).join(thread_stack[:3])}
        
        Focus on what went wrong and potential impact.
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"Summary generation failed: {e}"
    
    def suggest_kb_articles(self, error_message):
        """Suggest relevant KB articles based on error"""
        prompt = f"""
        For this IDPA/Data Protection error, suggest 3 relevant Dell/EMC KB article topics:
        
        Error: {error_message}
        
        Format as:
        1. [Topic] - Brief description
        2. [Topic] - Brief description  
        3. [Topic] - Brief description
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=300
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"KB suggestion failed: {e}"
    
    def predict_resolution_time(self, thread_data):
        """Predict resolution time based on error patterns"""
        prompt = f"""
        Based on this IDPA thread data, estimate resolution time:
        
        Component: {thread_data.get('owner', 'Unknown')}
        Error Count: {thread_data.get('error_count', 0)}
        Thread Age: {thread_data.get('age', 'Unknown')}
        
        Sample errors: {thread_data.get('sample_errors', [])}
        
        Provide estimate in format: "X hours/days" with brief reasoning.
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=150
            )
            return response.choices[0].message.content
        except Exception as e:
            return "Estimation unavailable"
    
    def generate_incident_report(self, analysis_results):
        """Generate formatted incident report"""
        prompt = f"""
        Create a professional incident report for IDPA log analysis:
        
        Log Duration: {analysis_results.get('log_duration', 'Unknown')}
        Total Errors: {analysis_results.get('total_errors', 0)}
        Errors Eliminated: {analysis_results.get('errors_eliminated', 0)}
        Critical Threads: {len(analysis_results.get('critical_threads', []))}
        
        Include:
        1. Executive Summary
        2. Key Findings
        3. Recommended Actions
        4. Next Steps
        
        Keep it concise and professional.
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=800
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"Report generation failed: {e}"

# Integration with existing analyzer
def enhance_analyzer_with_ai(logfilename, api_key=None):
    """Enhanced version of main_analyzer with AI capabilities"""
    import mainanalyzer as anlz
    
    # Run original analysis
    ret_code, main_errors_stack, fullerrorstack, total_errors_count, \
    total_errors_removed, total_errors_left, percentage_reduction, \
    allthreads_list, log_start_time, log_end_time, logage = anlz.main_analyzer(logfilename, False)
    
    if not ret_code:
        return ret_code, main_errors_stack, fullerrorstack, total_errors_count, \
               total_errors_removed, total_errors_left, percentage_reduction, \
               allthreads_list, log_start_time, log_end_time, logage
    
    # Add AI enhancements
    ai_analyzer = LogAnalysisAI(api_key)
    
    # Enhance thread data with AI insights
    enhanced_threads = []
    for thread_info in allthreads_list:
        thread_dict = {
            'name': thread_info[0],
            'error_count': thread_info[1],
            'owner': thread_info[7],
            'age': thread_info[5],
            'sample_errors': thread_info[2][:3] if len(thread_info[2]) > 0 else []
        }
        
        # Add AI-generated insights
        ai_summary = ai_analyzer.generate_thread_summary(thread_info)
        resolution_time = ai_analyzer.predict_resolution_time(thread_dict)
        
        # Append AI data to thread info
        enhanced_thread = list(thread_info) + [ai_summary, resolution_time]
        enhanced_threads.append(enhanced_thread)
    
    # Generate overall analysis
    analysis_results = {
        'log_duration': logage,
        'total_errors': total_errors_count,
        'errors_eliminated': total_errors_removed,
        'critical_threads': [t for t in enhanced_threads if int(t[1]) > 5]
    }
    
    pattern_analysis = ai_analyzer.analyze_error_pattern(main_errors_stack)
    incident_report = ai_analyzer.generate_incident_report(analysis_results)
    
    return (ret_code, main_errors_stack, fullerrorstack, total_errors_count,
            total_errors_removed, total_errors_left, percentage_reduction,
            enhanced_threads, log_start_time, log_end_time, logage,
            pattern_analysis, incident_report)