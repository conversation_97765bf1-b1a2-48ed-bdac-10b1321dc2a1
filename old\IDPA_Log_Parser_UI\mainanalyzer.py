import re, os, sys, time, datetime, logging, sqlite3
from collections import Counter
from operator import itemgetter
from logging.handlers import RotatingFileHandler
from logging import handlers
reload(sys)
sys.setdefaultencoding('utf8')
##################
## LOGGING
##################
logging.getLogger("paramiko").setLevel(logging.INFO)
logger = logging.getLogger('')
logger.setLevel(logging.INFO)
format = logging.Formatter("%(asctime)s %(threadName)s %(name)s [%(levelname)s]  %(message)s")
ch = logging.StreamHandler(sys.stdout)
ch.setFormatter(format)
logger.addHandler(ch)

#####################
# NAMING CONVENTION
#####################
#all_thread_dict (dict) = contains all log messages per thread
#thread_dict (dict) = contains all threads with only errors log messages
#newthreaddict (dict) = contains threads with number of times word ERROR has popped up in their associated log files (it has both UI and App Threads in it)
#ui_threads (dict) = contains all UI threads along with their error occurence
#app_threads (dict) = contains all app threads along with their error occurence
#deleted_words_log_lines_list (list) = contains all valid words for a given log line. We simply removed stopwords from a given log line
#newvalue (list) = contains the list of only 2nd half of the log file lines, where we have removed the datetime, threadname, and threadstatus as redundant information 

############
#STATICS
############
stopwords = ['FILE', 'FALIED', 'FAILED', 'ETC', 'TO', 'FROM', 'OCCURRED', 'WHILE', 'AT','WITH','NULL','ERROR', 'NULL','LOGS','EXECUTION','MATCHER','YOU','USING','REMOTE','WANT','CONTAINS','ARE','ENABLE','SURE','EXECUTING','ENABLED', 'FAILED.', 'SERVER', 'APP','DS','LOGS.','PROXY', 'CONNECTION','FOUND','CONFIGURATION','MASTER','PARSER','OR','LOCATION','(NO','NOT','SUCH','GETTING', 'EXECUTED','INSTANCE','PARAMETERS','PERSISTED','GET','REFUSED','REFUSED)','INVOKE','OPERATION','MATCHER:','NEW','CONFIG','ENCRYPTION', 'ENCRYPTION?','EXPECT','FILE:','SDK','TASK.','IN','NULL.','PINFO','VALUE','DEPLOYMENT','IS','FOR','INIT','LOCALHOST','MESSAGE','OCCURED','SERVICE','RABBIT','ADDRESS','ADDRESS.','VALUES','PASSWORD','PASSWORDS','ON','LIST','FAILS','30000','MS)','AFTER','REBOOTING','UPDATE','IP','POPULATE','APPLIANCE','UNABLE','MQ','INIT-->', 'EXCEPTION']
#Stringdict is very special, the key string is looked upon from ERROR lines only whereas, we look for the value string from all lines of the thread
stringdict = {"Avamar isConfigPackageReady failed":"avadapter.AvamarUtil: Avamar Server: true", "ViJavaServiceInstanceProvider" : "ViJavaServiceInstanceProvider: getServiceInstanceWithRetries--> Able to connect to vCenter","updateNTPSettingsOnAvamarServer->Start":"Successfully executed command - su -c 'ntpdate", 'File system is not enabled or not running after rebooting':'The filesystem is enabled and running','updateNTPSettingsOnAvamarServer->End':"Successfully executed command - su -c 'ntpdate", "-dpcadapter.DPCUtil: updateNTPSettings->Start":"dpcadapter.DPCUtil: Successfully executed command - su -c 'echo", "-dpcadapter.DPCUtil: updateNTPSettings->End":"dpcadapter.DPCUtil: Successfully executed command - su -c 'echo"}
invalid_chars = ['AMQP Connection 127.0.0.1:5672','Timer','Finalizer', 'CONFIGURATION_ROLLBACK']  
invalid_word_names = ['thread','pool','ping', 'INFO', 'ERROR', 'DEBUG', 'WARNING', ':','=']

#########################
#FUNCTIONS
########################
def remove_invalid_error_threads(threadname, threaderrorlog, threadinfolog, origerrorlog, logfilename, total_errors_removed_phase_6, total_errors_removed_phase_7, debug):
    if debug:    
        logger.info('Working on Thread: ' + threadname)
        logger.info('Removing unwanted errors using logical & physical database..')
        logger.info('Using logical database to find solution of ' + str(len(threaderrorlog)) + ' error(s)')
    #Let's find out if errors found in a given thread match between dict, and two list. Here we are simply checking if key from stringdict partially matches an item in threaderrorlog then it becomes first valid match and now, we have to ensure that the ending statement of that error was a success or failure, which is stored in a stringdict value column. Once, again, let's say if there is an error of Avamar failing NTP, but at the end of the log, we see that Avamar NTP actually succeeded, then this becomes an invalid error message, therefore, here we are doing the same thing, we'll check if stringdict key matches error log, if yes, then check if value matches the infolog and if if both are a match, then say its a matchfound. Threaderrorlog contains only errors without the datetime, threadname and status whereas threadinfolog contains everything. Once, we'll have a match found, we'll simply delete all the entries of matchfound list from errorlog table leaving only errors that we are yet unsure of. There was another attempt that was made to use special list comprehension here, but that failed because we didn't check threadinfolog to confirm if the error was acutally resolved therefore, making it a false elimination. matchfound = [errline for errline in threaderrorlog if all (errstring not in errline for errstring, logstring in stringdict.iteritems())]        
    
    matchfound = [errline for errstring, logstring in stringdict.iteritems() for errline in threaderrorlog if errstring in errline for logline in threadinfolog if logstring in logline]
    for x in range(len(matchfound)):    
        total_errors_removed_phase_6.append(1)     
    matchfound = list(set(matchfound)) #removing duplicate entries and reusing same list 
    
    #print '------ORIGINAL LOG--------'    
    #for x in threaderrorlog:
    #    print x
    #
    ##print matchfound
    #print '--------------MATCH FOUND OLD---------------'    
    #for x in matchfound:
    #    print x
    #print '--------------MATCH FOUND UPDATED---------------'
    #for x in matchfound:
    #    print x    
    #print '----------------------------------------------'
    #sys.exit()    
    if debug:    
        logger.info('Total errors found : ' + str(len(threaderrorlog)))
        logger.info('Total errors removed : ' + str(len(matchfound)))
   

    diff =  len(threaderrorlog) - len(matchfound)    
    if debug:        
        logger.info('Total errrors left : ' + str(diff)) 
    #nonmatchedremainingerrors is a complex list comprehension output wherein, we are finding intersections between two list, one is threaderrorlog list which contains all errors stripped off of datetime, status and threadname and on the other hand we have matchfound list which contains errors that were match from our database of errors and now we just have to delete matchfound from threaderrorlog list and create a new list and name it nonmatchedremainingerrors.     
   
    
    if len(threaderrorlog) == len(matchfound):
        if debug:        
            logger.info('All errors were matched as per our Database, therefore, whole thread will be ignored')
        return True, [], [], []
    else:
        if debug:        
            logger.info('Using physical database to find solution of remaining ' + str(diff) + ' error(s)')
        master_db = sqlite3.connect('masterdb.db')
        cursor_master_db = master_db.cursor()
       
        #print 'Adding those errors to database for later review'        
        #print 'Removing elements : ' + str(matchfound)
        #nonmatchedremainingerrors = [line for err in matchfound for line in threaderrorlog if err not in line.strip()]      
        
        nonmatchedremainingerrors = [normalLine for normalLine in threaderrorlog if all(errLine not in normalLine for errLine in matchfound)]      
        #print 'Original threaderrorlog : ' + str(threaderrorlog)
        #print '---------------------------------'
        #print 'New threaderrorlog : ' + str(nonmatchedremainingerrors)
        
        solution_error, does_not_exists, exists_with_no_solution, invalid_error = [], [], [], []
        
        #Checking if any of these remaining errors exists in our database and if they do, then check if they are valid and if they are not, then ignore them, else, check if there is a solution for them.
        for eacherror in nonmatchedremainingerrors:
            cursor_master_db.execute("select * from ERRORLOG where ERROR=? LIMIT 1",(str(eacherror),))
            try:    
                out = cursor_master_db.fetchone()
                _id, logfile, ts_added, ts_updated, threadname, errorstr, is_valid, addedby, updatedby, bugref, kbref, scomm, ecomm = out[0],out[1],out[2],out[3],out[4],out[5],out[6],out[7],out[8],out[9],out[10],out[11],out[12]
                if str(is_valid) == 'True':
                    if updatedby:                
                        solution_error.append((_id, logfile, ts_added, ts_updated, threadname, errorstr, is_valid, addedby, updatedby, bugref, kbref, scomm, ecomm))
                        total_errors_removed_phase_7.append(1)                          
                    else:
                        exists_with_no_solution.append(eacherror)                                                           
                else:
                    invalid_error.append(eacherror)                                   
                    total_errors_removed_phase_7.append(1)                                              
            except Exception as e:
                does_not_exists.append(eacherror)
        if debug:    
            logger.info('Found ' + str(len(solution_error)) + ' error(s) with solution in database.')                
            logger.info('Found ' + str(len(exists_with_no_solution)) + ' error(s) with no solution in database.')        
            logger.info('Found ' + str(len(invalid_error)) + ' error(s) marked as invalid in database.')        
            logger.info('Found ' + str(len(does_not_exists)) + ' error(s) not in database. Adding them to database.')

        
        #Let's add these remaining errors into database now.        
        date = datetime.datetime.now()
        for line in does_not_exists: 
            try:    
                cursor_master_db.execute("INSERT OR IGNORE INTO ERRORLOG (LOGFILE, ADDED_TS, ERROR, ADDED_BY, THREAD, IS_VALID) VALUES ('{}','{}', '{}', '{}', '{}', '{}')".format(logfilename, date, line, 'System', threadname, True))
            except:
                try:
                    cursor_master_db.execute('INSERT OR IGNORE INTO ERRORLOG (LOGFILE, ADDED_TS, ERROR, ADDED_BY, THREAD, IS_VALID) VALUES ("{}","{}", "{}", "{}", "{}", "{}")'.format(logfilename, date, line, 'System', threadname, True))                
                except:
                    pass                
        master_db.commit()	
        master_db.close()          
        
        return False, solution_error, does_not_exists, exists_with_no_solution
####################################
#READING LIST COMPREHENSION - START
# Reading list comprehension can be really simple if this message is in place :)
# Let's pick the most difficult list comprehension : new_words = [word for line in newvalue for word in line.split() if word.upper() not in stopwords]
# In this one, first we are running 'for line in newvalue', which will give each line of a thread log, next we'll find each word in that line using 'for word in line.split()', which will give us each word that a line has, next
# we'll run 'If word.upper() not in stopwords' in which we are checking if a word doesn't belong to stopwords list, if it does not, then we are outputting the value as 'word' which will be added to list new_words
#READING LIST COMPREHENSION - DONE
####################################
def get_thread_info(threadName, threadCount, thread_dict, all_thread_dict, debug): #Takes input as threadname and threaderrrocount
#Reviewing Threads to see what sort of errors we found in them
#Here, we are first going through the appthread using each line (el) where el contains both, name of the thread & error count. Then, we are assigning the tn and tc which are thread name and thread error count respectively
#Next, we are finding the thread start date and thread end date using regex. Once, we have that, we'll simply find its difference which will become our thread age
#Next, we are finding the thread activity. This is really simple, we are finding the first line of the thread and finding what this thread is all about
#Next, we are finding the most common words in each of these thread stacks using the counter function. in counter function, we are using list comprehension where we are first doing word in thread_dict[tn] which will give us the whole list of logs for a specific thread and then we are going to split those logs word by word and then we are feeding x which is each word into counter parameter to show us most common words.
#Once, we have the most common words, we'll simply tag each of the log files with what they are most suitable with. Then we'll create a list in which we'll start appending the number of occurence of a particular component and then whichever component has got the max amount of hits will become OWNER of the thread.

    tn, tc = threadName, threadCount
    newvalue, deleted_words_log_lines_list = [], []
    
    for l in thread_dict[tn]: #Here we are simply reading each line of the logfile and stripping the first half of the log file that contains redundant information such as datetime, status and threadname to avoid showing that info in the counter object
        l = l.split(re.search(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [A-Z]+\s+\[([^\]]+)\]", l).group(1) + ']-')[1] #This regex will simply be used to split the logline and we'll get the 2nd half of it
        #newvalue.append(l[24:]) #removing 24 characters that are actually nothing but datetimestamp        
        newvalue.append(l)
    #Uptill here, we have the error log files associated for a failed thread and now we simply have to remove some common english words like 'to, from, it' etc. from it as they'll be repeated again and again
    deleted_words_log_lines_list = [word for line in newvalue for word in line.split() if word.upper() not in stopwords and 'MANAGER' not in word.upper() and 'DAO' not in word.upper() and 'XML' not in word.upper() and 'DIRECTORY' not in word.upper() and 'JAX' not in word.upper() and 'OVFDEPLOYER' not in word.upper() ]
    #print deleted_words_log_lines_list  
    
    #Finding the thread age. We'll find this by finding the first timestamp and then the last timestampe and subtracting them.
    thread_start = re.search(r"(^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})", all_thread_dict[tn][0]).group(1).strip()
    thread_end = re.search(r"(^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})", all_thread_dict[tn][-1]).group(1).strip()       
    #print 'Thread Started : ' + str(thread_start) 
    #print 'Thread Ended : ' + str(thread_end)  
    
    datetimeFormat = '%Y-%m-%d %H:%M:%S,%f'
    diff = datetime.datetime.strptime(thread_end, datetimeFormat)\
        - datetime.datetime.strptime(thread_start, datetimeFormat)
    th_days = diff.days
    th_minutes = diff.seconds / 60
    th_hours = th_minutes / 60    
    th_seconds = diff.seconds
    th_days, th_hours, th_minutes, th_seconds = int(th_days), int(th_hours), int(th_minutes), int(th_seconds)
    #print th_days, th_hours, th_minutes, th_seconds   
    if th_days > 0:
        thread_age = '~ ' + str(th_days) + ' day(s)'    
    elif th_days < 0:
        thread_age = 'Date/Time mis-match'                
    else:        
        if th_hours > 0:
            thread_age = '~ ' + str(th_hours) + ' hour(s)'
        else:        
            if th_minutes > 0:             
                thread_age = '~ ' + str(th_minutes) + ' minutes(s)'
            else:            
                if th_seconds > 0:             
                    thread_age = '~ ' + str(th_seconds) + ' second(s)'
                else:
                    thread_age = 'Date/Time mis-match'
            #print 'Thread Age : Time changed in-between thread execution, therefore, no thread age detected'        
    
    #Thread Activity Finder
    first_line_of_thread_start = all_thread_dict[tn][0].strip()
    #Previous Regex : ^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [A-Z]+\s+\[[^\]]+\]-[a-zA-Z]+.[a-zA-Z]+: (.+)    
    if debug:
        thread_activity = re.search(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [\S]+\s+\[[^\]]+\]-[\S]+.[\S]+: (.+)", first_line_of_thread_start).group(1) 
    else:
        try: 
            thread_activity = re.search(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [\S]+\s+\[[^\]]+\]-[\S]+.[\S]+: (.+)", first_line_of_thread_start).group(1) 
        except:
            first_line_of_thread_start = all_thread_dict[tn][1].strip()            
            thread_activity = re.search(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [\S]+\s+\[[^\]]+\]-[\S]+.[\S]+: (.+)", first_line_of_thread_start).group(1)             
    #print 'Thread Activity : ' + thread_activity        
    #Next, we'll feed this deleted_words_log_lines_list to the counter obj to get the list of most common words from it
    
    #for l in new_words:
    #    print l.strip()    
    #for l in newvalue:
    #    print l.strip()    
    keywords = Counter(word for line in deleted_words_log_lines_list for word in line.split()) #This will get us the most common words    
    #keywords = Counter(x for word in thread_dict[tn] for x in word.split())
    #print keywords                
    
    #for elit in thread_dict[tn]: #Displays the list of lines for a particular thread (ONLY ERRORS)
    #    print elit.strip()
    
    #Once, we get the keywords list, we'll simply go through each keyword and try to identify to which component it belongs to.
    #Keyword is a dict item, therefore, we'll go through it and then check each keyword, once, it finds the relevant keyword, we'll simply add their original count to it
    #category_found = False 
    vclist, esxlist, avlist, ddlist, dpalist, dpclist, dpslist, ldaplist, ddcdrlist = [], [], [], [], [], [], [], [], []
    for k,v in keywords.iteritems():
        ek = k  #name of the keyword (ek == each keyword)
        cn = v  #occurence of a keywords         (cn == count)
        if 'AVAMAR' in ek.upper():
            avlist.append(1)
            for x in range(cn):
                avlist.append(x)                
            category_found = True
       
        if 'DPA' in ek.upper() and 'VCEDPA' not in ek.upper() and 'IDPA' not in ek.upper():
            dpalist.append(1)        
            category_found = True
            for x in range(cn):
                dpalist.append(x)                 
    
        if 'DPC' in ek.upper():
            dpclist.append(1)        
            category_found = True
            for x in range(cn):
                dpclist.append(x)                 
    
        if 'DPS' in ek.upper():
            dpslist.append(1)        
            category_found = True
            for x in range(cn):
                dpslist.append(x)                 
    
        if 'DATADOMAIN' in ek.upper() or 'DDVE' in ek.upper() or 'DATA' in ek.upper() or 'DOMAIN' in ek.upper() or 'DDADAPTER' in ek.upper():
            ddlist.append(1)        
            category_found = True
            for x in range(cn):
                ddlist.append(x)                 
    
        if 'VCENTER' in ek.upper() or 'VCSA' in ek.upper() or 'VIJAVA' in ek.upper():
            category_found = True  
            vclist.append(1)
            for x in range(cn):
                vclist.append(x)                  
    
        if 'ESX' in ek.upper():
            category_found = True        
            esxlist.append(1)     
            for x in range(cn):
                esxlist.append(x)                 
    
        if 'LDAP' in ek.upper():
            ldaplist.append(1)        
            category_found = True  
            for x in range(cn):
                ldaplist.append(x)                 
       
        if 'DDCDR' in ek.upper() or 'CDRA' in ek.upper():
            ddcdrlist.append(1)        
            category_found = True 
            for x in range(cn):
                ddcdrlist.append(x)                 
    
    
    #Next, we'll simply create a finallist list which contains all of the above components hit and then find which one go the highest hits and then accordingly mark that thread as relevant to that product        
    finallist = [vclist, esxlist, avlist, ddlist, dpalist, dpclist, dpslist, ldaplist, ddcdrlist]  
    #print finallist 
    
    mosthits = finallist.index(max(finallist, key=len)) #Mosthits contains the index which has the highest match in finalist
    #print len(finallist[mosthits]) 
    
    #we are simply finding who has got the highest hits and then ensuring that length is high. If not, we'll mark it as other thread.
    if mosthits == 0 and len(finallist[mosthits]) > 0:
            thread_owner = 'vCenter'        
            #print 'Thread owner : vCenter'  
    elif mosthits == 1 and len(finallist[mosthits]) > 0:             
            thread_owner = 'ESX'        
            #print 'Thread owner : ESX'      
    elif mosthits == 2 and len(finallist[mosthits]) > 0:             
            thread_owner = 'Avamar'        
            #print 'Thread owner : Avamar'     
    elif mosthits == 3 and len(finallist[mosthits]) > 0:             
            thread_owner = 'Data Domain'        
            #print 'Thread owner : Data Domain'     
    elif mosthits == 4 and len(finallist[mosthits]) > 0:             
            thread_owner = 'DPA'        
            #print 'Thread owner : DPA'       
    elif mosthits == 5 and len(finallist[mosthits]) > 0:             
            thread_owner = 'DPC'        
            #print 'Thread owner : DPC'       
    elif mosthits == 6 and len(finallist[mosthits]) > 0:             
            thread_owner = 'DPS'        
            #print 'Thread owner : DPS'       
    elif mosthits == 7 and len(finallist[mosthits]) > 0:             
            thread_owner = 'LDAP'        
            #print 'Thread owner : LDAP'      
    elif mosthits == 8 and len(finallist[mosthits]) > 0:             
            thread_owner = 'CDRA'        
            #print 'Thread owner : CDRA'                  
    else: 
        thread_owner = 'Others'         
        #print '==> thread belongs to OTHERS'          
    #print '========================='        
    thread_name, thread_error_count, thread_stack, thread_start, thread_end, thread_age, thread_activity, thread_owner = str(tn), str(tc), thread_dict[tn], str(thread_start), str(thread_end), str(thread_age), str(thread_activity), thread_owner
    newvalue, deleted_words_log_lines_list = [], []
    return  thread_name, thread_error_count, thread_stack, thread_start, thread_end, thread_age, thread_activity, thread_owner  

def main_analyzer(logfilename, debug):
    ###############
    #INITIALIZERS
    ##############
    #Errors from logs are removed in 7 phases
    #phase1 : All error logs which have 'invalid credentials word in them are eliminated' and also, things in invalid_chars list are also eliminated
    #phase2 : All UI threads are eliminated and also all errors where 'Invalid credentials' is printed 
    #phase3 : All threads with name matching a certain name will be eliminated (like localhost-startStop-1 thread)
    #phase4 : All threads that have error but actually succeeded will be eliminated (word success in their last line)
    #phase5 : Redundant lines in a thread are eliminated (for instance a same line : 
    #phase6 : All errors which exists in logical dict are eliminated
    #phase7 : All errors which exists in physical database are eliminated
    #final_phase : In this one, all thread log lines are checked and common lines among different threads are then eliminated
    
    f = open(logfilename,'r')
    content = f.readlines()
    f.close()    
    
    if len(content) < 100: #if content lenght is less than 100 lines, we are not interested in it.
        return False, None,None,None,None,None,None,None,None,None,None     
    
    total_errors_removed_phase_1, total_errors_removed_phase_2, total_errors_removed_phase_3, total_errors_removed_phase_4, total_errors_removed_phase_5, total_errors_removed_phase_6, total_errors_removed_phase_7, final_phase = [], [], [], [], [], [], [], []
    total_errors_count, total_lines, total_threads, total_ui_threads, total_app_threads, all_err_lines = [], [], [], [], [], []
    
    master_db = sqlite3.connect('masterdb.db')
    master_db.isolation_level = None	 
    master_db.execute('PRAGMA foreign_keys = ON;')		
    master_db.text_factory = str	  
    master_db.execute('CREATE TABLE IF NOT EXISTS ERRORLOG ("ID" INTEGER, "LOGFILE" TEXT NOT NULL, "ADDED_TS" TEXT NOT NULL, "UPDATED_TS" TEXT, "THREAD" TEXT NOT NULL, "ERROR" TEXT NOT NULL UNIQUE, "IS_VALID" BOOL, "ADDED_BY" TEXT NOT NULL, "UPDATED_BY" TEXT, "BUG_REF" TEXT, "KB_REF" TEXT, "SUPPORT_COMMENT" TEXT , "ENGG_COMMENT" TEXT , PRIMARY KEY("ID" AUTOINCREMENT))')
    master_db.close() #####
    if debug:        
        logger.info('================================')
        logger.info(' LOG INFORMATION ')
        logger.info('================================')
    
    st = time.time()
    #if 'pool' not in word and 'thread' not in word and 'ping' not in word and ':' not in word and '=' not in word and 'from' not in word and 'Successfully' not in word and 'file' not in word and 'for' not in word:
    
    thread_dict = {}
    all_thread_dict = {}

    st_format, et_format = False, False
    localtime_rx = r"(^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})"
    myr = r'(^\d+\s\S+\s\d+\s\d+:\d+:\d+.\d+)' #other style of data format
    
    #Finding log start time and log end time and their difference or lifespan of a log
    for x in range(5000):
        try:
            log_start_time = re.search(localtime_rx, content[x]).group(1).strip()
            break         
        except:
            pass    
    
    try: #Most of the log files should have standard date format of  (2021-12-10 23:54:10,559) but some can have (02 Mar 2021 04:04:42.22), therefore, we need to have try and except block for them.
        log_start_time = log_start_time
    except:
        st_format = True
        for x in range(5000):
            try:
                log_start_time = re.search(myr, content[x]).group(1).strip()
                break         
            except:
                pass        
        
    for x in range(-1,-5000,-1):
        try:
            log_end_time = re.search(localtime_rx, content[x]).group(1).strip() 
            break         
        except:
            pass    
            
    try: #Most of the log files should have standard date format of  (2021-12-10 23:54:10,559) but some can have (02 Mar 2021 04:04:42.22), therefore, we need to have try and except block for them.
        log_end_time = log_end_time
    except:
        et_format = True
        for x in range(-1,-5000,-1):
            try:
                log_end_time = re.search(myr, content[x]).group(1).strip() 
                break         
            except:
                pass          



    if st_format and et_format: #both times are in 02 Mar 2021 format
        datetimeFormat = '%d %b %Y %H:%M:%S.%f'        
        diff = datetime.datetime.strptime(log_end_time, datetimeFormat)\
            - datetime.datetime.strptime(log_start_time, datetimeFormat)
        th_days = diff.days
        th_hours = diff.days * 24
        th_minutes = diff.seconds / 60
        th_seconds = diff.seconds 
        if th_days > 30:
            logage = str(th_days/30) + ' month(s)'    
        else:        
            if th_hours > 24:
                logage = '~ ' + str(th_days) + ' day(s)'
            else:        
                if th_minutes > 60:             
                    logage = '~ ' + str(th_hours) + ' hour(s)'
                else:            
                    if th_seconds > 60:             
                        logage = '~ ' + str(th_minutes) + ' minute(s)'
                    else:
                        if th_seconds < 60:             
                            logage = '~ ' + str(th_seconds) + ' second(s)'            
                        else:                    
                            if int(diff.days) >= 0:
                                logage = '~ ' + str(th_days)
                                #print 'Thread Age : ' + str(thread_age)
                            else:
                                logage = 'Date/Time mis-match'          
    elif st_format == False and et_format == False: #both times are in 2021-12-10 format
        datetimeFormat = '%Y-%m-%d %H:%M:%S,%f'        
        diff = datetime.datetime.strptime(log_end_time, datetimeFormat)\
            - datetime.datetime.strptime(log_start_time, datetimeFormat)
        th_days = diff.days
        th_hours = diff.days * 24
        th_minutes = diff.seconds / 60
        th_seconds = diff.seconds 
        if th_days > 30:
            logage = str(th_days/30) + ' month(s)'    
        else:        
            if th_hours > 24:
                logage = '~ ' + str(th_days) + ' day(s)'
            else:        
                if th_minutes > 60:             
                    logage = '~ ' + str(th_hours) + ' hour(s)'
                else:            
                    if th_seconds > 60:             
                        logage = '~ ' + str(th_minutes) + ' minute(s)'
                    else:
                        if th_seconds < 60:             
                            logage = '~ ' + str(th_seconds) + ' second(s)'            
                        else:                    
                            if int(diff.days) >= 0:
                                logage = '~ ' + str(th_days)
                                #print 'Thread Age : ' + str(thread_age)
                            else:
                                logage = 'Date/Time mis-match'              
    else: #Timezone changed between the logfile, we'll only have the log start time and end time no logage.
        logage = 'Undefined due to Time format change'    
    
    
    for line in content:
        line = line.strip()
        total_lines.append(1)
        try:
            #This will give us the status of a thread, once we have that, we'll find associated thread-name for that and then grab all matching lines of that thread.
            thread_status = re.search(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} ([A-Z]+)", line).group(1).strip() #This regex will get us the status of the a log line
            if thread_status == 'ERROR': 
                total_errors_count.append(1)          
                if 'Invalid credentials' not in line and 'invalid credentials' not in line:
                    try:        
                        #This will get us just the thread name which has Error in it
                        thread_name = re.search(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [A-Z]+\s+\[([^\]]+)\]", line).group(1)  #This regex will get us the thread name of a log line (not with square brackets)
                        if thread_name not in invalid_chars:
                            if 'Timer' not in thread_name:                
                                if thread_name in thread_dict:
                                    thread_dict[thread_name].append(line)
                                else:            
                                    thread_dict[thread_name] = [line]                
                            else:                                    
                                total_errors_removed_phase_1.append(1) #removing errors from thread which match invalid_chars(threadnames) list                                                
                        else:
                            total_errors_removed_phase_1.append(1) #removing errors from thread which match invalid_chars(threadnames) list                    
                    except:
                        pass
                else:
                    total_errors_removed_phase_1.append(1)            
        except:
            pass
        try:        
            #getting all threads and their associated entries as well        
            thread_name = re.search(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [A-Z]+\s+\[([^\]]+)\]", line).group(1)  
            if thread_name not in invalid_chars:                
                if thread_name in all_thread_dict:
                    all_thread_dict[thread_name].append(line)
                else:            
                    all_thread_dict[thread_name] = [line]            
        except:
            pass    
    
    newthreaddict = {}    #this will contains all threads with errors in them 
    #e = []
    for thread_name, thread_line in thread_dict.iteritems():
    #    for x in range(len(thread_line)):
    #        e.append(1)
    #   print 'Found thread name : ' + thread_name + ' with ' + str(len(thread_line)) + ' errors'
        newthreaddict[thread_name] = len(thread_line)
    
    #print 'Total errors : ' + str(len(e))
    #print 'Error count here : ' + str(len(total_errors_count))
    #print 'Errors removed in phase one : ' + str(len(total_errors_removed_phase_1))
    #rem = len(total_errors_count) - len(total_errors_removed_phase_1)
    #print 'Remainder : ' + str(rem)
    
    
    ui_threads = {}
    app_threads = {}
    #errsss =[]
    all_errors = []
    for threadname, threaderr in newthreaddict.iteritems():
        total_threads.append(1)
        for x in range(threaderr):          
            all_errors.append(1)
        if 'http' in threadname:
            total_ui_threads.append(1)    
            if threadname in ui_threads:
                ui_threads[threadname].append(threaderr)
            else:
                ui_threads[threadname] = threaderr        
            for x in range(threaderr): #adding errors from UI threads into phase_1            
                total_errors_removed_phase_2.append(1)            
        else:
            total_app_threads.append(1)    
            if threadname in app_threads:
                app_threads[threadname].append(threaderr)
            else:
                app_threads[threadname] = threaderr  
            #for x in range(threaderr): #adding errors from UI threads into phase_1            
            #    errsss.append(1)             
    
    #print 'Errors removed due to http threads : ' + str(len(total_errors_removed_phase_2))
    #print ('Errors left as per app threads : ' + str(len(errsss)))
    #print 'total thread errors : ' + str(len(total_errors_removed_phase_2) +len(errsss))
    #print 'Errors left : ' + str(rem - len(total_errors_removed_phase_2))
    #print 'All errros : ' + str(len(all_errors))
    #sys.exit()
    
    #Let's sort them according to the number of errors that they have
    ui_threads = sorted(ui_threads.items(), key=itemgetter(1))
    ui_threads=ui_threads[::-1]
    app_threads = sorted(app_threads.items(), key=itemgetter(1))
    app_threads=app_threads[::-1]
    
    #print 'OUR UI THREADS ARE :'
    #print ui_threads
    #
    #print 'OUR APP THREADS ARE :'
    #print app_threads
    
    total_errors_from_start, total_lines, total_threads, total_ui_threads, total_app_threads = len(total_errors_count), len(total_lines), len(total_threads), len(total_ui_threads), len(total_app_threads)
    if debug:        
        logger.info('Statistics :\nTotal Lines: ' + str(total_lines) + '\nTotal Errors: ' + str(total_errors_from_start) + '\nTotal threads: ' + str(total_threads) + '\nTotal UI Threads: ' + str(total_ui_threads) + '\nTotal App Threads: ' + str(total_app_threads) + '\nErrors Removed using UI Threads (Phase 1) : ' + str(len(total_errors_removed_phase_1)))
        logger.info('Took ' + str(time.time() - st) + ' second to read the file, find thread name per line, find thread status per line and then create two list of UI Threads and App Threads ')
    
    
    st = time.time()
    
    if debug:        
        logger.info('================================')
        logger.info(' THREAD INFORMATION ')
        logger.info('================================')
    
    
    ###PERFORMING THREAD ASSOCIATION FOR BETTER UNDERSTANDING OF LOGS#### 
    thread_association = {} 
    threadinfo = {}
    total_error_messge_no_db_hit_no_solution, total_error_messge_db_hit_no_solution, allthreads_list = [], [], []
    
    
    for el in app_threads: #lets associate threads togehter, for example all threads that have pool word in them should have all of there relevant threads together
        threadname, threaderrcount = el[0], el[1] #thread name and thread error count   
        if 'pool' in threadname:
            pool_name = re.search(r'^(\w+-\d+)', threadname).group(1)
            if pool_name in thread_association:
                thread_association[pool_name].append({threadname:threaderrcount})
            else:
                thread_association[pool_name] = [{threadname:threaderrcount}]
        else:
            pool_name = 'Non-Pool'    
            if pool_name in thread_association:
                thread_association[pool_name].append({threadname:threaderrcount})
            else:
                thread_association[pool_name] = [{threadname:threaderrcount}]
             
    for poolname, threadinfodict in thread_association.iteritems(): #This dict contains Poolnames and their associated thread
        #print '**************************************************************************************'    
        #print 'WORKING ON ' + str(poolname) + ' WITH ' + str(len(threadinfodict)) + ' THREAD(s) (' + str(threadinfodict) + ')'      
        #print '**************************************************************************************'
        total_threads, valid_threads, ignored_threads = [], [], []   
        for eachitem in threadinfodict: #eachitems contains threadname and threaderrcount
            for threadname, threaderrcount in eachitem.iteritems():
                #print 'Thread : ' + str(threadname) + ' has ' + str(threaderrcount) + ' errors'
                total_threads.append(threadname)
                if 'localhost-startStop-1' not in threadname :
                    thread_first_line, thread_last_line = all_thread_dict[threadname][0].strip(), all_thread_dict[threadname][-1].strip()        
                    if 'ccess' in thread_last_line or ('collect' in thread_last_line and 'logs' in thread_last_line): #Checking if thread last line says success, if yes, then simply ignore thread
                        ignored_threads.append(threadname)
                        for x in range(threaderrcount):
                            total_errors_removed_phase_4.append(1)
                    else:    
                        #Let's remove redundant lines from thread error logs to avoid calculation mistakes. for this we'll simply use set command.
                        #This regex will get everything after threadname
                        #threaderrorlog = list(set([re.search(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [A-Z]+\s+\[[^\]]+\](-[\S]+.[\S]+: .+)', line).group(1) for line in thread_dict[threadname]]))
                        threaderrorlog = []
                        for line in thread_dict[threadname]:
                            try:
                                thread_msg = re.search(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [A-Z]+\s+\[[^\]]+\](-[\S]+.[\S]+: .+)', line).group(1)
                                threaderrorlog.append(thread_msg)
                            except:
                                pass                            
                        threaderrorlog = list(set(threaderrorlog))
                        #print 'Previous error count was ' + str(len(thread_dict[threadname])) + ' and new error count is ' + str(len(threaderrorlog))
                        #print 'Diff : ' + str(len(thread_dict[threadname])-len(threaderrorlog))
                        #print 'FOUND ' + str(len(thread_dict[threadname])) + ' ORIGLINES AND ' + str(len(threaderrorlog)) + ' NEW LOGS WITH DIFF ' + str(len(thread_dict[threadname])-len(threaderrorlog))
                        for x in range(len(thread_dict[threadname])-len(threaderrorlog)):
                            total_errors_removed_phase_5.append(1)
                        ret, solution_error, does_not_exists, exists_with_no_solution = remove_invalid_error_threads(threadname, threaderrorlog, all_thread_dict[threadname], thread_dict[threadname], logfilename, total_errors_removed_phase_6, total_errors_removed_phase_7, debug)
                        if ret:
                            ignored_threads.append(threadname)                        
                        else: #Now, these are errors that we have no idea about , therefore let's try to work them out. At the same time, we have already saved them in our database.              
                            valid_threads.append(threadname)
                            thread_name, thread_error_count, thread_stack, thread_start, thread_end, thread_age, thread_activity, thread_owner = get_thread_info(threadname, threaderrcount, thread_dict, all_thread_dict, debug)
                            allthreads_list.append((thread_name, thread_error_count, thread_stack, thread_start, thread_end, thread_age, thread_activity, thread_owner, all_thread_dict[threadname],solution_error))
                            for x in thread_dict[threadname]: #Saving remaining threads
                                all_err_lines.append(x) 
                            if debug:
                                for x in thread_stack:
                                    print x                                
                            if len(exists_with_no_solution) > 0:
                                #logger.info('Displaying errors which exsits in db but have no solution')
                                for x in list(set(exists_with_no_solution)):
                                    #print x
                                    total_error_messge_db_hit_no_solution.append(x)
                            if len(does_not_exists) > 0:                                
                                #logger.info('Displaying errors which did not exsits in db but now have been added')
                                for x in list(set(does_not_exists)):
                                    #print x
                                    total_error_messge_no_db_hit_no_solution.append(x)
                            if debug:                                        
                                logger.info('Thread Statistics:\nThread Name: ' + str(thread_name) + '\nThread Start: ' + str(thread_start) + '\nThread End: ' + str(thread_end) + '\nThread Age: ' + str(thread_age) + '\nThread Error Count: ' + str(thread_error_count) + '\nThread Activity: ' + str(thread_activity) + '\nThread Owner: ' + str(thread_owner))            
                                print '--------------------------------------------'       
                                print '---END OF THREAD---'
                                print '--------------------------------------------'                       
                else:
                    for x in range(threaderrcount):            
                        total_errors_removed_phase_3.append(1)              
                    ignored_threads.append(1) 
        #logger.info('Pool ' + poolname + ' summary : Found Total ' + str(len(total_threads)) + ' thread(s) with ' + str(len(valid_threads)) + ' thread(s) valid (' + str(valid_threads) + ') and ' + str(len(ignored_threads)) + ' thread(s) worth ignoring (' + str(ignored_threads) + ')')
        #if len(ignored_threads) == len(threadinfodict):
        #    logger.info('All threads for ' + poolname + ' were ignored')
        #else:
        #    logger.info('Some threads were processed for ' + poolname)    

    ##FINAL LOG SUMMARY##
    rem_err_log = list(set(total_error_messge_no_db_hit_no_solution)) + list(set(total_error_messge_db_hit_no_solution))            
    total_errors_removed_or_eliminated_in_phases = len(total_errors_removed_phase_1) + len(total_errors_removed_phase_2) + len(total_errors_removed_phase_3) + len(total_errors_removed_phase_4) + len(total_errors_removed_phase_5) + len(total_errors_removed_phase_6) + len(total_errors_removed_phase_7)

    total_errors_removed_so_far = len(total_errors_count) - total_errors_removed_or_eliminated_in_phases
    further_removed = total_errors_removed_so_far - len(rem_err_log) 
    total_errors_removed_or_eliminated = total_errors_removed_or_eliminated_in_phases + further_removed
    total_errors_left = len(total_errors_count) - total_errors_removed_or_eliminated

    #total_errors_left = total_errors_left - (total_errors_left - len(rem_err_log))#Final errors removed after removing redundant lines from logs based on threads (if two threads have same line)
    try:    
        percentage_reduction = total_errors_removed_or_eliminated * 100 / len(total_errors_count)
    except:
        percentage_reduction = 0    

    logger.info('LOG SUMMARY:\nTotal Erors Found : ' + str(len(total_errors_count)) +  '\nTotal Errors Removed (Overall Phase 1 to 7 + Final) : ' + str(total_errors_removed_or_eliminated) + '\nTotal Errors Left : ' + str(total_errors_left)  + '\nReduction : ' + str(percentage_reduction) + '%\n----------------------\nTotal Errors Removed (Phase_1_InvalidC) : ' + str(len(total_errors_removed_phase_1))  +'\nTotal Errors Removed (Phase_2_UIThreads) : ' + str(len(total_errors_removed_phase_2)) + '\nTotal Errors Removed (Phase_3_CertainName) : ' + str(len(total_errors_removed_phase_3))  + '\nTotal Errors Removed (Phase_4_SuccessMsg) : ' + str(len(total_errors_removed_phase_4))  + '\nTotal Errors Removed (Phase_5_Redundant) : ' + str(len(total_errors_removed_phase_5))  + '\nTotal Errors Removed (Phase_6_Logical) : ' + str(len(total_errors_removed_phase_6)) + '\nTotal Errors Removed (Phase_7_physical) : ' + str(len(total_errors_removed_phase_7)) + '\nTotal Errors Removed (Final Phase (Thread_Redudant)) : ' + str(further_removed) + '\n----------------------') 
    
    #PRINTING ERROR MESSAGES
    #if len(total_error_messge_db_hit_no_solution) > 0:    
    #    logger.info('Errors that were found in database but with no solution')
    #    for i, x in enumerate(list(set(total_error_messge_db_hit_no_solution))):
    #        print str(i+1) + ') ' + str(x)
    #if len(total_error_messge_no_db_hit_no_solution) > 0:    
    #    logger.info('Totally unknown errors')
    #    for i, x in enumerate(list(set(total_error_messge_no_db_hit_no_solution))):
    #        print str(i+1) + ') ' + str(x)

   
    fullerrorstack = []
    fullerrorstack = [normalLine for normalLine in all_err_lines if not all(errLine not in normalLine for errLine in rem_err_log)] 
    #print '-------------------'  
    #for e in fullerrorstack:
    #    print e
    #print '-------------------'    
    #for e in rem_err_log:
    #    print e 
    #    for k in all_err_lines:
    #        if e in k:
    #            fullerrorstack.append(k)
    #print len(fullerrorstack)            
    #print '-------------------'            
    
    if debug:    
        for e in rem_err_log:
            print e        
        logger.info('Printing full error lines of remaining errors')    
        #for e in list(set(fullerrorstack)):
        #    print e            
    
    logger.info('Took ' + str(time.time() - st) + ' seconds to find Thread information')
    return True, rem_err_log, fullerrorstack, str(len(total_errors_count)), str(total_errors_removed_or_eliminated), str(total_errors_left), str(percentage_reduction)+'%', allthreads_list,  log_start_time, log_end_time, logage     
    
#if __name__ == '__main__':
#    logfilename = '2021_01_04_02_09_28240232078.log'
#    main_analyzer(logfilename)
