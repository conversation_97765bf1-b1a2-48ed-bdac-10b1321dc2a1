3.5.4 (Bleeding Edge)
a4b51aee4b4fb3cff9c3b77601592c62d772130a
o:Sass::Tree::RootNode:@children[o:Sass::Tree::CommentNode:@value[I"/* Sizing shortcuts */:ET:
@type:silent;[ :@filename0:
@options{ :
@linei:@source_rangeo:Sass::Source::Range	:@start_poso:Sass::Source::Position;i:@offseti:
@end_poso;;i;i:
@fileI" bootstrap/mixins/_size.scss;	T:@importero: Sass::Importers::Filesystem:
@rootI"G/var/www/html/products/html/admintemplates/sufee-admin/assets/scss;	T:@real_rootI"G/var/www/html/products/html/admintemplates/sufee-admin/assets/scss;	T:@same_name_warningso:Set:
@hash} Fo:Sass::Tree::MixinDefNode:
@nameI"	size;	T:
@args[[o:!Sass::Script::Tree::Variable;I"
width;	T:@underscored_nameI"
width;	T;i;o;	;o;;i;i;o;;i;i;@;@;@;
@0[o; ;I"height;	T;!I"height;	T;i;o;	;o;;i;i;o;;i;i!;@;@;@;
@o; ;I"
width;	T;!I"
width;	T;i;o;	;o;;i;i#;o;;i;i);@;@;@;
@:@splat0;[o:Sass::Tree::PropNode;[I"
width;	T;[o; ;I"
width;	T;!I"
width;	T;i	;o;	;o;;i	;i;o;;i	;i;@;@;@;
@:
@tabsi :@prop_syntax:new;[ ;0;
@;i	;o;	;o;;i	;i;o;;i	;i;@;@:@name_source_rangeo;	;@9;o;;i	;i
;@;@:@value_source_rangeo;	;o;;i	;i;@:;@;@o;#;[I"height;	T;[o; ;I"height;	T;!I"height;	T;i
;o;	;o;;i
;i;o;;i
;i;@;@;@;
@;$i ;%;&;[ ;0;
@;i
;o;	;o;;i
;i;o;;i
;i;@;@;'o;	;@K;o;;i
;i;@;@;(o;	;o;;i
;i;@L;@;@;0;
@;i;o;	;o;;i;i;o;;i;i+;@;@:@has_childrenT;0;
@:@templateI"g// Sizing shortcuts

@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}
;	T;i;o;	;o;;i;i;o;;i;i;@;@;)T