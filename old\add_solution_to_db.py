import sqlite3, os, sys, datetime

master_error_dict = {'Unable to set timezone on vCenter server':'Failed to set timezone on vCenter Server,Follow KB525866 to resolve this issue','ddadapter.DataDomainPlugin: Failed to execute: elicense update':'Failed to Configure the DDVE License,This could be due to incorrect License File supplied or DDVE License files has issues such as "#" in the address parameter or maybe incorrect storage','Failed to apply DPA license':'Failed to Configure the DPA License,This could be due to incorrect License File supplied or DPA License files has issues such as "#" in the address parameter or extra space or maybe incorrect License Type','Exception org.elasticsearch.transport.BindTransportException: Failed to resolve publish address':'Failure in ElasticSearch Stack for the Publish Address,One of the DPSearch Services such as ES was not able to start. Workaround is to start the DPS Configuration from ACM Dashboard and once DPS Master VM is deployed we need to edit the elasticsearch.yml located in /etc/elasticsearch of this VM. The editing of the file had to be done as soon as the elasticsearch.yml file was created and before the system loaded the file.You need to replace this line : "network.publish_host: _site_" With : "network.publish_host: _local_" We have about 45 seconds from the time that the file is written to the master node to when the system read this file. Clearquest ID CQSDR0006253 opened against DP Search. KB 525083','com.emc.vcedpa.common.exception.ApplianceException: Data Domain data drive configuration failed':'Failed to configure data drive configuration on DDVE,This could be due to bad cleanup of the DDVE do the cleanup again for DDVE or Faulty NVMe Issue or Datastores not listed properly or see Escalation 33581','Proxy Deployment Failed Unable to find task info from vCenter for AVProxy':'Failed to deploy Avamar Proxy,This could be due to network issues therefore recommended to try manual deploy of proxy as a test using MCGUI before retrying the workflow.','Exception while deploying DPA Application':'Failed to deploy DPA,In one of such issues there was a connectivity issue between the vmnic and the switch you can check that using the ping and if you see high latency in time parameter.Another reason can be a password issue where password has special character such as hypen in it.','Failed to get MCLogin Info since there are no EMDB properties loaded':'Failed to configure AVE,This could be due to the company name having space in it','Search failed to update new LDAP':'Failed to update LDAP information on DPSearch,This could be due to payload issues with ACM API call to DPsearch. Try using the command from ACM and DPSearch and compare "ldapsearch -h <<LDAP server hostname or IP>> -p 389 -D <<Query Username>> -b <<Base Domain Name>> -w Idpa_1234"','ERROR: sm_cloudunit_create: cloud unit':'Failed to configure Cloud Tier,This could be due to a timeout issue with AWS. See KB 525492','Unable to connect to rabbit MQ service on localhost':'Failed to get ESXi Details,See Escalation 33273','OVF deployment failed with Exception':'Failed to deploy the inital OVF,This is most likely a DNS issue. Check the network again. See Escalation 33242','Error message Failed to setup Secure OpenLDAP on ACM.':'Failed to configure OpenLDAP,This could be a transient error message and retrying the workflow should automatically fix it. Some commands might have failed.','Failed to add NIC on Virtual Machine':'Failed to add NIC to DDVE VM,This could be due to improper cleanup performed. Make sure you disassociate ESXi from vCenter using actions menu.','Error occurred while creating virtual app DataProtection':'Failed to create the vAPP for IDPA,This could be due to improper cleanup. Login to VCSA and delete the stale vAPPs and then try the workflow again','Incorrect Locking Id value in Data Domain license file':'Incorrect Locking Id value in Data Domain license file,Please check the DDVE License file again','Failed to update LDAP on Search Master':'Failed to update LDAP on Search Master,AD Details are case-sensitive. Please use the in-case sensitive details','Failed to enable cloud':'Workflow Failed to run "enable cloud" command on DDVE,This could be caused due to issues with CDRA Config on DD therefore you ignore CDRA configuration at the beginning of the workflow and can later seperately install it','Failed to add interface':'DDVE experienced a failure in adding the interface ip to group, Unfortunately this is a non-reproducible issue by Engineering Team and most likely could be due to Temporary problem with the IP address assignment therefore recommendation is to re-run the workflow and you should not see this issue or there might be an issue with the IP assigned to DD therefore review the alerts show command on DDVE.','/usr/local/dataprotection/var/configmgr/server_data/config/dpcconfig.xmljava.io.FileNotFoundException':'DPC Deployment Failed due to missing file,If you performed a cleanup maybe you have deleted the dpcconfig.xml file kindly check that','ConfigAVEServerTask: Exception occurred while executing Avamar config server task' : 'Avamar Configuration received an exception,As of now there is no known cause of this error and If the workflow continues you can ignore this problem.','Exception occurred while executing deploy Avamar vApp task':'There was an exception while configuring the vApp for Avamar and therefore the process failed, This could be due to an improper cleanup of vCenter and Avamar vApp still present in the vCenter before the workflow was retried. Make sure to delete the Avamar vApp from the vCenter before retrying the operation.','rate limit response from server':'An exception has occurred when the workflow was trying to set the NTP on vCenter,This could be caused due to NTP Server not responding in timely manner. Review KB 528543 for more info.','Failed to execute: net show hardware':'Workflow failed to run a command on DDVE,This could be caused due to workflow unable to reach to DDVE IP address or there might be some conflict in the IP Address.Review the alerts show command for more information on this.','Failed to configure ports':'Workflow was unable to configure the networking on DDVE,This could be caused due to IP Conflict in the environment.You can try to manually assign the IP to DD Intergace to confirm or you can review alerts on DDVE via alerts show command and see the error message.','Failed to deploy the Proxy':'Failed to deploy Avamar Proxy,This could be due to network or timing issues therefore recommended to try manual deploy of proxy as a test using MCGUI before retrying the workflow. Also check the logs for exact problem if it says that the vCenter could not find the proxy then it is a timing issue.'}

master_db = sqlite3.connect('masterdb.db')
cursor_master = master_db.cursor()
print len(master_error_dict)
date = datetime.datetime.now()
for key, value in master_error_dict.iteritems():
    cursor_master.execute('select ERROR from ERRORLOG where ERROR like "%'+str(key)+'%"')
    d = cursor_master.fetchone()    
    #try:
    #    k = d[0]
    #    found = True
    #except:
    #    found = False
    #    pass    
    #if found:  #Add them to DB
    #    if str(d[0]) is not None:  
    #        cursor_master.execute("UPDATE ERRORLOG SET UPDATED_TS=?, UPDATED_BY='Pankaj Pande', SUPPORT_COMMENT=? WHERE ERROR=?", (str(date), str(value), str(k)))
    try:    
        if str(d[0]) is None:
            print 'Need to add'    
    except:
        print key    
        cursor_master.execute("INSERT INTO ERRORLOG (LOGFILE, ADDED_TS, ERROR, ADDED_BY, THREAD, IS_VALID, UPDATED_TS, UPDATED_BY, SUPPORT_COMMENT) VALUES ('{}','{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}')".format('Hand-Typed', date, key, 'Pankaj Pande', 'Not Required', True, date, 'Pankaj Pande', value))    

master_db.commit()
master_db.close()