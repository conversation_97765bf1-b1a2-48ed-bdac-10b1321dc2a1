

list_1 = ['the world', 'abc', 'bcd', 'want a car', 'hell', 'you rock']
list_2 = ['the world is big', 'i want a car', 'puppies are best', 'you rock the world']
dict_1 = {'a car':'i want a car', 'champ':'i am champ', 'you know':'you rock the world'}
out = [key for key, value in dict_1.items() if key in list_1 and value in list_2]

out = [ele_1 for key, value in dict_1.iteritems() for ele_1 in list_1 if key in ele_1 for elem_2 in list_2 if value in elem_2]
print list(set(out))
print out