"""
AI Integration Example for IDPA Log Analyzer
This module shows how to integrate AI capabilities into the existing application
"""

import os
from flask import Flask, render_template, request, jsonify
import mainanalyzer as anlz
from ai_error_analyzer import AIErrorAnalyzer, enhance_thread_analysis
from ai_solution_generator import AI<PERSON>olutionGenerator
from ai_log_summarizer import AILogSummarizer
import time
import logging

# Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', 'your-api-key-here')
AI_MODEL = os.getenv('AI_MODEL', 'gpt-3.5-turbo')

# Initialize AI modules
ai_error_analyzer = AIErrorAnalyzer(OPENAI_API_KEY, AI_MODEL)
ai_solution_generator = AISolutionGenerator(OPENAI_API_KEY, AI_MODEL)
ai_log_summarizer = AILogSummarizer(OPENAI_API_KEY, AI_MODEL)

logger = logging.getLogger(__name__)

def enhanced_log_analysis(logfilename: str, enable_ai: bool = True) -> dict:
    """
    Enhanced log analysis with AI capabilities
    
    Args:
        logfilename: Path to log file
        enable_ai: Whether to enable AI enhancements
        
    Returns:
        Enhanced analysis results with AI insights
    """
    # Run original analysis
    ret_code, main_errors_stack, fullerrorstack, total_errors_count, \
    total_errors_removed_or_eliminated, total_errors_left, percentage_reduction, \
    allthreads_list, log_start_time, log_end_time, logage = anlz.main_analyzer(logfilename, False)
    
    if not ret_code:
        return {
            'success': False,
            'error': 'Log analysis failed',
            'original_results': None,
            'ai_enhancements': None
        }
    
    # Prepare original results
    original_results = {
        'main_errors_stack': main_errors_stack,
        'fullerrorstack': fullerrorstack,
        'total_errors_count': total_errors_count,
        'total_errors_removed_or_eliminated': total_errors_removed_or_eliminated,
        'total_errors_left': total_errors_left,
        'percentage_reduction': percentage_reduction,
        'allthreads_list': allthreads_list,
        'log_start_time': log_start_time,
        'log_end_time': log_end_time,
        'logage': logage
    }
    
    ai_enhancements = {}
    
    if enable_ai and OPENAI_API_KEY != 'your-api-key-here':
        try:
            # 1. Enhanced Thread Analysis with AI
            logger.info("Generating AI-enhanced thread analysis...")
            enhanced_threads = []
            for thread_data in allthreads_list[:5]:  # Analyze top 5 threads to manage API costs
                enhanced_thread = enhance_thread_analysis(thread_data, ai_error_analyzer)
                enhanced_threads.append(enhanced_thread)
            
            ai_enhancements['enhanced_threads'] = enhanced_threads
            
            # 2. AI-Generated Solutions for Unknown Errors
            logger.info("Generating AI solutions for unknown errors...")
            unknown_errors = main_errors_stack[:10]  # Top 10 unknown errors
            ai_solutions = []
            
            for error in unknown_errors:
                try:
                    solution = ai_solution_generator.generate_solution(
                        error_message=error,
                        error_context={
                            'component': 'Unknown',
                            'thread_name': 'Multiple',
                            'timestamp': log_start_time,
                            'severity': 'Medium'
                        }
                    )
                    ai_solutions.append({
                        'error': error,
                        'solution': solution
                    })
                except Exception as e:
                    logger.error(f"Solution generation failed for error: {error[:50]}... Error: {str(e)}")
            
            ai_enhancements['ai_solutions'] = ai_solutions
            
            # 3. Executive Summary
            logger.info("Generating executive summary...")
            executive_summary = ai_log_summarizer.create_executive_summary(original_results)
            ai_enhancements['executive_summary'] = executive_summary
            
            # 4. Executive Dashboard
            logger.info("Creating executive dashboard...")
            executive_dashboard = ai_log_summarizer.create_executive_dashboard(original_results)
            ai_enhancements['executive_dashboard'] = executive_dashboard
            
            # 5. Pattern Analysis
            logger.info("Analyzing error patterns...")
            if main_errors_stack:
                pattern_analysis = ai_error_analyzer.analyze_error_patterns(main_errors_stack)
                ai_enhancements['pattern_analysis'] = pattern_analysis
            
        except Exception as e:
            logger.error(f"AI enhancement failed: {str(e)}")
            ai_enhancements['error'] = f"AI analysis failed: {str(e)}"
    
    return {
        'success': True,
        'original_results': original_results,
        'ai_enhancements': ai_enhancements
    }

# Flask route example for AI-enhanced analysis
def create_ai_enhanced_route(app: Flask):
    """Add AI-enhanced routes to the Flask app"""
    
    @app.route('/ai_analyze', methods=['POST'])
    def ai_enhanced_analysis():
        """AI-enhanced log analysis endpoint"""
        try:
            # Get uploaded file or file path
            if 'file' in request.files:
                file = request.files['file']
                filename = f"temp_{int(time.time())}_{file.filename}"
                filepath = os.path.join('Uploads', filename)
                file.save(filepath)
            else:
                filepath = request.form.get('filepath')
                filename = os.path.basename(filepath)
            
            if not filepath or not os.path.exists(filepath):
                return jsonify({'error': 'File not found'}), 400
            
            # Run enhanced analysis
            results = enhanced_log_analysis(filepath, enable_ai=True)
            
            if not results['success']:
                return jsonify({'error': results['error']}), 500
            
            # Render enhanced template
            return render_template('ai_analyzed.html', 
                                 results=results,
                                 filename=filename)
            
        except Exception as e:
            logger.error(f"AI analysis route failed: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/ai_chat', methods=['POST'])
    def ai_chat():
        """AI chat interface for log analysis questions"""
        try:
            user_question = request.json.get('question', '')
            analysis_context = request.json.get('context', {})
            
            if not user_question:
                return jsonify({'error': 'No question provided'}), 400
            
            # Generate AI response based on analysis context
            response = generate_chat_response(user_question, analysis_context)
            
            return jsonify({
                'response': response,
                'timestamp': time.time()
            })
            
        except Exception as e:
            logger.error(f"AI chat failed: {str(e)}")
            return jsonify({'error': 'Chat service unavailable'}), 500
    
    @app.route('/ai_dashboard')
    def ai_dashboard():
        """AI-powered executive dashboard"""
        try:
            # Get recent analysis results (this would typically come from a database)
            # For demo purposes, we'll use a sample
            sample_results = get_sample_analysis_results()
            
            dashboard_data = ai_log_summarizer.create_executive_dashboard(sample_results)
            
            return render_template('ai_dashboard.html', dashboard=dashboard_data)
            
        except Exception as e:
            logger.error(f"AI dashboard failed: {str(e)}")
            return render_template('ai_dashboard.html', 
                                 dashboard={'error': 'Dashboard unavailable'})

def generate_chat_response(question: str, context: dict) -> str:
    """
    Generate AI chat response based on user question and analysis context
    
    Args:
        question: User's question
        context: Analysis context and results
        
    Returns:
        AI-generated response
    """
    try:
        import openai
        
        # Prepare context summary
        context_summary = f"""
        Log Analysis Context:
        - Total Errors: {context.get('total_errors', 'Unknown')}
        - Errors Resolved: {context.get('errors_resolved', 'Unknown')}
        - Critical Threads: {context.get('critical_threads', 'Unknown')}
        - Components Affected: {context.get('components', 'Unknown')}
        - Analysis Duration: {context.get('duration', 'Unknown')}
        """
        
        prompt = f"""
        You are an IDPA log analysis expert. Answer the user's question based on the analysis context.
        
        {context_summary}
        
        User Question: {question}
        
        Provide a helpful, accurate response based on the analysis data. If you don't have enough 
        information, explain what additional data would be needed.
        """
        
        response = openai.ChatCompletion.create(
            model=AI_MODEL,
            messages=[
                {"role": "system", "content": "You are an expert IDPA system administrator helping users understand log analysis results."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=500
        )
        
        return response.choices[0].message.content
        
    except Exception as e:
        logger.error(f"Chat response generation failed: {str(e)}")
        return "I'm sorry, I'm unable to process your question right now. Please try again later."

def get_sample_analysis_results() -> dict:
    """Get sample analysis results for demo purposes"""
    return {
        'total_errors_count': '150',
        'total_errors_removed_or_eliminated': '120',
        'total_errors_left': '30',
        'percentage_reduction': '80%',
        'allthreads_list': [
            ('Thread-1', '15', [], '2024-01-01 10:00:00', '2024-01-01 10:30:00', '30 minutes', 'Backup operation', 'Avamar', [], []),
            ('Thread-2', '8', [], '2024-01-01 10:15:00', '2024-01-01 10:25:00', '10 minutes', 'Network check', 'vCenter', [], []),
        ],
        'log_start_time': '2024-01-01 10:00:00',
        'log_end_time': '2024-01-01 12:00:00',
        'logage': '2 hours'
    }

# Configuration helper
def setup_ai_configuration():
    """Setup AI configuration and validation"""
    config = {
        'ai_enabled': False,
        'api_key_valid': False,
        'model': AI_MODEL,
        'features': {
            'error_analysis': False,
            'solution_generation': False,
            'log_summarization': False,
            'chat_interface': False
        }
    }
    
    if OPENAI_API_KEY and OPENAI_API_KEY != 'your-api-key-here':
        try:
            # Test API key validity
            import openai
            openai.api_key = OPENAI_API_KEY
            
            # Simple test call
            test_response = openai.ChatCompletion.create(
                model=AI_MODEL,
                messages=[{"role": "user", "content": "Test"}],
                max_tokens=5
            )
            
            config['ai_enabled'] = True
            config['api_key_valid'] = True
            config['features'] = {
                'error_analysis': True,
                'solution_generation': True,
                'log_summarization': True,
                'chat_interface': True
            }
            
        except Exception as e:
            logger.error(f"AI configuration test failed: {str(e)}")
            config['error'] = str(e)
    
    return config

# Usage example in main application
if __name__ == "__main__":
    # Test the AI integration
    print("Testing AI Integration...")
    
    # Setup configuration
    config = setup_ai_configuration()
    print(f"AI Configuration: {config}")
    
    if config['ai_enabled']:
        # Test with sample log file
        sample_log = "2021_02_02_23_40_73426179344.log"
        if os.path.exists(sample_log):
            print(f"Running enhanced analysis on {sample_log}...")
            results = enhanced_log_analysis(sample_log, enable_ai=True)
            
            if results['success']:
                print("✅ AI-enhanced analysis completed successfully!")
                print(f"Original errors: {results['original_results']['total_errors_count']}")
                
                if 'executive_summary' in results['ai_enhancements']:
                    summary = results['ai_enhancements']['executive_summary']
                    print(f"Executive Summary: {summary.executive_summary}")
                    print(f"System Health Score: {summary.system_health_score}")
            else:
                print(f"❌ Analysis failed: {results['error']}")
        else:
            print(f"Sample log file {sample_log} not found")
    else:
        print("AI features disabled - check API key configuration")

# Additional utility functions for UI integration

def format_ai_insights_for_template(ai_enhancements: dict) -> dict:
    """Format AI insights for template rendering"""
    formatted = {
        'has_ai_data': bool(ai_enhancements and 'error' not in ai_enhancements),
        'executive_summary': None,
        'dashboard': None,
        'enhanced_threads': [],
        'ai_solutions': [],
        'pattern_analysis': None
    }

    if formatted['has_ai_data']:
        # Format executive summary
        if 'executive_summary' in ai_enhancements:
            summary = ai_enhancements['executive_summary']
            formatted['executive_summary'] = {
                'summary': summary.executive_summary,
                'key_findings': summary.key_findings,
                'health_score': summary.system_health_score,
                'risk_assessment': summary.risk_assessment,
                'recommendations': summary.recommended_actions
            }

        # Format dashboard
        if 'executive_dashboard' in ai_enhancements:
            dashboard = ai_enhancements['executive_dashboard']
            formatted['dashboard'] = {
                'status': dashboard.overall_status,
                'summary': dashboard.summary_text,
                'metrics': dashboard.key_metrics,
                'risk_level': dashboard.risk_level
            }

        # Format enhanced threads
        if 'enhanced_threads' in ai_enhancements:
            for thread in ai_enhancements['enhanced_threads']:
                formatted['enhanced_threads'].append({
                    'thread_name': thread['original_data'][0],
                    'ai_severity': thread.get('ai_severity', 'Medium'),
                    'ai_components': thread.get('ai_components', []),
                    'confidence': thread.get('ai_confidence', 0.5),
                    'insights': thread.get('ai_insights', [])
                })

        # Format AI solutions
        if 'ai_solutions' in ai_enhancements:
            for sol_data in ai_enhancements['ai_solutions']:
                solution = sol_data['solution']
                formatted['ai_solutions'].append({
                    'error': sol_data['error'][:100] + "..." if len(sol_data['error']) > 100 else sol_data['error'],
                    'summary': solution.solution_summary,
                    'immediate_actions': solution.immediate_actions,
                    'confidence': solution.confidence_score,
                    'resolution_time': solution.estimated_resolution_time
                })

        # Format pattern analysis
        if 'pattern_analysis' in ai_enhancements:
            formatted['pattern_analysis'] = ai_enhancements['pattern_analysis']

    return formatted
