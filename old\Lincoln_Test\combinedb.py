import sqlite3, os, sys, time

master_db = sqlite3.connect('main-db.db')
master_db.isolation_level = None	 
master_db.execute('PRAGMA foreign_keys = ON;')		
master_db.text_factory = str	  
master_db.execute('CREATE TABLE IF NOT EXISTS ERRORLOG ("ID" INTEGER, "LOGFILE" TEXT NOT NULL, "ADDED_TS" TEXT NOT NULL, "UPDATED_TS" TEXT, "THREAD" TEXT NOT NULL, "ERROR" TEXT NOT NULL UNIQUE, "IS_VALID" BOOL, "ADDED_BY" TEXT NOT NULL, "UPDATED_BY" TEXT, "BUG_REF" TEXT, "KB_REF" TEXT, "SUPPORT_COMMENT" TEXT , "ENGG_COMMENT" TEXT , PRIMARY KEY("ID" AUTOINCREMENT))')
cursor_master_db = master_db.cursor()

master_db_1 = sqlite3.connect('masterdb.db')
master_db_2 = sqlite3.connect('masterdb_2.db')
cursor_master_db_1 = master_db_1.cursor()
cursor_master_db_2 = master_db_2.cursor()

cursor_master_db_1.execute("select * from ERRORLOG")
data = cursor_master_db_1.fetchall()
print 'Found ' + str(len(data)) + ' records to enter into db'
st = time.time()
for el in data:
    cursor_master_db.execute("INSERT OR IGNORE INTO ERRORLOG (ID, LOGFILE, ADDED_TS, UPDATED_TS, THREAD, ERROR, IS_VALID, ADDED_BY, UPDATED_BY, BUG_REF, KB_REF, SUPPORT_COMMENT, ENGG_COMMENT) VALUES (?, ?, ?,?, ?, ?,?, ?, ?,?, ?, ?, ?)", el)
master_db.commit()
master_db_1.close()
print 'Done in ' + str(time.time() - st) + ' seconds'

st = time.time()
cursor_master_db_2.execute("select * from ERRORLOG")
data = cursor_master_db_2.fetchall()
print 'Found ' + str(len(data)) + ' records to enter into db'
st = time.time()
for el in data:
    cursor_master_db.execute("INSERT OR IGNORE INTO ERRORLOG (ID, LOGFILE, ADDED_TS, UPDATED_TS, THREAD, ERROR, IS_VALID, ADDED_BY, UPDATED_BY, BUG_REF, KB_REF, SUPPORT_COMMENT, ENGG_COMMENT) VALUES (?, ?, ?,?, ?, ?,?, ?, ?,?, ?, ?, ?)", el)
master_db.commit()
master_db_2.close()

master_db.close()        
print 'Done in ' + str(time.time() - st) + ' seconds'