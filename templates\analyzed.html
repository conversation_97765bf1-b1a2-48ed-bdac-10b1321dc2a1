<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IDPA Log Analyzer - AI Enhanced</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
            --light-bg: #f8fafc;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--dark-color);
        }
        
        .main-container {
            background: var(--light-bg);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .header-section {
            background: white;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--primary-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }
        
        .modern-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }
        
        .table-header {
            background: var(--primary-color);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
        }
        
        .search-box {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 1rem;
        }
        
        .table thead th {
            background: #f1f5f9;
            border: none;
            padding: 1rem;
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.9rem;
        }
        
        .table tbody td {
            padding: 1rem;
            border-color: #e2e8f0;
            vertical-align: middle;
        }
        
        .btn-modern {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border: none;
            transition: all 0.2s ease;
        }
        
        .btn-ai {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-ai:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .error-section {
            background: white;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            margin-top: 2rem;
        }
        
        .error-header {
            background: var(--danger-color);
            color: white;
            padding: 1.5rem;
            border-radius: 16px 16px 0 0;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .error-item {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .error-item:last-child {
            border-bottom: none;
        }
        
        .badge-modern {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.25rem 0.75rem;
        }
        
        .ai-modal .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .ai-modal .modal-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 16px 16px 0 0;
            border: none;
        }
        
        .ai-response {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid var(--primary-color);
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
        }
        
        .loading-spinner {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .collapsible-content {
            max-height: 400px;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .nav-pills {
            background: white;
            border-radius: 12px;
            padding: 0.5rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }
        
        .nav-pills .nav-link {
            border-radius: 8px;
            font-weight: 500;
            color: var(--secondary-color);
        }
        
        .nav-pills .nav-link.active {
            background: var(--primary-color);
        }
        
        footer {
            background: white;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid px-4">
            <!-- Header Section -->
            <div class="header-section">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2"><i class="fas fa-robot text-primary me-2"></i>AI-Enhanced Log Analysis</h1>
                        <p class="text-muted mb-0">
                            <strong>File:</strong> {{org_filename or filename}} | 
                            <strong>Duration:</strong> {{log_start_time}} - {{log_end_time}} ({{logage}})
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <nav class="nav nav-pills justify-content-end">
                            <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Home</a>
                            <a class="nav-link" href="/newparser"><i class="fas fa-plus me-1"></i>New Analysis</a>
                            <a class="nav-link" href="/reviewboard"><i class="fas fa-clipboard me-1"></i>Review Board</a>
                        </nav>
                    </div>
                </div>
            </div>

            {% if error %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Unable to analyze the logfile: {{error}}
            </div>
            {% else %}

            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--danger-color);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="mb-1">{{total_errors_count}}</h3>
                    <p class="text-muted mb-0">Total Errors Found</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--success-color);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="mb-1">{{total_errors_removed_or_eliminated}}</h3>
                    <p class="text-muted mb-0">Total Errors Eliminated</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--warning-color);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="mb-1">{{total_errors_left}}</h3>
                    <p class="text-muted mb-0">Total Errors Left</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--primary-color);">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h3 class="mb-1">{{difftime}}s</h3>
                    <p class="text-muted mb-0">Log Analyzed in</p>
                </div>
                {% if total_solutions %}
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--success-color);">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="mb-1">{{total_solutions}}</h3>
                    <p class="text-muted mb-0">Possible Solutions Found</p>
                </div>
                {% endif %}
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--primary-color);">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3 class="mb-1">{{percentage_reduction}}</h3>
                    <p class="text-muted mb-0">Erroneous/Redundant Reduction</p>
                </div>
            </div>

            <!-- Search Box -->
            <div class="search-box">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="searchInput" placeholder="Search threads, errors, or components...">
                </div>
            </div>

            <!-- Thread Analysis Table -->
            <div class="modern-table">
                <div class="table-header">
                    <h4 class="mb-0"><i class="fas fa-list-ul me-2"></i>Error Thread Details</h4>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="threadsTable">
                        <thead>
                            <tr>
                                <th>Thread Name</th>
                                <th>Thread Error Count</th>
                                <th>Thread Start</th>
                                <th>Thread End</th>
                                <th>Thread Age</th>
                                <th>Thread Activity</th>
                                <th>Thread Owner (Possible)</th>
                                <th>Thread Summary</th>
                                <th>Thread Error Stack</th>
                                <th>Thread Full Stack</th>
                                <th>Thread Solutions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for thinfo in allthreads_list %}
                            <tr>
                                <td><strong>{{thinfo[0]}}</strong></td>
                                <td><span class="badge badge-modern bg-danger">{{thinfo[1]}}</span></td>
                                <td>{{thinfo[3]}}</td>
                                <td>{{thinfo[4]}}</td>
                                <td>{{thinfo[5]}}</td>
                                <td>
                                    <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                        {{thinfo[6]}}
                                    </div>
                                </td>
                                <td><span class="badge badge-modern bg-info">{{thinfo[7]}}</span></td>
                                <td>
                                    <button class="btn btn-ai btn-modern btn-sm" onclick="showAISummary('{{thinfo[0]}}', '{{thinfo[7]}}', '{{thinfo[6]}}', {{loop.index}})">
                                        <i class="fas fa-robot me-1"></i>AI Summary
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#errorModal{{loop.index}}">
                                        <i class="fas fa-bug me-1"></i>Error Stack
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#fullModal{{loop.index}}">
                                        <i class="fas fa-list me-1"></i>Full Stack
                                    </button>
                                </td>
                                <td>
                                    {% if thinfo[9] %}
                                    <button class="btn btn-outline-success btn-sm" data-bs-toggle="modal" data-bs-target="#solutionModal{{loop.index}}">
                                        <i class="fas fa-lightbulb me-1"></i>Review Solutions
                                    </button>
                                    {% else %}
                                    <span class="text-muted">No Solutions</span>
                                    {% endif %}
                                </td>
                            </tr>

                            <!-- Error Modal -->
                            <div class="modal fade" id="errorModal{{loop.index}}" tabindex="-1">
                                <div class="modal-dialog modal-xl">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title"><i class="fas fa-bug me-2"></i>Thread Error Stack - {{thinfo[0]}}</h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            {% for x in thinfo[2] %}
                                            <div class="error-item">
                                                <code class="text-danger">{{x}}</code>
                                                <div>
                                                    <a href="https://jira.cec.lab.emc.com/issues/?jql=text~'{{x.split(': ')[1] if ': ' in x else x}}'" target="_blank" class="btn btn-outline-primary btn-sm me-1">
                                                        <i class="fab fa-jira me-1"></i>JIRA Search
                                                    </a>
                                                    <button class="btn btn-ai btn-sm" onclick="analyzeError('{{x}}')">
                                                        <i class="fas fa-robot me-1"></i>AI Analyze
                                                    </button>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Full Log Modal -->
                            <div class="modal fade" id="fullModal{{loop.index}}" tabindex="-1">
                                <div class="modal-dialog modal-xl">
                                    <div class="modal-content">
                                        <div class="modal-header bg-warning text-dark">
                                            <h5 class="modal-title"><i class="fas fa-list me-2"></i>Thread Stack - {{thinfo[0]}}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body" style="max-height: 500px; overflow-y: auto;">
                                            {% for x in thinfo[8] %}
                                            <div class="mb-1">
                                                {% if 'ERROR' in x %}
                                                <code class="text-danger"><strong>{{x}}</strong></code>
                                                {% else %}
                                                <code>{{x}}</code>
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Solutions Modal -->
                            {% if thinfo[9] %}
                            <div class="modal fade" id="solutionModal{{loop.index}}" tabindex="-1">
                                <div class="modal-dialog modal-xl">
                                    <div class="modal-content">
                                        <div class="modal-header bg-success text-white">
                                            <h5 class="modal-title"><i class="fas fa-lightbulb me-2"></i>Possible Solutions Found - {{thinfo[0]}}</h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Internal Error ID</th>
                                                            <th>Error</th>
                                                            <th>VALID</th>
                                                            <th>Last Updated</th>
                                                            <th>Updated By</th>
                                                            <th>Jira Bug</th>
                                                            <th>KB Ref</th>
                                                            <th>Lightning KB</th>
                                                            <th>Support Comments</th>
                                                            <th>Engg. Comments</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for x in thinfo[9] %}
                                                        <tr>
                                                            <td>{{x[0]}}</td>
                                                            <td><code>{{x[5]}}</code></td>
                                                            <td>
                                                                {% if 'True' in x[6] %}
                                                                <span class="badge bg-success">{{x[6]}}</span>
                                                                {% else %}
                                                                <span class="badge bg-danger">{{x[6]}}</span>
                                                                {% endif %}
                                                            </td>
                                                            <td>{{x[3]}}</td>
                                                            <td>{{x[8]}}</td>
                                                            <td>
                                                                {% if x[9] and 'None' not in x[9] %}
                                                                <a href="https://jira.cec.lab.emc.com/browse/DDOSCFD-{{x[9]}}" target="_blank">{{x[9]}}</a>
                                                                {% else %}
                                                                {{x[9]}}
                                                                {% endif %}
                                                            </td>
                                                            <td>
                                                                {% if x[10] and 'None' not in x[10] %}
                                                                <a href="https://support.emc.com/kb/{{x[10]}}" target="_blank">{{x[10]}}</a>
                                                                {% else %}
                                                                {{x[10]}}
                                                                {% endif %}
                                                            </td>
                                                            <td>
                                                                {% if x[11] and 'None' not in x[11] %}
                                                                <a href="https://www.dell.com/support/kbdoc/en-in/{{x[11]}}" target="_blank">{{x[11]}}</a>
                                                                {% else %}
                                                                {{x[11]}}
                                                                {% endif %}
                                                            </td>
                                                            <td>{{x[12]}}</td>
                                                            <td>{{x[13]}}</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Error Summary Sections -->
            <div class="error-section">
                <div class="error-header" onclick="toggleSection('nonRedundantErrors')">
                    <div>
                        <h4 class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>ALL NON-REDUNDANT ERRORS FROM LOG ({{main_errors_stack|length}})</h4>
                    </div>
                    <i class="fas fa-chevron-down" id="nonRedundantErrors-icon"></i>
                </div>
                <div class="collapsible-content" id="nonRedundantErrors" style="display: none;">
                    {% for line in main_errors_stack %}
                    <div class="error-item">
                        <div>
                            <strong>{{loop.index}}.</strong> <code>{{line}}</code>
                        </div>
                        <div>
                            <a href="https://jira.cec.lab.emc.com/issues/?jql=text~'{{line.split(': ')[1] if ': ' in line else line}}'" target="_blank" class="btn btn-outline-primary btn-sm me-1">
                                <i class="fab fa-jira me-1"></i>JIRA Search
                            </a>
                            <button class="btn btn-ai btn-sm" onclick="analyzeError('{{line}}')">
                                <i class="fas fa-robot me-1"></i>AI Analyze
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <div class="error-section">
                <div class="error-header" onclick="toggleSection('relevantErrors')" style="background: var(--warning-color);">
                    <div>
                        <h4 class="mb-0"><i class="fas fa-list me-2"></i>ALL RELEVANT ERRORS FROM LOG ({{fullerrorstack|length}})</h4>
                    </div>
                    <i class="fas fa-chevron-down" id="relevantErrors-icon"></i>
                </div>
                <div class="collapsible-content" id="relevantErrors" style="display: none;">
                    {% if fullerrorstack|length > 50 %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        More than 50 lines found for Error logs. Showing first 50 lines.
                    </div>
                    {% endif %}
                    {% for lines in fullerrorstack[:50] %}
                    <div class="error-item">
                        <code>{{lines}}</code>
                    </div>
                    {% endfor %}
                </div>
            </div>

            {% if file_list_obj %}
            <div class="text-center mt-4">
                <a href="/download_log/{{logfilename}}" class="btn btn-primary me-2">
                    <i class="fas fa-download me-1"></i>Download {{logfilename}}
                </a>
            </div>
            {% endif %}

            {% endif %}
        </div>
    </div>

    <!-- Footer -->
    <footer class="text-center py-4 mt-5">
        <div class="container">
            <p class="text-muted mb-0">Developed with ❤️ by Pankaj Pande</p>
        </div>
    </footer>

    <!-- AI Summary Modal -->
    <div class="modal fade ai-modal" id="aiSummaryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-robot me-2"></i>AI Thread Summary</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="aiSummaryContent">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-3">AI is analyzing the thread...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Error Analysis Modal -->
    <div class="modal fade ai-modal" id="aiErrorModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-robot me-2"></i>AI Error Analysis</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="aiErrorContent">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-3">AI is analyzing the error...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        window.allThreadsData = {{ allthreads_list|tojson }};

        function showAISummary(threadName, threadOwner, threadActivity, rowIndex) {
            const modal = new bootstrap.Modal(document.getElementById('aiSummaryModal'));
            const content = document.getElementById('aiSummaryContent');
            
            content.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status"></div>
                    <span class="ms-3">AI is analyzing thread: <strong>${threadName}</strong></span>
                </div>
            `;
            
            modal.show();
            
            const threadStack = window.allThreadsData[rowIndex-1][2];
            
            fetch('/ai_thread_summary', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    thread_name: threadName,
                    thread_stack: threadStack,
                    thread_owner: threadOwner,
                    thread_activity: threadActivity
                })
            })
            .then(response => response.json())
            .then(data => {
                content.innerHTML = `
                    <div class="ai-response">
                        <h6 class="mb-3"><i class="fas fa-brain text-primary me-2"></i>AI Analysis for Thread: ${threadName}</h6>
                        <div class="mb-3">
                            <strong>Component:</strong> <span class="badge bg-info">${threadOwner}</span>
                        </div>
                        <div class="mb-3">
                            <strong>Activity:</strong> ${threadActivity}
                        </div>
                        <hr>
                        <div class="ai-summary">
                            ${data.summary.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                `;
            })
            .catch(error => {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            });
        }

        function analyzeError(errorMessage) {
            const modal = new bootstrap.Modal(document.getElementById('aiErrorModal'));
            const content = document.getElementById('aiErrorContent');
            
            content.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status"></div>
                    <span class="ms-3">AI is analyzing the error...</span>
                </div>
            `;
            
            modal.show();
            
            setTimeout(() => {
                content.innerHTML = `
                    <div class="ai-response">
                        <h6 class="mb-3"><i class="fas fa-bug text-danger me-2"></i>Error Analysis</h6>
                        <div class="mb-3">
                            <strong>Error:</strong><br>
                            <code class="d-block p-2 bg-light rounded">${errorMessage}</code>
                        </div>
                        <hr>
                        <div class="mb-3">
                            <strong>AI Analysis:</strong><br>
                            This error appears to be related to component connectivity issues. Recommended actions include checking network connectivity and service status.
                        </div>
                        <div class="mb-3">
                            <strong>Suggested KB Articles:</strong>
                            <ul class="mt-2">
                                <li>KB123456 - Component Connection Troubleshooting</li>
                                <li>KB789012 - Service Recovery Procedures</li>
                                <li>KB345678 - Network Configuration Guide</li>
                            </ul>
                        </div>
                    </div>
                `;
            }, 2000);
        }

        function toggleSection(sectionId) {
            const section = document.getElementById(sectionId);
            const icon = document.getElementById(sectionId + '-icon');
            
            if (section.style.display === 'none') {
                section.style.display = 'block';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            } else {
                section.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        }

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('#threadsTable tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    </script>
</body>
</html>