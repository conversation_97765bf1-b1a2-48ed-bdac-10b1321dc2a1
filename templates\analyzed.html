<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />

<center>
<title>LAFI : Analyzer Result</title>
</center>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<link href='https://fonts.googleapis.com/css?family=Raleway' rel='stylesheet'>
<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'>
<style type="text/css">
.button {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    
    padding: 1%;
    background: #33568e;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}
.button1 {
  background-color: #6A5ACD;
  color: white;
  font-size: 11px;  
  border: 2px solid #40E0D0;
}

.button1:hover {
  background-color: #40E0D0;
  color: white;
}

.button2 {
  padding: 0.3%;
  background-color: white;
  color: black;
  font-size: 10px;  
  border: 2px solid #4CAF50;`
}

.button2:hover {
  background-color: #008CBA;
  color: white;
}

.button3 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    padding: 3%;
    background: #0dbaab;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}

.button2:hover {
  background-color: #008CBA;
  color: white;
}
.modal-dialog-full-width {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width:none !important;

}

.modal-content-full-width  {
    height: auto !important;
    min-height: 100% !important;
    border-radius: 0 !important;
    background-color: #FFFAFA !important 
}

.modal-header-full-width  {
    border-bottom: 1px solid #33568e !important;
}

.modal-footer-full-width  {
    border-top: 1px solid #33568e !important;
}

/* BELOW IS OUR ORIGINAL CODE */
        h3 span {
            font-size: 22px;
        }
        h3 input.search-input {
            width: 300px;
            margin-left: auto;
            float: right
        }
        .mt32 {
            margin-top: 32px;
        }
.card {
  /* Add shadows to create the "card" effect */
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  transition: 0.3s;
}

/* On mouse-over, add a deeper shadow */
.card:hover {
  box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
}

/* Add some padding inside the card container */
.container {
  padding: 2px 16px;
}

.container2 {
  padding: 2px 100px;
}
.alert {
    padding: 1em;
    background: yellow;
}

.form-style-7{
    font: 95% Arial, Helvetica, sans-serif;
    max-width: 1200px;
    margin: 10px auto;
    padding: 16px;
    background: white;
    font-family: 'Montserrat';
	
}

.form-style-6{
    font: 95% Arial, Helvetica, sans-serif;
    max-width: 400px;
    margin: 10px auto;
    padding: 16px;
    background: white;
    font-family: 'Montserrat';
	
}
.form-style-6 h1{
    background: #0dbaab;
    padding: 20px 0;
    font-size: 140%;
    font-weight: 300;
    text-align: center;
    color: #fff;
    margin: -16px -16px 16px -16px;
   font-family: 'Montserrat';
}


.form-style-6 input[type="text"],
.form-style-6 input[type="date"],
.form-style-6 input[type="datetime"],
.form-style-6 input[type="email"],
.form-style-6 input[type="number"],
.form-style-6 input[type="search"],
.form-style-6 input[type="time"],
.form-style-6 input[type="url"],
.form-style-6 input[type="password"],
.form-style-6 textarea,
.form-style-6 select 
{
    -webkit-transition: all 0.30s ease-in-out;
    -moz-transition: all 0.30s ease-in-out;
    -ms-transition: all 0.30s ease-in-out;
    -o-transition: all 0.30s ease-in-out;
    outline: none;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    background: #fff;
    margin-bottom: 4%;
    border: 1px solid #ccc;
    padding: 3%;
    color: #555;
    font: 95% Arial, Helvetica, sans-serif;
	font-family: 'Montserrat';
}
.form-style-6 input[type="text"]:focus,
.form-style-6 input[type="date"]:focus,
.form-style-6 input[type="datetime"]:focus,
.form-style-6 input[type="email"]:focus,
.form-style-6 input[type="number"]:focus,
.form-style-6 input[type="search"]:focus,
.form-style-6 input[type="time"]:focus,
.form-style-6 input[type="url"]:focus,
.form-style-6 textarea:focus,
.form-style-6 select:focus
{
    box-shadow: 0 0 5px #43D1AF;
    padding: 3%;
    border: 1px solid #43D1AF;
	font-family: 'Montserrat';
}

.form-style-6 input[type="submit"],
.form-style-6 input[type="button"]{
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    padding: 3%;
    background: #33568e;
    border-bottom: 2px solid #30C29E;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
	font-family: 'Montserrat';
}
.form-style-6 input[type="submit"]:hover,
.form-style-6 input[type="button"]:hover{
    background: #2EBC99;
}
body {
    font-family: 'Montserrat';font-size: 15px;
}
.hide{
display:none;
}

.loge {
    border: 1px solid;
    padding: 20px; 
    width: 1200px;
    resize: both;
    overflow: auto;
	text-align: left
}
.other_details{
    
    padding: 20px; 
    width: 1200px;
    resize: both;

	text-align: left;
	
}
</style>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-F72SBSX0JW"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-F72SBSX0JW');
</script>

</head>
<body>
<center>
<br>
<center><h1><font color="blue">DELL</font> <font color="grey">TECHNOLOGIES</font></h1></center>
<button onclick="location.href='/'" type="button" class="button button2">
Home
</button>
<button onclick="location.href='newparser'" type="button" class="button button2">
Advance Parser
</button>
<button onclick="location.href='reviewboard'" type="button" class="button button2">
Review Board
</button>

<div class="form-style-6" id="pande">
<h1>Automated Log Analysis</h1>
</div>
<br/><br/>


{% if error %}
Unable to analyze the logfile due to failure in downloading log from Lincoln...
<br><br>
<a href="/">HOME</a>
{% else %}
<center>
<div class="container" id="pankaj">
<br>

{% if total_errors_left == '0' %}
<h3><font color="red">Congratulations, there is nothing left in this log file to be looked at.....</font></h3>
<br><br>
{% endif %}

<p align="left">
<strong>
Logfile: {{org_filename}} <br>
Log Start Date : {{log_start_time}} <br>
Log End Date : {{log_end_time}} <br>
Log Duration : {{logage}}
</strong>
</p>
<br>

  
 
</div>
</center> 
 
<br><br>
<font size="3">Search Support <input type="text" id="searchtext" name="searchtext"> <a target="_blank" onclick="this.href='https://support.emc.com/search/?text=' + document.getElementById('searchtext').value"><button>Go</button></a></font>
<br><br>

<!-- TABLE DIV START -->	
<div class="content mt-3">
<h2>Error Thread(s) Details</h2>
<!-- TABLE START -->
<table id="bootstrap-data-table" class="table table-striped table-bordered dataTable no-footer" role="grid" aria-describedby="bootstrap-data-table_info">
<!-- TABLE HEAD START-->
<thead class="sorting_desc">
                <tr role="row">
                    <th>Thread Name</th>
                    <th>Thread Error Count</th>
                    <th>Thread Start</th>						
                    <th>Thread End</th>
					<th>Thread Age</th>
					<th>Thread Activity</th>
					<th>Thread Owner (Possible)</th>
                    <th>Thread Error Stack</th>				
					<th>Thread Full Stack</th>
					<th>Thread Solutions</th>					
                </tr>
</thead>
<!-- TABLE HEAD END -->
<!-- TBODY START -->			
<tbody>
			{% for thinfo in allthreads_list %}
			<tr role="row" class="odd" >
			<td class="sorting_1"> {{thinfo[0]}}</td>
			<td class="sorting_1">{{thinfo[1]}}</td>
			<td class="sorting_1">{{thinfo[3]}}</td>
			<td class="sorting_1">{{thinfo[4]}}</td>
			<td class="sorting_1">{{thinfo[5]}}</td>
			<td class="sorting_1">{{thinfo[6]}}</td>
			<td class="sorting_1">{{thinfo[7]}}</td>
			

			
			


<td class="sorting_1">
<button id="modalActivate" type="button" class="btn btn-danger" data-toggle="modal" data-target="#{{thinfo[0]}}_1">
Error Stack
</button>

<!-- Modal -->
<div class="modal fade right" id="{{thinfo[0]}}_1" tabindex="-1" role="dialog" aria-labelledby="exampleModalPreviewLabel" aria-hidden="true">
    <div class="modal-dialog-full-width modal-dialog momodel modal-fluid" role="document">
        <div class="modal-content-full-width modal-content ">
            <div class=" modal-header-full-width   modal-header text-center">
                <h5 class="modal-title w-100" id="exampleModalPreviewLabel">Thread Error Stack for {{thinfo[0]}}</h5>
                <button type="button" class="close " data-dismiss="modal" aria-label="Close">
                    <span style="font-size: 1.3em;" aria-hidden="true">&times;</span>
                </button>
            </div>
			
			
			
            <div class="modal-body">
                    {% for x in thinfo[2] %}
			        <font color="red">{{x}}</font> 
					
					<a href="http://appeng-hop.asl.lab.emc.com/search/?query={{x.split(''': ''')[1]}}" target="_blank"><button class="button button2">Bugzilla Search</button></a>
					
					<a href="https://jira.cec.lab.emc.com/issues/?jql=project%20in%20(DDOSCFD%2C%20IDPA)%20AND%20issuetype%20in%20(Bug%2C%20Epic%2C%20Error%2C%20Initiative%2C%20Spike%2C%20Story%2C%20Task)%20AND%20text~'{{x.split(''': ''')[1]}}'" target="_blank"><button class="button button2">JIRA Search</button></a>
					
					<br>
			        {% endfor %}
            </div>
            <div class="modal-footer-full-width  modal-footer">
                <button type="button" class="btn btn-danger btn-md btn-rounded" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
</td>






			
			
			
<td class="sorting_1">
<button id="modalActivate" type="button" class="btn btn-warning" data-toggle="modal" data-target="#{{thinfo[0]}}_2">
Full Stack
</button>

<!-- Modal -->
<div class="modal fade right" id="{{thinfo[0]}}_2" tabindex="-1" role="dialog" aria-labelledby="exampleModalPreviewLabel" aria-hidden="true">
    <div class="modal-dialog-full-width modal-dialog momodel modal-fluid" role="document">
        <div class="modal-content-full-width modal-content ">
            <div class=" modal-header-full-width   modal-header text-center">
                <h5 class="modal-title w-100" id="exampleModalPreviewLabel">Thread Stack for {{thinfo[0]}}</h5>
                <button type="button" class="close " data-dismiss="modal" aria-label="Close">
                    <span style="font-size: 1.3em;" aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                    {% for x in thinfo[8] %}
					{% if 'ERROR' in x %}
			        <font color="red"><b>{{x}}</b></font><br>
					{% else %}
					{{x}} <br>
					{% endif %}
			        {% endfor %}
            </div>
            <div class="modal-footer-full-width  modal-footer">
                <button type="button" class="btn btn-danger btn-md btn-rounded" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
</td>



<td class="sorting_1">
{% if thinfo[9] %}
<button id="modalActivate" type="button" class="btn btn-success" data-toggle="modal" data-target="#{{thinfo[0]}}_3">
Review Solutions
</button>
{% endif %}


<div class="modal fade right" id="{{thinfo[0]}}_3" tabindex="-1" role="dialog" aria-labelledby="exampleModalPreviewLabel" aria-hidden="true">
    <div class="modal-dialog-full-width modal-dialog momodel modal-fluid" role="document">
        <div class="modal-content-full-width modal-content ">
            <div class=" modal-header-full-width   modal-header text-center">
                <h5 class="modal-title w-100" id="exampleModalPreviewLabel">Possible Solutions Found for {{thinfo[0]}}</h5>
                <button type="button" class="close " data-dismiss="modal" aria-label="Close">
                    <span style="font-size: 1.3em;" aria-hidden="true">&times;</span>
                </button>
            </div>
        <div class="modal-body">
		
		
		<!-- Modal 
					{% for x in thinfo[9] %}
					<b>FOR ERROR MESSAGE :   <font color="red">{{x[5]}}</font> </b><br>
					
					<b>VALID         : 
					{% if 'True' in x[6] %}
					<font color="red">{{x[6]}} </font> <br>
					{% else %}
					<font color="green">{{x[6]}} </font> <br>
					{%endif%}
					</b> 					
					
                    <b>Error Entry ID     : </b>  {{x[0]}}  <br>
                    <b>Last Updated   : </b>  {{x[3]}}  <br>
					<b>Updated By      : </b>  {{x[8]}}  <br>
					<b>Bug Ref         : </b>  {{x[9]}}  <br>
                    <b>KB Ref		   : </b>  {{x[10]}} <br>
					<b>Support Comment : </b>  {{x[11]}} <br>
					<b>Engg. Comment   : </b>  {{x[12]}} <br><br>
	                <p>---------------------------------------------</p>
			        {% endfor %}
		-->



<div class="content mt-3">
<table id="bootstrap-data-table" class="table table-striped table-bordered dataTable no-footer" role="grid" aria-describedby="bootstrap-data-table_info">
    <thead class="sorting_desc">
        <tr role="row">
        <th>Internal Error ID </th>
        <th>Error </th>
        <th>VALID</th>				
        <th>Last Updated </th>
        <th>Updated By  </th>
        <th>Jira Bug</th>
        <th>KB Ref </th>
        <th>Lightning KB</th>		
        <th>Support Comments </th>	
        <th>Engg. Comments </th>
        </tr>
    </thead>
    <tbody>
      {% for x in thinfo[9] %}
        <tr role="row" class="odd">		  
        <td class="sorting_1">{{x[0]}} </td>
        <td class="sorting_1">{{x[5]}} </td>
		
		
		{% if 'True' in x[6] %}
		<td class="sorting_1"><font color="red"><b>{{x[6]}} </b></font></td> 
		{% else %}
		<td class="sorting_1"><font color="green"><b>{{x[6]}} </b></font></td>
		{%endif%}		
		
        
		
		
        <td class="sorting_1">{{x[3]}} </td>
        <td class="sorting_1">{{x[8]}} </td>
	    <!--bug
        {% if not x[9] or 'None' in x[9]%}<td class="sorting_1">{{x[9]}}</td>{%else%}<td class="sorting_1"><a href="https://esc.avamar.com/show_bug.cgi?id={{x[9]}}" target="_blank">{{x[9]}}</a></td>{%endif%} 
        -->
        {% if not x[9] or 'None' in x[9]%}<td class="sorting_1">{{x[9]}}</td>{%else%}<td class="sorting_1"><a href="https://jira.cec.lab.emc.com/browse/DDOSCFD-{{x[9]}}" target="_blank">{{x[9]}}</a></td>{%endif%}


		
		<!--kb-->
        {% if not x[10] or 'None' in x[10] %}<td class="sorting_1">{{x[10]}}</td>{%else%}<td class="sorting_1"><a href="https://support.emc.com/kb/{{x[10]}}" target="_blank">{{x[10]}}</a></td>{%endif%} 
        {% if not x[11] or 'None' in x[11] %}<td class="sorting_1">{{x[11]}}</td>{%else%}<td class="sorting_1"><a href="https://www.dell.com/support/kbdoc/en-in/{{x[11]}}" target="_blank">{{x[11]}}</a></td>{%endif%} 		
        <td class="sorting_1">{{x[12]}} </td>
        <td class="sorting_1">{{x[13]}} </td>		
        </tr>				
        {% endfor %}

    </tbody>
</table>
</div>




        </div>
            <div class="modal-footer-full-width  modal-footer">
                <button type="button" class="btn btn-danger btn-md btn-rounded" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
</td>
<!-- TD END -->
</tr>
{% endfor %}
</tbody>
<!-- TBODY END -->
</table>
<!-- TABLE END -->
</div>
<!-- TABLE DIV END -->



<br><br>
<div class="container">
<b><center>ALL NON-REDUNDANT ERRORS FROM LOG (Errors : {{main_errors_stack|length}})</center></b>
<br/>
<div class="loge">
{% for line in main_errors_stack%}
{{loop.index}}) {{line}} &nbsp; &nbsp;
<a href="http://appeng-hop.asl.lab.emc.com/search/?query={{line.split('-')[1]}}" target="_blank"><button class="button button2">Bugzilla Search</button></a>
&nbsp; &nbsp;
<a href="https://jira.cec.lab.emc.com/issues/?jql=project%20in%20(DDOSCFD%2C%20IDPA)%20AND%20issuetype%20in%20(Bug%2C%20Epic%2C%20Error%2C%20Initiative%2C%20Spike%2C%20Story%2C%20Task)%20AND%20text~'{{line.split(''': ''')[1]}}'" target="_blank"><button class="button button2">JIRA Search</button></a>
					
<br> 
---------------------------------------------
<br>
{%endfor%}
</div>
<br>
<b><center>ALL RELEVANT ERRORS FROM LOG (Errors : {{fullerrorstack|length}})</center></b>
<br/>

<div class="loge">
{% if fullerrorstack|length  > 50 %}

More than 50 lines found for Error logs, therefore, we have to hide them from viewport...Click on <button onclick="myFunction()">View Logs</button> to view them.

<div id="myDIV" style="display:none">
{% for lines in fullerrorstack%}
{{lines}} <br>
{% endfor %}
</div>

{% else %}
{% for lines in fullerrorstack%}
{{lines}} <br>
{% endfor %}
{% endif %}
</div>


<br/><br/>








  <div class="card-columns">
    <div class="card bg-danger">
      <div class="card-body text-center">
        <p class="card-text"><b><font color="white">Total Errors Found : {{total_errors_count}}</font></b></p>
      </div>
    </div>
    <div class="card bg-success">
      <div class="card-body text-center">
        <p class="card-text"><b><font color="white">Possible Solutions Found : {{total_solutions}}</font></b></p>
      </div>
    </div>
    <div class="card bg-success">
      <div class="card-body text-center">
        <p class="card-text"><b><font color="white">Total Errors Eliminated : {{total_errors_removed_or_eliminated}}</font></b></p>
      </div>
    </div>
    <div class="card bg-primary">
      <div class="card-body text-center">
        <p class="card-text"><b><font color="white">Log Analyzed in : {{difftime}} seconds</font></b></p>
      </div>
    </div>  
	
    <div class="card bg-warning">
      <div class="card-body text-center">
        <p class="card-text"><b>Total Errors Left : {{total_errors_left}}</font></b></p>
      </div>
    </div>	
	

	
	
    <div class="card bg-info">
      <div class="card-body text-center">
        <p class="card-text"><b><font color="white">Erroneous/Redundant Reduction : {{percentage_reduction}}</font></b></p>
      </div>
    </div>
  </div>






















{% if file_list_obj%}
<a href="/download_log/{{logfilename}}"><button class="button button1">Download {{logfilename}}</button></a>
<br><br>
-----------------------------------------------------------
<br><br>
<b>Select other files for Review</b>
<br><br>
<form method=POST action="/review_log" onsubmit="ShowLoading()">
{%for file in file_list_obj%}
<p align="left">
<font color="red"><input type="radio" name="file_name" value="{{file}}!{{lincoln_path}}!{{file_list_obj}}">  {{file}}</font><br/>
{% endfor %}
<br/><br/>
<input type="submit" value='Process'>
</p>
</form>
<br><br>
{% endif %}




<a href="newparser"><button class="button button1">PARSER HOME</button></a> <a href="/"><button class="button button1">HOME</button></a>
<br><br>
</div>
</center>
{% endif %}


<script>
function myFunction() {
  var x = document.getElementById("myDIV");
  if (x.style.display === "none") {
    x.style.display = "block";
  } else {
    x.style.display = "none";
  }
}
</script>


    <script>
        (function(document) {
            'use strict';

            var TableFilter = (function(myArray) {
                var search_input;

                function _onInputSearch(e) {
                    search_input = e.target;
                    var tables = document.getElementsByClassName(search_input.getAttribute('data-table'));
                    myArray.forEach.call(tables, function(table) {
                        myArray.forEach.call(table.tBodies, function(tbody) {
                            myArray.forEach.call(tbody.rows, function(row) {
                                var text_content = row.textContent.toLowerCase();
                                var search_val = search_input.value.toLowerCase();
                                row.style.display = text_content.indexOf(search_val) > -1 ? '' : 'none';
                            });
                        });
                    });
                }

                return {
                    init: function() {
                        var inputs = document.getElementsByClassName('search-input');
                        myArray.forEach.call(inputs, function(input) {
                            input.oninput = _onInputSearch;
                        });
                    }
                };
            })(Array.prototype);

            document.addEventListener('readystatechange', function() {
                if (document.readyState === 'complete') {
                    TableFilter.init();
                }
            });

        })(document);
    </script>





    <script src="{{url_for('static', filename='assets/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/datatables.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/dataTables.bootstrap.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/dataTables.buttons.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.bootstrap.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/jszip.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/pdfmake.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/vfs_fonts.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.html5.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.print.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.colVis.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/datatables-init.js')}}"></script>



<script type="text/javascript">
    function ShowLoading(e) {
        var elmnt = document.getElementById("pande");	
        elmnt.scrollIntoView();	
        var div = document.createElement('div');
        var img = document.createElement('img');
	    var imgArray = ['{{url_for('static', filename="_preloader.gif")}}']
	    var randomImage = Math.floor(Math.random()*imgArray.length);
        //img.src = 'b6a8F5G.gif';
        img.src = imgArray[randomImage];
        div.innerHTML = "<br><b>Fetching Files...<b>&nbsp&nbsp&nbsp&nbsp&nbsp<br><br>Please don't refresh or close this page &nbsp&nbsp&nbsp&nbsp&nbsp<br />";
        div.style.cssText = 'position: absolute;top: 30%; left: 0;  right: 0;  margin-top: 0; text-align: center;background:rgba(240,240,240,0.9); padding:0 20px;box-sizing:border-box;';
        div.appendChild(img);
        document.body.appendChild(div);
        return true;
        // These 2 lines cancel form submission, so only use if needed.
        //window.event.cancelBubble = true;
        //e.stopPropagation();
    }

	
</script>

</body>
</html>
