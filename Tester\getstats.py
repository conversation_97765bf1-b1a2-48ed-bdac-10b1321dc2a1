import os, logging, shlex
#import analyzer as p
import time, sys
from logging.handlers import RotatingFileHandler
import subprocess
import analyzer as p
logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s | %(levelname)s | %(message)s')

stdout_handler = logging.StreamHandler(sys.stdout)
stdout_handler.setLevel(logging.DEBUG)
stdout_handler.setFormatter(formatter)

file_handler = logging.FileHandler('logs.log')
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(formatter)


logger.addHandler(file_handler)
logger.addHandler(stdout_handler)

logger.info('Starting Business....')
logger.info('INDIVIDUAL FILE ANALYSIS')
#logfilename = '/root/parser_log_store/homeadminlogs12185755acm-config-logslogsserver.log.7'
logfilename = '/root/logparser-UI/Uploads/2021_02_02_23_40_73426179344.log'
#logfilename = raw_input('FILE:')
p.main_analyzer(logfilename, False)
sys.exit()





st = time.time()

cmd = 'ls /root/parser_log_store/*.log.*[0-9]'

proc=subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE, shell=True, universal_newlines=True)
#proc = subprocess.Popen(shlex.split(cmd, posix=True),stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE, env=env)
std_out, std_err = proc.communicate()
a = std_out
_filelist = a.split('\n')

filelist = []
for filename in _filelist[:-1]:
    filelist.append(1)
    logfilename = filename
    logger.info('Analyzing Log : ' + logfilename)
#    try:
    p.main_analyzer(logfilename, False)
#    except Exception as e:
#        logger.info('FAILED on logfile : ' + logfilename + ' due to ' + str(e))
et = time.time()
logger.info('Took ' + str(et -st) + ' seconds to process ' + str(len(filelist)) + ' files')
logger.info('Ended Business....')

