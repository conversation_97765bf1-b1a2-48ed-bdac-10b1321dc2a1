import re, sqlite3, datetime

stringdict = {"Avamar isConfigPackageReady failed":"avadapter.AvamarUtil: Avamar Server: true", "ViJavaServiceInstanceProvider" : "ViJavaServiceInstanceProvider: getServiceInstanceWithRetries--> Able to connect to vCenter","updateNTPSettingsOnAvamarServer->Start":"Successfully executed command - su -c 'ntpdate", 'File system is not enabled or not running after rebooting':'The filesystem is enabled and running'}

mainlog = ['2018-12-10 23:59:37,693 INFO  [pool-20-thread-6]-avadapter.DeployAvamarVAppTask: Executing deploy Avamar vApp task.\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Received notifyStatus from task : AVAMAR:DEPLOY:AVAMAR_VAPP:0:IN_PROGRESS:1:10%:0:0\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Action progress percentage = 55 for action : AVAMAR:DEPLOY\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-configure.WorkflowManager: Received notifyStatus from action : AVAMAR:DEPLOY:IN_PROGRESS:0:55%:0:0\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-configure.WorkflowManager: Overall progress percentage = 20\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-configure.WorkflowManager: Warnings = 0\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-configure.WorkflowManager: Errors = 0\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-configure.WorkflowManager: Are there critical components failed = false\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Status notified from Workflow manager.\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Trying to save configuration status in file.\n', '2018-12-10 23:59:37,694 INFO  [pool-20-thread-6]-dao.InfrastructureComponentsDAOImpl: getInfrastructureInfo Retrieving infrastructure components details from file /usr/local/dataprotection/var/configmgr/server_data/config/InfrastructureComponents.xml\n', '2018-12-10 23:59:37,696 INFO  [pool-20-thread-6]-dao.InfrastructureComponentsDAOImpl: getInfrastructureInfo Successfully retrieved infrastructure components details\n', '2018-12-10 23:59:37,696 INFO  [pool-20-thread-6]-skuadapter.SKUSelectionService: Getting sku modelVersion.\n', '2018-12-10 23:59:37,696 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml\n', '2018-12-10 23:59:37,729 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration\n', '2018-12-10 23:59:37,729 INFO  [pool-20-thread-6]-dao.ApplianceStatusDAOImpl: Getting appliance Status from file at: /usr/local/dataprotection/var/configmgr/server_data/status/applianceStatus.xml\n', '2018-12-10 23:59:37,730 INFO  [pool-20-thread-6]-dao.ApplianceStatusDAOImpl: Successfully retrieved appliance status\n', '2018-12-10 23:59:37,730 INFO  [pool-20-thread-6]-skuadapter.SKUSelectionService: Getting sku modelVersion.\n', '2018-12-10 23:59:37,730 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml\n', '2018-12-10 23:59:37,762 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration\n', '2018-12-10 23:59:37,762 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml\n', '2018-12-10 23:59:37,763 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration\n', '2018-12-10 23:59:37,763 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml\n', '2018-12-10 23:59:37,764 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration\n', '2018-12-10 23:59:37,764 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml\n', '2018-12-10 23:59:37,764 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration\n', '2018-12-10 23:59:37,765 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml\n', '2018-12-10 23:59:37,765 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration\n', '2018-12-10 23:59:37,765 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml\n', '2018-12-10 23:59:37,766 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration\n', '2018-12-10 23:59:37,766 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Retrieving vcenter configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/vcenterconfig.xml\n', '2018-12-10 23:59:37,767 INFO  [pool-20-thread-6]-dao.VcenterConfigDAOImpl: Successfully retrieved vcenter configuration\n', '2018-12-10 23:59:37,767 INFO  [pool-20-thread-6]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml\n', '2018-12-10 23:59:37,811 INFO  [pool-20-thread-6]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials\n', '2018-12-10 23:59:37,811 INFO  [pool-20-thread-6]-dao.ComponentCredentialsDAOImpl: Retrieving component credentials from file /usr/local/dataprotection/var/configmgr/server_data/config/componentCredentials.xml\n', '2018-12-10 23:59:37,856 INFO  [pool-20-thread-6]-dao.ComponentCredentialsDAOImpl: Successfully retrieved component credentials\n', '2018-12-10 23:59:37,856 INFO  [pool-20-thread-6]-vi.ViJavaServiceInstanceProvider: Creating service instance for host : *********\n', '2018-12-10 23:59:37,856 INFO  [pool-20-thread-6]-vi.ViJavaServiceInstanceProvider: Getting VI Java instance for *********\n', '2018-12-10 23:59:37,856 INFO  [pool-20-thread-6]-vi.ViJavaServiceInstanceProvider: ViSDK URL: https://*********:443/sdk\n', '2018-12-10 23:59:37,911 INFO  [pool-20-thread-6]-skuadapter.SKUSelectionService: Getting selected sku configuration.\n', '2018-12-10 23:59:37,911 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml\n', '2018-12-10 23:59:37,943 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration\n', '2018-12-10 23:59:37,943 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Received notifyStatus from task : AVAMAR:DEPLOY:AVAMAR_VAPP:0:IN_PROGRESS:2:20%:0:0\n', '2018-12-10 23:59:37,943 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Action progress percentage = 60 for action : AVAMAR:DEPLOY\n', '2018-12-10 23:59:37,943 INFO  [pool-20-thread-6]-configure.WorkflowManager: Received notifyStatus from action : AVAMAR:DEPLOY:IN_PROGRESS:0:60%:0:0\n', '2018-12-10 23:59:37,943 INFO  [pool-20-thread-6]-configure.WorkflowManager: Overall progress percentage = 20\n', '2018-12-10 23:59:37,943 INFO  [pool-20-thread-6]-configure.WorkflowManager: Warnings = 0\n', '2018-12-10 23:59:37,943 INFO  [pool-20-thread-6]-configure.WorkflowManager: Errors = 0\n', '2018-12-10 23:59:37,943 INFO  [pool-20-thread-6]-configure.WorkflowManager: Are there critical components failed = false\n', '2018-12-10 23:59:37,944 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Status notified from Workflow manager.\n', '2018-12-10 23:59:37,944 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Trying to save configuration status in file.\n', '2018-12-10 23:59:37,952 INFO  [pool-20-thread-6]-vi.ViJavaAccess: Single Host setup.Deploying vApp/VM on host: san-idpa1-esxin.dkengr.com\n', '2018-12-10 23:59:37,962 INFO  [pool-20-thread-6]-vi.ViJavaAccess: getVMFolder-->  VM Folder found. VM folder name:  vm\n', '2018-12-10 23:59:37,963 INFO  [pool-20-thread-6]-avadapter.DeployAvamarVAppTask: createVApp --> VM Folder vm\n', '2018-12-10 23:59:37,971 INFO  [pool-20-thread-6]-vi.ViJavaAccess: createVapp--> Virtual App successfully created.\n', '2018-12-10 23:59:37,971 INFO  [pool-20-thread-6]-avadapter.DeployAvamarVAppTask: createVApp -->  vApp create successfully. vApp Name Avamar\n', '2018-12-10 23:59:37,971 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Received notifyStatus from task : AVAMAR:DEPLOY:AVAMAR_VAPP:0:IN_PROGRESS:3:40%:0:0\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Action progress percentage = 70 for action : AVAMAR:DEPLOY\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-configure.WorkflowManager: Received notifyStatus from action : AVAMAR:DEPLOY:IN_PROGRESS:0:70%:0:0\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-configure.WorkflowManager: Overall progress percentage = 21\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-configure.WorkflowManager: Warnings = 0\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-configure.WorkflowManager: Errors = 0\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-configure.WorkflowManager: Are there critical components failed = false\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Status notified from Workflow manager.\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Trying to save configuration status in file.\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-skuadapter.SKUSelectionService: Getting selected sku configuration.\n', '2018-12-10 23:59:37,972 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml\n', '2018-12-10 23:59:38,004 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration\n', '2018-12-10 23:59:38,008 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> ++\n', '2018-12-10 23:59:38,008 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> ++\n', '2018-12-10 23:59:38,012 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> All Task Completed true\n', '2018-12-10 23:59:38,012 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> --\n', '2018-12-10 23:59:38,012 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> --\n', '2018-12-10 23:59:38,022 INFO  [pool-20-thread-6]-vi.ViJavaAccess: moveIntoVapp with MorType-->  Entity AVE moved sucvcessfully into Virtual App Avamar\n', '2018-12-10 23:59:38,022 INFO  [pool-20-thread-6]-avadapter.DeployAvamarVAppTask: moveVMsIntoVApp -->  All Avamar VM moved into Avamar vApp.\n', '2018-12-10 23:59:38,022 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Received notifyStatus from task : AVAMAR:DEPLOY:AVAMAR_VAPP:0:IN_PROGRESS:4:60%:0:0\n', '2018-12-10 23:59:38,022 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Action progress percentage = 80 for action : AVAMAR:DEPLOY\n', '2018-12-10 23:59:38,022 INFO  [pool-20-thread-6]-configure.WorkflowManager: Received notifyStatus from action : AVAMAR:DEPLOY:IN_PROGRESS:0:80%:0:0\n', '2018-12-10 23:59:38,022 INFO  [pool-20-thread-6]-configure.WorkflowManager: Overall progress percentage = 21\n', '2018-12-10 23:59:38,022 INFO  [pool-20-thread-6]-configure.WorkflowManager: Warnings = 0\n', '2018-12-10 23:59:38,023 INFO  [pool-20-thread-6]-configure.WorkflowManager: Errors = 0\n', '2018-12-10 23:59:38,023 INFO  [pool-20-thread-6]-configure.WorkflowManager: Are there critical components failed = false\n', '2018-12-10 23:59:38,023 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Status notified from Workflow manager.\n', '2018-12-10 23:59:38,023 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Trying to save configuration status in file.\n', '2018-12-10 23:59:38,023 INFO  [pool-20-thread-6]-skuadapter.SKUSelectionService: Getting selected sku configuration.\n', '2018-12-10 23:59:38,023 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Retriving selected sku configuration from file /usr/local/dataprotection/var/configmgr/server_data/skuconfig/selskuconfig.xml\n', '2018-12-10 23:59:38,055 INFO  [pool-20-thread-6]-dao.SelSKUConfigDAOImpl: Successfully retrieved selected sku configuration\n', '2018-12-10 23:59:38,060 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> ++\n', '2018-12-10 23:59:38,060 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> ++\n', '2018-12-10 23:59:38,065 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> All Task Completed true\n', '2018-12-10 23:59:38,065 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> --\n', '2018-12-10 23:59:38,065 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> --\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-vi.ViJavaAccess: configureVApp with Mor--> Virtual App configured sucvcessfully. vApp Name: Avamar\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-avadapter.DeployAvamarVAppTask: configureVApp -->  Avamar VM configured successfully.\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Received notifyStatus from task : AVAMAR:DEPLOY:AVAMAR_VAPP:0:IN_PROGRESS:5:70%:0:0\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Action progress percentage = 85 for action : AVAMAR:DEPLOY\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-configure.WorkflowManager: Received notifyStatus from action : AVAMAR:DEPLOY:IN_PROGRESS:0:85%:0:0\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-configure.WorkflowManager: Overall progress percentage = 21\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-configure.WorkflowManager: Warnings = 0\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-configure.WorkflowManager: Errors = 0\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-configure.WorkflowManager: Are there critical components failed = false\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Status notified from Workflow manager.\n', '2018-12-10 23:59:38,071 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Trying to save configuration status in file.\n', '2018-12-10 23:59:38,073 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> ++\n', '2018-12-10 23:59:38,073 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> ++\n', '2018-12-10 23:59:38,078 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> All Task Completed true\n', '2018-12-10 23:59:38,079 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> --\n', '2018-12-10 23:59:38,079 INFO  [pool-20-thread-6]-vi.ViJavaAccess: waitToFinishAllTask(ManagedEntity managedEntity, int countToWait) --> --\n', '2018-12-11 00:01:39,779 INFO  [pool-20-thread-6]-configure.ConfigInfoHandler: Getting Avamar configuration.\n', '2018-12-11 00:01:39,779 INFO  [pool-20-thread-6]-dao.AvamarConfigDAOImpl: Retrieving avamar configuration from file /usr/local/dataprotection/var/configmgr/server_data/config/avamarconfig.xml\n', '2018-12-11 00:01:39,799 INFO  [pool-20-thread-6]-dao.AvamarConfigDAOImpl: Successfully retrieved avamar configuration\n', '2018-12-11 00:01:39,799 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Checking if AVE is up.\n', '2018-12-11 00:01:53,421 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> isConfigPackageReady command output: \n', '2018-12-11 00:01:53,421 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:01:53,421 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Execution of isConfigPackageReady task is completed.\n', '2018-12-11 00:01:53,421 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Avamar Server: false\n', '2018-12-11 00:01:53,421 WARN  [pool-20-thread-6]-avadapter.AvamarUtil: validateVmIsUp --> config package is not yet ready 0 sec.\n', '2018-12-11 00:02:23,421 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Checking if AVE is up.\n', '2018-12-11 00:02:26,675 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> isConfigPackageReady command output: Title:                    |   Version: |    Status: | Description:\n', '2018-12-11 00:02:26,675 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:02:26,675 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Execution of isConfigPackageReady task is completed.\n', '2018-12-11 00:02:26,675 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Avamar Server: false\n', '2018-12-11 00:02:26,675 WARN  [pool-20-thread-6]-avadapter.AvamarUtil: validateVmIsUp --> config package is not yet ready 30 sec.\n', '2018-12-11 00:02:56,676 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Checking if AVE is up.\n', '2018-12-11 00:02:57,530 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> isConfigPackageReady command output: Title:                    |   Version: |    Status: | Description:\n', '2018-12-11 00:02:57,530 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:02:57,530 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Execution of isConfigPackageReady task is completed.\n', '2018-12-11 00:02:57,530 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Avamar Server: false\n', '2018-12-11 00:02:57,530 WARN  [pool-20-thread-6]-avadapter.AvamarUtil: validateVmIsUp --> config package is not yet ready 60 sec.\n', '2018-12-11 00:03:27,530 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Checking if AVE is up.\n', '2018-12-11 00:03:28,026 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> isConfigPackageReady command output: \n', '2018-12-11 00:03:28,026 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:03:28,026 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Execution of isConfigPackageReady task is completed.\n', '2018-12-11 00:03:28,026 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Avamar Server: false\n', '2018-12-11 00:03:28,026 WARN  [pool-20-thread-6]-avadapter.AvamarUtil: validateVmIsUp --> config package is not yet ready 90 sec.\n', '2018-12-11 00:03:58,026 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Checking if AVE is up.\n', '2018-12-11 00:04:00,368 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> isConfigPackageReady command output: Title:                    |   Version: |    Status: | Description:\n', '2018-12-11 00:04:00,368 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:04:00,368 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Execution of isConfigPackageReady task is completed.\n', '2018-12-11 00:04:00,368 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Avamar Server: false\n', '2018-12-11 00:04:00,368 WARN  [pool-20-thread-6]-avadapter.AvamarUtil: validateVmIsUp --> config package is not yet ready 120 sec.\n', '2018-12-11 00:04:30,368 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Checking if AVE is up.\n', '2018-12-11 00:04:31,290 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> isConfigPackageReady command output: Title:                    |   Version: |    Status: | Description:\n', '2018-12-11 00:04:31,290 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:04:31,290 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Execution of isConfigPackageReady task is completed.\n', '2018-12-11 00:04:31,290 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Avamar Server: false\n', '2018-12-11 00:04:31,290 WARN  [pool-20-thread-6]-avadapter.AvamarUtil: validateVmIsUp --> config package is not yet ready 150 sec.\n', '2018-12-11 00:05:01,290 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Checking if AVE is up.\n', '2018-12-11 00:05:02,159 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> isConfigPackageReady command output: Title:                    |   Version: |    Status: | Description:\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady succeeded for IP: *********\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Execution of isConfigPackageReady task is completed.\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: Avamar Server: true\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-avadapter.AvamarUtil: validateVmIsUp --> All VMs are pingable.\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Received notifyStatus from task : AVAMAR:DEPLOY:AVAMAR_VAPP:0:COMPLETED:6:100%:0:0\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Action progress percentage = 100 for action : AVAMAR:DEPLOY\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: All tasks completed either successfully or with failure for action :AVAMAR:DEPLOY\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: Setting config state as completed for action :AVAMAR:DEPLOY\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-configure.WorkflowManager: Received notifyStatus from action : AVAMAR:DEPLOY:COMPLETED:0:100%:0:0\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-configure.WorkflowManager: Overall progress percentage = 24\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-configure.WorkflowManager: Warnings = 0\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-configure.WorkflowManager: Errors = 0\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-configure.WorkflowManager: Are there critical components failed = false\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : IDPA_VAPP, ActionId : DEPLOY, ReadyToExecute : false\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : IDPA_VAPP, ActionId : INTEGRATE, ReadyToExecute : false\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : AVAMAR, ActionId : CONFIG, ReadyToExecute : true\n', '2018-12-11 00:05:02,160 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: ProductId : AVAMAR, ActionId : CONFIG, ComponentId : AVAMAR_SERVER, ComponentInstanceId : 0, ReadyToExecute : true\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: ProductId : AVAMAR, ActionId : CONFIG, ComponentId : AVAMAR_PROXY, ComponentInstanceId : 0, ReadyToExecute : false\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : AVAMAR, ActionId : INTEGRATE, ReadyToExecute : false\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_ADVISOR, ActionId : DEPLOY, ReadyToExecute : false\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_ADVISOR, ActionId : CONFIG, ReadyToExecute : false\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_ADVISOR, ActionId : INTEGRATE, ReadyToExecute : false\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_SEARCH, ActionId : DEPLOY, ReadyToExecute : false\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_SEARCH, ActionId : CONFIG, ReadyToExecute : false\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_SEARCH, ActionId : INTEGRATE, ReadyToExecute : false\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_CENTRAL, ActionId : DEPLOY, ReadyToExecute : true\n', '2018-12-11 00:05:02,161 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: ProductId : DATA_PROTECTION_CENTRAL, ActionId : DEPLOY, ComponentId : DPC_SERVER, ComponentInstanceId : 0, ReadyToExecute : true\n', '2018-12-11 00:05:02,162 INFO  [pool-20-thread-6]-abstracts.ProductPlugin: ProductId : DATA_PROTECTION_CENTRAL, ActionId : DEPLOY, ComponentId : DPC_VAPP, ComponentInstanceId : 0, ReadyToExecute : false\n', '2018-12-11 00:05:02,162 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_CENTRAL, ActionId : CONFIG, ReadyToExecute : false\n', '2018-12-11 00:05:02,162 INFO  [pool-20-thread-6]-configure.WorkflowManager: ProductId : DATA_PROTECTION_CENTRAL, ActionId : INTEGRATE, ReadyToExecute : false\n', '2018-12-11 00:05:02,162 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Status notified from Workflow manager.\n', '2018-12-11 00:05:02,162 INFO  [pool-20-thread-6]-configure.ConfigStatusCollector: Trying to save configuration status in file.\n']
	
content = ['2018-12-11 00:01:53,421 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:02:26,675 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:02:57,530 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:03:28,026 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:04:00,368 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n', '2018-12-11 00:04:31,290 ERROR [pool-20-thread-6]-avadapter.AvamarUtil: isConfigPackageReady --> Avamar isConfigPackageReady failed for IP: *********\n','2018-12-11 00:23:11,631 ERROR [pool-20-thread-8]-avadapter.AvamarUtil: updateNTPSettingsOnAvamarServer->Start\n', '2018-12-11 00:23:18,909 ERROR [pool-20-thread-8]-avadapter.AvamarUtil: updateNTPSettingsOnAvamarServer->End\n','2018-12-10 23:45:29,446 ERROR [pool-20-thread-1]-vi.ViJavaServiceInstanceProvider: Error while creating ViJava service instance. java.rmi.RemoteException: VI SDK invoke exception:java.net.ConnectException: Connection refused (Connection refused)\n', '2018-12-10 23:45:29,446 ERROR [pool-20-thread-1]-vi.ViJavaServiceInstanceProvider: Exception occurred while creating ViJava service instance with persisted vCenter parameters. com.emc.vcedpa.common.exception.ApplianceException: Failed to get ESXi instance\n', '2018-12-10 23:47:29,636 ERROR [pool-20-thread-1]-vi.ViJavaServiceInstanceProvider: Exception occurred while creating ViJava service instance with persisted vCenter parameters. java.lang.NullPointerException\n', '2018-12-10 23:49:29,825 ERROR [pool-20-thread-1]-vi.ViJavaServiceInstanceProvider: Exception occurred while creating ViJava service instance with persisted vCenter parameters. java.lang.NullPointerException\n',"2018-12-11 00:04:26,264 ERROR [pool-20-thread-7]-util.SSHUtil:  Error in command execution Expect operation fails (timeout: 30000 ms) for matcher: contains('Enter new passphrase')\n", '2018-12-11 00:04:26,266 ERROR [pool-20-thread-7]-util.SSHUtil: Failed to executed remote command using SSH.\n', "2018-12-11 00:06:13,084 ERROR [pool-20-thread-7]-util.SSHUtil:  Error in command execution Expect operation fails (timeout: 30000 ms) for matcher: contains('Do you want to enable encryption?')\n", "2018-12-11 00:11:57,172 ERROR [pool-20-thread-7]-util.SSHUtil:  Error in command execution Expect operation fails (timeout: 30000 ms) for matcher: contains('  Are you sure?')\n", '2018-12-11 00:12:10,349 ERROR [pool-20-thread-7]-util.SSHUtil: Failed to executed remote command using SSH.\n', '2018-12-11 00:12:10,349 ERROR [pool-20-thread-7]-ddadapter.ConfigDataDomainTask: ApplianceException occurred while executing Datadomain config task. \n']

def add_info_to_db(db, logfile, errorlist):
    date = datetime.datetime.now()
    master_db = sqlite3.connect('masterdb_' + str(db)+'.db')
    master_db.isolation_level = None	 
    master_db.execute('PRAGMA foreign_keys = ON;')		
    master_db.text_factory = str	  
    master_db.execute('CREATE TABLE IF NOT EXISTS MISSED ("LOGFILE"	TEXT NOT NULL, "TS" TEXT NOT NULL, "ERROR" TEXT NOT NULL UNIQUE, PRIMARY KEY("ERROR"))')
    cursor_master_db = master_db.cursor() 
    for el in errorlist:  
        line = re.search(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [A-Z]+\s+\[[^\]]+\]-([a-zA-Z]+.[a-zA-Z]+: .+)', el).group(1)    
        cursor_master_db.execute('INSERT OR IGNORE INTO MISSED (LOGFILE, TS, ERROR) VALUES ("{}","{}", "{}")'.format(logfile, date, line))
    master_db.commit()	
    master_db.close()    

def remove_invalid_error_threads(threadname):
    newerrorlog = [re.search(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} [A-Z]+\s+\[[^\]]+\](-[a-zA-Z]+.[a-zA-Z]+: .+)', line).group(1) for line in content] #removed datetime/status/threadname
    matchfound = [errline.strip() for errstring, logstring in stringdict.iteritems() for errline in newerrorlog if errstring in errline.strip() for logline in mainlog if logstring in logline.strip()]
    print 'Total errors found : ' + str(len(content))
    print 'Total match found : ' + str(len(matchfound))
    #print list(set(matchfound))    
    if len(content) == len(matchfound):
        print 'All errors were matched as per our Database, therefore, whole thread will be ignored'
        return True, []
    else:
        print 'Still have ' + str(len(content) - len(matchfound)) + ' errors left to be discovered'
        #print 'Adding those errors to database for later review'        
        matchfound = list(set(matchfound)) #removing duplicate entries and reusing same list    
        #print 'Removing elements : ' + str(matchfound)
        nonmatchedremainingerrors = [line for err in matchfound for line in content if err not in line.strip()]      
        #print 'Original Content : ' + str(content)
        #print '---------------------------------'
        #print 'New Content : ' + str(nonmatchedremainingerrors)
        logfile = 'abc.log'
        dbcontent = str(nonmatchedremainingerrors)
        add_info_to_db(1, logfile , nonmatchedremainingerrors)   
        try:        
            add_info_to_db(1, logfile , nonmatchedremainingerrors, len(nonmatchedremainingerrors))    
        except:
            try:
                add_info_to_db(2, logfile, nonmatchedremainingerrors, len(nonmatchedremainingerrors))        
            except:
                try:
                    add_info_to_db(3, logfile, nonmatchedremainingerrors, len(nonmatchedremainingerrors))                
                except:
                    try:
                        add_info_to_db(4, logfile, nonmatchedremainingerrors, len(nonmatchedremainingerrors))                    
                    except:
                        pass                    
        return False, nonmatchedremainingerrors

    
ret, errcontent = remove_invalid_error_threads(1)
if ret:
    print 'All errors matched and therefore, this thread is done'
else:
    print 'Process thread'