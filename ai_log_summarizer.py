"""
AI-Powered Log Summarization Module for IDPA Log Analyzer
This module creates executive summaries and intelligent insights from log analysis
"""

import openai
import json
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class LogSummary:
    """Data class for log analysis summary"""
    executive_summary: str
    key_findings: List[str]
    risk_assessment: str
    critical_issues: List[Dict]
    recommended_actions: List[Dict]
    system_health_score: float
    trends_analysis: str
    next_steps: List[str]

@dataclass
class ExecutiveDashboard:
    """Data class for executive dashboard data"""
    overall_status: str  # Healthy, Warning, Critical
    summary_text: str
    key_metrics: Dict
    priority_issues: List[Dict]
    recommendations: List[str]
    risk_level: str

class AILogSummarizer:
    """AI-powered log summarizer that creates executive insights"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        """
        Initialize the AI Log Summarizer
        
        Args:
            api_key: OpenAI API key
            model: Model to use
        """
        openai.api_key = api_key
        self.model = model
        self.logger = logging.getLogger(__name__)
        
        # Risk assessment criteria
        self.risk_criteria = {
            'critical': ['authentication failure', 'data corruption', 'backup failure', 'system crash'],
            'high': ['network timeout', 'storage full', 'service unavailable', 'configuration error'],
            'medium': ['performance degradation', 'warning message', 'retry attempt'],
            'low': ['informational', 'debug message', 'routine operation']
        }
    
    def create_executive_summary(self, analysis_results: Dict) -> LogSummary:
        """
        Create an executive summary from log analysis results
        
        Args:
            analysis_results: Complete analysis results from mainanalyzer.py
            
        Returns:
            LogSummary object with executive insights
        """
        try:
            # Extract key data from analysis results
            summary_data = self._extract_summary_data(analysis_results)
            
            prompt = self._create_summary_prompt(summary_data)
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_summary_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1500
            )
            
            ai_response = response.choices[0].message.content
            return self._parse_summary_response(ai_response, summary_data)
            
        except Exception as e:
            self.logger.error(f"Executive summary generation failed: {str(e)}")
            return self._create_fallback_summary(analysis_results)
    
    def create_executive_dashboard(self, analysis_results: Dict) -> ExecutiveDashboard:
        """
        Create executive dashboard data
        
        Args:
            analysis_results: Complete analysis results
            
        Returns:
            ExecutiveDashboard object
        """
        try:
            # Calculate overall system health
            health_score = self._calculate_health_score(analysis_results)
            overall_status = self._determine_overall_status(health_score, analysis_results)
            
            # Extract priority issues
            priority_issues = self._extract_priority_issues(analysis_results)
            
            prompt = f"""
            Create an executive dashboard summary for this IDPA log analysis:
            
            System Health Score: {health_score}/100
            Overall Status: {overall_status}
            Total Errors: {analysis_results.get('total_errors_count', 0)}
            Errors Resolved: {analysis_results.get('total_errors_removed_or_eliminated', 0)}
            Critical Threads: {len([t for t in analysis_results.get('allthreads_list', []) if int(t[1]) > 10])}
            
            Priority Issues:
            {json.dumps(priority_issues, indent=2)}
            
            Provide executive dashboard in JSON format:
            {{
                "summary_text": "2-3 sentence executive summary",
                "key_metrics": {{
                    "availability": "99.5%",
                    "performance": "Good",
                    "security": "Secure"
                }},
                "recommendations": ["Recommendation 1", "Recommendation 2"]
            }}
            """
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a CTO creating executive dashboards for IT infrastructure."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=800
            )
            
            dashboard_data = json.loads(response.choices[0].message.content)
            
            return ExecutiveDashboard(
                overall_status=overall_status,
                summary_text=dashboard_data.get('summary_text', 'System analysis completed'),
                key_metrics=dashboard_data.get('key_metrics', {}),
                priority_issues=priority_issues,
                recommendations=dashboard_data.get('recommendations', []),
                risk_level=self._assess_risk_level(analysis_results)
            )
            
        except Exception as e:
            self.logger.error(f"Dashboard creation failed: {str(e)}")
            return self._create_fallback_dashboard(analysis_results)
    
    def generate_trend_analysis(self, historical_data: List[Dict]) -> Dict:
        """
        Generate trend analysis from historical log data
        
        Args:
            historical_data: List of historical analysis results
            
        Returns:
            Trend analysis insights
        """
        try:
            if len(historical_data) < 2:
                return {"message": "Insufficient historical data for trend analysis"}
            
            # Prepare trend data
            trend_summary = self._prepare_trend_data(historical_data)
            
            prompt = f"""
            Analyze trends in IDPA system health over time:
            
            Historical Data Summary:
            {json.dumps(trend_summary, indent=2)}
            
            Identify:
            1. Improving trends
            2. Deteriorating patterns
            3. Recurring issues
            4. Seasonal patterns
            5. Predictive insights
            6. Recommended monitoring focus areas
            
            Format as JSON with clear trend analysis.
            """
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a data analyst specializing in IT infrastructure trends."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1200
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            self.logger.error(f"Trend analysis failed: {str(e)}")
            return {"error": "Trend analysis unavailable"}
    
    def create_incident_report(self, analysis_results: Dict, incident_context: Dict = None) -> Dict:
        """
        Create a formal incident report from log analysis
        
        Args:
            analysis_results: Log analysis results
            incident_context: Additional incident context
            
        Returns:
            Structured incident report
        """
        try:
            critical_errors = self._extract_critical_errors(analysis_results)
            
            prompt = f"""
            Create a formal incident report for this IDPA system analysis:
            
            Analysis Summary:
            - Total Errors: {analysis_results.get('total_errors_count', 0)}
            - Critical Issues: {len(critical_errors)}
            - System Components Affected: {self._get_affected_components(analysis_results)}
            - Analysis Duration: {analysis_results.get('log_duration', 'Unknown')}
            
            Critical Errors:
            {json.dumps(critical_errors[:5], indent=2)}
            
            Create incident report with:
            1. Incident Summary
            2. Impact Assessment
            3. Root Cause Analysis
            4. Timeline of Events
            5. Resolution Actions Taken
            6. Lessons Learned
            7. Prevention Measures
            
            Format as structured JSON report.
            """
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an IT incident manager creating formal incident reports."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            self.logger.error(f"Incident report creation failed: {str(e)}")
            return {"error": "Incident report generation failed"}
    
    def _extract_summary_data(self, analysis_results: Dict) -> Dict:
        """Extract key data for summary generation"""
        threads = analysis_results.get('allthreads_list', [])
        
        return {
            'total_errors': analysis_results.get('total_errors_count', 0),
            'errors_resolved': analysis_results.get('total_errors_removed_or_eliminated', 0),
            'errors_remaining': analysis_results.get('total_errors_left', 0),
            'reduction_percentage': analysis_results.get('percentage_reduction', '0%'),
            'total_threads': len(threads),
            'critical_threads': len([t for t in threads if int(t[1]) > 10]),
            'components_affected': list(set([t[7] for t in threads if t[7] != 'Others'])),
            'log_duration': analysis_results.get('logage', 'Unknown'),
            'analysis_time': analysis_results.get('difftime', 'Unknown'),
            'top_errors': analysis_results.get('main_errors_stack', [])[:5]
        }
    
    def _create_summary_prompt(self, summary_data: Dict) -> str:
        """Create prompt for executive summary"""
        return f"""
        Create an executive summary for this IDPA log analysis:
        
        System Analysis Results:
        - Total Errors Detected: {summary_data['total_errors']}
        - Errors Automatically Resolved: {summary_data['errors_resolved']}
        - Errors Requiring Attention: {summary_data['errors_remaining']}
        - Error Reduction Achieved: {summary_data['reduction_percentage']}
        - Analysis Duration: {summary_data['analysis_time']} seconds
        - Log Time Span: {summary_data['log_duration']}
        - Total Error Threads: {summary_data['total_threads']}
        - Critical Threads (>10 errors): {summary_data['critical_threads']}
        - Components Affected: {', '.join(summary_data['components_affected'])}
        
        Top Error Messages:
        {chr(10).join([f"- {error}" for error in summary_data['top_errors']])}
        
        Provide analysis in JSON format:
        {{
            "executive_summary": "2-3 sentence high-level summary for executives",
            "key_findings": ["Finding 1", "Finding 2", "Finding 3"],
            "risk_assessment": "Overall risk level and explanation",
            "critical_issues": [
                {{"issue": "Issue description", "impact": "Business impact", "urgency": "High/Medium/Low"}}
            ],
            "recommended_actions": [
                {{"action": "Action description", "priority": "High/Medium/Low", "timeline": "Immediate/Short-term/Long-term"}}
            ],
            "system_health_score": 85.5,
            "trends_analysis": "Analysis of patterns and trends observed",
            "next_steps": ["Next step 1", "Next step 2"]
        }}
        """
    
    def _get_summary_system_prompt(self) -> str:
        """System prompt for summary generation"""
        return """
        You are a senior IT executive and IDPA subject matter expert creating executive summaries.
        Your audience includes CIOs, CTOs, and senior management who need:
        
        1. Clear, non-technical language
        2. Business impact focus
        3. Risk-based prioritization
        4. Actionable recommendations
        5. Strategic insights
        
        Focus on:
        - System reliability and availability
        - Data protection effectiveness
        - Operational efficiency
        - Risk mitigation
        - Resource optimization
        
        Avoid technical jargon and focus on business outcomes.
        """
    
    def _parse_summary_response(self, ai_response: str, summary_data: Dict) -> LogSummary:
        """Parse AI response into LogSummary object"""
        try:
            if ai_response.strip().startswith('{'):
                data = json.loads(ai_response)
                return LogSummary(
                    executive_summary=data.get('executive_summary', 'System analysis completed'),
                    key_findings=data.get('key_findings', []),
                    risk_assessment=data.get('risk_assessment', 'Risk assessment unavailable'),
                    critical_issues=data.get('critical_issues', []),
                    recommended_actions=data.get('recommended_actions', []),
                    system_health_score=data.get('system_health_score', 75.0),
                    trends_analysis=data.get('trends_analysis', 'Trend analysis unavailable'),
                    next_steps=data.get('next_steps', [])
                )
            else:
                return self._parse_freeform_summary(ai_response, summary_data)
                
        except Exception as e:
            self.logger.error(f"Failed to parse summary response: {str(e)}")
            return self._create_fallback_summary({'summary_data': summary_data})
    
    def _parse_freeform_summary(self, response: str, summary_data: Dict) -> LogSummary:
        """Parse free-form summary response"""
        return LogSummary(
            executive_summary=response[:300] + "..." if len(response) > 300 else response,
            key_findings=["AI-generated analysis available"],
            risk_assessment="Manual review recommended",
            critical_issues=[],
            recommended_actions=[{"action": "Review AI analysis", "priority": "Medium", "timeline": "Short-term"}],
            system_health_score=70.0,
            trends_analysis="Detailed analysis in AI response",
            next_steps=["Review complete AI analysis"]
        )
    
    def _create_fallback_summary(self, analysis_results: Dict) -> LogSummary:
        """Create fallback summary when AI fails"""
        summary_data = analysis_results.get('summary_data', {})
        
        return LogSummary(
            executive_summary="IDPA log analysis completed. Manual review of results recommended due to AI analysis unavailability.",
            key_findings=[
                f"Total errors detected: {summary_data.get('total_errors', 'Unknown')}",
                f"Error reduction achieved: {summary_data.get('reduction_percentage', 'Unknown')}",
                "Detailed analysis available in system reports"
            ],
            risk_assessment="Manual risk assessment required - AI analysis unavailable",
            critical_issues=[{"issue": "AI analysis failure", "impact": "Reduced insight quality", "urgency": "Medium"}],
            recommended_actions=[
                {"action": "Review detailed error logs", "priority": "High", "timeline": "Immediate"},
                {"action": "Investigate AI analysis failure", "priority": "Medium", "timeline": "Short-term"}
            ],
            system_health_score=50.0,  # Conservative score when AI unavailable
            trends_analysis="Trend analysis unavailable - manual review required",
            next_steps=[
                "Review detailed thread analysis",
                "Investigate critical errors manually",
                "Restore AI analysis capability"
            ]
        )
    
    def _calculate_health_score(self, analysis_results: Dict) -> float:
        """Calculate overall system health score"""
        try:
            total_errors = int(analysis_results.get('total_errors_count', 0))
            errors_resolved = int(analysis_results.get('total_errors_removed_or_eliminated', 0))
            
            if total_errors == 0:
                return 100.0
            
            resolution_rate = (errors_resolved / total_errors) * 100
            
            # Adjust score based on critical issues
            threads = analysis_results.get('allthreads_list', [])
            critical_threads = len([t for t in threads if int(t[1]) > 10])
            
            # Penalty for critical threads
            critical_penalty = min(critical_threads * 5, 30)
            
            health_score = max(resolution_rate - critical_penalty, 0)
            return min(health_score, 100.0)
            
        except Exception:
            return 50.0  # Default score when calculation fails
    
    def _determine_overall_status(self, health_score: float, analysis_results: Dict) -> str:
        """Determine overall system status"""
        if health_score >= 90:
            return "Healthy"
        elif health_score >= 70:
            return "Warning"
        else:
            return "Critical"
    
    def _extract_priority_issues(self, analysis_results: Dict) -> List[Dict]:
        """Extract priority issues from analysis results"""
        issues = []
        threads = analysis_results.get('allthreads_list', [])
        
        # Find threads with high error counts
        for thread in threads:
            error_count = int(thread[1])
            if error_count > 5:
                issues.append({
                    "thread": thread[0],
                    "error_count": error_count,
                    "component": thread[7],
                    "priority": "High" if error_count > 10 else "Medium"
                })
        
        return sorted(issues, key=lambda x: x['error_count'], reverse=True)[:5]
    
    def _assess_risk_level(self, analysis_results: Dict) -> str:
        """Assess overall risk level"""
        health_score = self._calculate_health_score(analysis_results)
        
        if health_score >= 85:
            return "Low"
        elif health_score >= 70:
            return "Medium"
        elif health_score >= 50:
            return "High"
        else:
            return "Critical"
    
    def _create_fallback_dashboard(self, analysis_results: Dict) -> ExecutiveDashboard:
        """Create fallback dashboard when AI fails"""
        health_score = self._calculate_health_score(analysis_results)
        
        return ExecutiveDashboard(
            overall_status=self._determine_overall_status(health_score, analysis_results),
            summary_text="System analysis completed. AI-enhanced insights unavailable.",
            key_metrics={
                "health_score": f"{health_score:.1f}/100",
                "total_errors": analysis_results.get('total_errors_count', 0),
                "resolution_rate": analysis_results.get('percentage_reduction', '0%')
            },
            priority_issues=self._extract_priority_issues(analysis_results),
            recommendations=["Review detailed analysis results", "Investigate high-error threads"],
            risk_level=self._assess_risk_level(analysis_results)
        )
    
    def _prepare_trend_data(self, historical_data: List[Dict]) -> Dict:
        """Prepare historical data for trend analysis"""
        trend_data = {
            "time_periods": len(historical_data),
            "error_trends": [],
            "resolution_trends": [],
            "component_trends": {}
        }
        
        for data in historical_data:
            trend_data["error_trends"].append(int(data.get('total_errors_count', 0)))
            trend_data["resolution_trends"].append(float(data.get('percentage_reduction', '0').replace('%', '')))
        
        return trend_data
    
    def _extract_critical_errors(self, analysis_results: Dict) -> List[Dict]:
        """Extract critical errors for incident reporting"""
        critical_errors = []
        threads = analysis_results.get('allthreads_list', [])
        
        for thread in threads:
            if int(thread[1]) > 10:  # High error count threshold
                critical_errors.append({
                    "thread": thread[0],
                    "error_count": thread[1],
                    "component": thread[7],
                    "start_time": thread[3],
                    "end_time": thread[4]
                })
        
        return critical_errors
    
    def _get_affected_components(self, analysis_results: Dict) -> List[str]:
        """Get list of affected components"""
        threads = analysis_results.get('allthreads_list', [])
        components = list(set([t[7] for t in threads if t[7] != 'Others']))
        return components
