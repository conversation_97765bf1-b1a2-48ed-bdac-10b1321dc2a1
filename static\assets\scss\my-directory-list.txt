The file socials.css contains:
.social {
  color: #fff; }
  .social.btn-sm {
    padding: 0 10px 0 0;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0; }
  .social.btn-lg {
    border-radius: 0; }
  .social.btn {
    border-radius: 0; }
  .social i {
    width: 1.8125rem;
    line-height: 2; }
  .social.facebook {
    background: #3b5998; }
    .social.facebook:hover {
      background: #344e86; }
  .social [class*="-facebook"] {
    background: #2d4373; }
  .social.twitter {
    background: #00aced; }
    .social.twitter:hover {
      background: #0099d4; }
  .social [class*="-twitter"] {
    background: #0087ba; }
  .social.linkedin {
    background: #4875b4; }
    .social.linkedin:hover {
      background: #4169a2; }
  .social [class*="-linkedin"] {
    background: #395d90; }
  .social.flickr {
    background: #ff0084; }
    .social.flickr:hover {
      background: #e60077; }
  .social [class*="-flickr"] {
    background: #cc006a; }
  .social.tumblr {
    background: #32506d; }
    .social.tumblr:hover {
      background: #2a435c; }
  .social [class*="-tumblr"] {
    background: #22364a; }
  .social.xing {
    background: #026466; }
    .social.xing:hover {
      background: #024b4d; }
  .social [class*="-xing"] {
    background: #013334; }
  .social.github {
    background: #4183c4; }
    .social.github:hover {
      background: #3876b4; }
  .social [class*="-github"] {
    background: #3269a0; }
  .social.html5 {
    background: #e34f26; }
    .social.html5:hover {
      background: #d4431b; }
  .social [class*="-html5"] {
    background: #be3c18; }
  .social.openid {
    background: #f78c40; }
    .social.openid:hover {
      background: #f67d28; }
  .social [class*="-openid"] {
    background: #f56f0f; }
  .social.stack-overflow {
    background: #fe7a15; }
    .social.stack-overflow:hover {
      background: #f86c01; }
  .social [class*="-stack-overflow"] {
    background: #df6101; }
  .social.css3 {
    background: #0170ba; }
    .social.css3:hover {
      background: #0161a1; }
  .social [class*="-css3"] {
    background: #015187; }
  .social.youtube {
    background: #b00; }
    .social.youtube:hover {
      background: #a20000; }
  .social [class*="-youtube"] {
    background: #880000; }
  .social.dribbble {
    background: #ea4c89; }
    .social.dribbble:hover {
      background: #e7357a; }
  .social [class*="-dribbble"] {
    background: #e51e6b; }
  .social.dropbox {
    background: #007ee5; }
    .social.dropbox:hover {
      background: #0070cc; }
  .social [class*="-dropbox"] {
    background: #0070cc; }
  .social.google-plus {
    background: #d34836; }
    .social.google-plus:hover {
      background: #c43d2b; }
  .social [class*="-google-plus"] {
    background: #b03626; }
  .social.instagram {
    background: #517fa4; }
    .social.instagram:hover {
      background: #497293; }
  .social [class*="-instagram"] {
    background: #406582; }
  .social.pinterest {
    background: #cb2027; }
    .social.pinterest:hover {
      background: #b51d23; }
  .social [class*="-pinterest"] {
    background: #9f191f; }
  .social.vk {
    background: #45668e; }
    .social.vk:hover {
      background: #3d5a7d; }
  .social [class*="-vk"] {
    background: #344d6c; }
  .social.yahoo {
    background: #400191; }
    .social.yahoo:hover {
      background: #350178; }
  .social [class*="-yahoo"] {
    background: #2a015e; }
  .social.behance {
    background: #1769ff; }
    .social.behance:hover {
      background: #0059fd; }
  .social [class*="-behance"] {
    background: #0050e3; }
  .social.reddit {
    background: #ff4500; }
    .social.reddit:hover {
      background: #e63e00; }
  .social [class*="-reddit"] {
    background: #cc3700; }
  .social.spotify {
    background: #7ab800; }
    .social.spotify:hover {
      background: #699f00; }
  .social [class*="-spotify"] {
    background: #588500; }
  .social.vine {
    background: #00bf8f; }
    .social.vine:hover {
      background: #00a67c; }
  .social [class*="-vine"] {
    background: #008c69; }
  .social.foursquare {
    background: #1073af; }
    .social.foursquare:hover {
      background: #0e6498; }
  .social [class*="-foursquare"] {
    background: #0c5480; }
  .social.vimeo {
    background: #aad450; }
    .social.vimeo:hover {
      background: #a0cf3c; }
  .social [class*="-vimeo"] {
    background: #93c130; }

The file socials.css.map contains:
{
"version": 3,
"mappings": "AAAI,OAAO;EACH,KAAK,EAAE,IAAI;EACT,cAAS;IACP,OAAO,EAAE,UAAU;IACnB,SAAS,EAAE,QAAQ;IACnB,WAAW,EAAE,GAAG;IAChB,aAAa,EAAE,CAAC;EAElB,cAAS;IAEP,aAAa,EAAE,CAAC;EAElB,WAAK;IACH,aAAa,EAAE,CAAC;EAElB,SAAC;IACG,KAAK,EAAE,SAAS;IAChB,WAAW,EAAE,CAAC;EAGlB,gBAAU;IACR,UAAU,EAAE,OAAO;IACnB,sBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,4BAAqB;IACnB,UAAU,EAAE,OAAO;EAGrB,eAAS;IACP,UAAU,EAAE,OAAO;IACnB,qBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,2BAAoB;IAClB,UAAU,EAAE,OAAO;EAGrB,gBAAU;IACR,UAAU,EAAE,OAAO;IACnB,sBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,4BAAqB;IACnB,UAAU,EAAE,OAAO;EAGrB,cAAQ;IACN,UAAU,EAAE,OAAO;IACnB,oBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,0BAAmB;IACjB,UAAU,EAAE,OAAO;EAGrB,cAAQ;IACN,UAAU,EAAE,OAAO;IACnB,oBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,0BAAmB;IACjB,UAAU,EAAE,OAAO;EAGrB,YAAM;IACJ,UAAU,EAAE,OAAO;IACnB,kBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,wBAAiB;IACf,UAAU,EAAE,OAAO;EAGrB,cAAQ;IACN,UAAU,EAAE,OAAO;IACnB,oBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,0BAAmB;IACjB,UAAU,EAAE,OAAO;EAGrB,aAAO;IACL,UAAU,EAAE,OAAO;IACnB,mBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,yBAAkB;IAChB,UAAU,EAAE,OAAO;EAGrB,cAAQ;IACN,UAAU,EAAE,OAAO;IACnB,oBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,0BAAmB;IACjB,UAAU,EAAE,OAAO;EAGrB,sBAAgB;IACd,UAAU,EAAE,OAAO;IACnB,4BAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,kCAA2B;IACzB,UAAU,EAAE,OAAO;EAGrB,YAAM;IACJ,UAAU,EAAE,OAAO;IACnB,kBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,wBAAiB;IACf,UAAU,EAAE,OAAO;EAGrB,eAAS;IACP,UAAU,EAAE,IAAI;IAChB,qBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,2BAAoB;IAClB,UAAU,EAAE,OAAO;EAGrB,gBAAU;IACR,UAAU,EAAE,OAAO;IACnB,sBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,4BAAqB;IACnB,UAAU,EAAE,OAAO;EAGrB,eAAS;IACP,UAAU,EAAE,OAAO;IACnB,qBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,2BAAoB;IAClB,UAAU,EAAE,OAAO;EAGrB,mBAAa;IACX,UAAU,EAAE,OAAO;IACnB,yBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,+BAAwB;IACtB,UAAU,EAAE,OAAO;EAGrB,iBAAW;IACT,UAAU,EAAE,OAAO;IACnB,uBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,6BAAsB;IACpB,UAAU,EAAE,OAAO;EAGrB,iBAAW;IACT,UAAU,EAAE,OAAO;IACnB,uBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,6BAAsB;IACpB,UAAU,EAAE,OAAO;EAGrB,UAAI;IACF,UAAU,EAAE,OAAO;IACnB,gBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,sBAAe;IACb,UAAU,EAAE,OAAO;EAGrB,aAAO;IACL,UAAU,EAAE,OAAO;IACnB,mBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,yBAAkB;IAChB,UAAU,EAAE,OAAO;EAGrB,eAAS;IACP,UAAU,EAAE,OAAO;IACnB,qBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,2BAAoB;IAClB,UAAU,EAAE,OAAO;EAGrB,cAAQ;IACN,UAAU,EAAE,OAAO;IACnB,oBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,0BAAmB;IACjB,UAAU,EAAE,OAAO;EAGrB,eAAS;IACP,UAAU,EAAE,OAAO;IACnB,qBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,2BAAoB;IAClB,UAAU,EAAE,OAAO;EAGrB,YAAM;IACJ,UAAU,EAAE,OAAO;IACnB,kBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,wBAAiB;IACf,UAAU,EAAE,OAAO;EAGrB,kBAAY;IACV,UAAU,EAAE,OAAO;IACnB,wBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,8BAAuB;IACrB,UAAU,EAAE,OAAO;EAGrB,aAAO;IACL,UAAU,EAAE,OAAO;IACnB,mBAAQ;MACN,UAAU,EAAE,OAAO;EAGvB,yBAAkB;IAChB,UAAU,EAAE,OAAO",
"sources": ["socials.scss"],
"names": [],
"file": "socials.css"
}
The file socials.scss contains:
    .social{
        color: #fff;
          &.btn-sm {
            padding: 0 10px 0 0;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0;
          }
          &.btn-lg {

            border-radius: 0;
          }
          &.btn{
            border-radius: 0;
          }
          i{
              width: 1.8125rem;
              line-height: 2;
          }

          &.facebook{
            background: #3b5998;
            &:hover {
              background: #344e86;
            }
          }
          [class*="-facebook"] {
            background: #2d4373;
          }

          &.twitter{
            background: #00aced;
            &:hover {
              background: #0099d4;
            }
          }
          [class*="-twitter"] {
            background: #0087ba;
          }

          &.linkedin{
            background: #4875b4;
            &:hover {
              background: #4169a2;
            }
          }
          [class*="-linkedin"] {
            background: #395d90;
          }

          &.flickr{
            background: #ff0084;
            &:hover {
              background: #e60077;
            }
          }
          [class*="-flickr"] {
            background: #cc006a;
          }

          &.tumblr{
            background: #32506d;
            &:hover {
              background: #2a435c;
            }
          }
          [class*="-tumblr"] {
            background: #22364a;
          }

          &.xing{
            background: #026466;
            &:hover {
              background: #024b4d;
            }
          }
          [class*="-xing"] {
            background: #013334;
          }

          &.github{
            background: #4183c4;
            &:hover {
              background: #3876b4;
            }
          }
          [class*="-github"] {
            background: #3269a0;
          }

          &.html5{
            background: #e34f26;
            &:hover {
              background: #d4431b;
            }
          }
          [class*="-html5"] {
            background: #be3c18;
          }

          &.openid{
            background: #f78c40;
            &:hover {
              background: #f67d28;
            }
          }
          [class*="-openid"] {
            background: #f56f0f;
          }

          &.stack-overflow{
            background: #fe7a15;
            &:hover {
              background: #f86c01;
            }
          }
          [class*="-stack-overflow"] {
            background: #df6101;
          }

          &.css3{
            background: #0170ba;
            &:hover {
              background: #0161a1;
            }
          }
          [class*="-css3"] {
            background: #015187;
          }

          &.youtube{
            background: #b00;
            &:hover {
              background: #a20000;
            }
          }
          [class*="-youtube"] {
            background: #880000;
          }

          &.dribbble{
            background: #ea4c89;
            &:hover {
              background: #e7357a;
            }
          }
          [class*="-dribbble"] {
            background: #e51e6b;
          }

          &.dropbox{
            background: #007ee5;
            &:hover {
              background: #0070cc;
            }
          }
          [class*="-dropbox"] {
            background: #0070cc;
          }

          &.google-plus{
            background: #d34836;
            &:hover {
              background: #c43d2b;
            }
          }
          [class*="-google-plus"] {
            background: #b03626;
          }

          &.instagram{
            background: #517fa4;
            &:hover {
              background: #497293;
            }
          }
          [class*="-instagram"] {
            background: #406582;
          }

          &.pinterest{
            background: #cb2027;
            &:hover {
              background: #b51d23;
            }
          }
          [class*="-pinterest"] {
            background: #9f191f;
          }

          &.vk{
            background: #45668e;
            &:hover {
              background: #3d5a7d;
            }
          }
          [class*="-vk"] {
            background: #344d6c;
          }

          &.yahoo{
            background: #400191;
            &:hover {
              background: #350178;
            }
          }
          [class*="-yahoo"] {
            background: #2a015e;
          }

          &.behance{
            background: #1769ff;
            &:hover {
              background: #0059fd;
            }
          }
          [class*="-behance"] {
            background: #0050e3;
          }

          &.reddit{
            background: #ff4500;
            &:hover {
              background: #e63e00;
            }
          }
          [class*="-reddit"] {
            background: #cc3700;
          }

          &.spotify{
            background: #7ab800;
            &:hover {
              background: #699f00;
            }
          }
          [class*="-spotify"] {
            background: #588500;
          }

          &.vine{
            background: #00bf8f;
            &:hover {
              background: #00a67c;
            }
          }
          [class*="-vine"] {
            background: #008c69;
          }

          &.foursquare{
            background: #1073af;
            &:hover {
              background: #0e6498;
            }
          }
          [class*="-foursquare"] {
            background: #0c5480;
          }

          &.vimeo{
            background: #aad450;
            &:hover {
              background: #a0cf3c;
            }
          }
          [class*="-vimeo"] {
            background: #93c130;
          }


     }

The file style.css contains:
/* This css file is to over write bootstarp css
--------------------------------------------------------- /
* Theme Name: Sufee-Admin Admin Template
* Theme URI: http://demos.jeweltheme.com/Sufee-Admin/
* Author: jewel_theme
* Author URI: http://themeforest.net/user/jewel_theme/portfolio
* Description:
* Version: 1.0.0
* License: GNU General Public License v2 or later
* License URI: http://www.gnu.org/licenses/gpl-2.0.html
* Tags: html, themplate, Sufee-Admin
--------------------------------------------------------- */
/* Bootstrap */
@import url(../css/animate.css);
.gaugejs-wrap {
  position: relative;
  margin: 0 auto; }
  .gaugejs-wrap canvas.gaugejs {
    width: 100% !important;
    height: auto !important; }
  .gaugejs-wrap i, .gaugejs-wrap.sparkline .value {
    top: 50%;
    display: block;
    width: 100%;
    text-align: center; }
  .gaugejs-wrap i {
    position: absolute;
    left: 0;
    z-index: 1000;
    margin-top: -15px;
    font-size: 30px; }
  .gaugejs-wrap.type-2 .value {
    display: block;
    margin-top: -85px; }
  .gaugejs-wrap.type-2 label {
    display: block;
    margin-top: -10px;
    font-size: 10px;
    font-weight: 600;
    color: #9da0a8;
    text-transform: uppercase; }
  .gaugejs-wrap.sparkline {
    position: relative; }
    .gaugejs-wrap.sparkline .value {
      position: absolute;
      margin-top: -5px;
      font-size: 10px;
      line-height: 10px; }

.switch.switch-default {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 40px;
  height: 24px;
  background-color: transparent;
  cursor: pointer; }
  .switch.switch-default .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0; }
  .switch.switch-default .switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: opacity background .15s ease-out; }
  .switch.switch-default .switch-input:checked ~ .switch-label::before {
    opacity: 0; }
  .switch.switch-default .switch-input:checked ~ .switch-label::after {
    opacity: 1; }
  .switch.switch-default .switch-handle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 1px;
    transition: left .15s ease-out; }
  .switch.switch-default .switch-input:checked ~ .switch-handle {
    left: 18px; }
  .switch.switch-default.switch-lg {
    width: 48px;
    height: 28px; }
    .switch.switch-default.switch-lg .switch-label {
      font-size: 12px; }
    .switch.switch-default.switch-lg .switch-handle {
      width: 24px;
      height: 24px; }
    .switch.switch-default.switch-lg .switch-input:checked ~ .switch-handle {
      left: 22px; }
  .switch.switch-default.switch-sm {
    width: 32px;
    height: 20px; }
    .switch.switch-default.switch-sm .switch-label {
      font-size: 8px; }
    .switch.switch-default.switch-sm .switch-handle {
      width: 16px;
      height: 16px; }
    .switch.switch-default.switch-sm .switch-input:checked ~ .switch-handle {
      left: 14px; }
  .switch.switch-default.switch-xs {
    width: 24px;
    height: 16px; }
    .switch.switch-default.switch-xs .switch-label {
      font-size: 7px; }
    .switch.switch-default.switch-xs .switch-handle {
      width: 12px;
      height: 12px; }
    .switch.switch-default.switch-xs .switch-input:checked ~ .switch-handle {
      left: 10px; }

.switch.switch-text {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 48px;
  height: 24px;
  background-color: transparent;
  cursor: pointer; }
  .switch.switch-text .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0; }
  .switch.switch-text .switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: opacity background .15s ease-out; }
  .switch.switch-text .switch-label::before,
  .switch.switch-text .switch-label::after {
    position: absolute;
    top: 50%;
    width: 50%;
    margin-top: -.5em;
    line-height: 1;
    text-align: center;
    transition: inherit; }
  .switch.switch-text .switch-label::before {
    right: 1px;
    color: #e9ecef;
    content: attr(data-off); }
  .switch.switch-text .switch-label::after {
    left: 1px;
    color: #fff;
    content: attr(data-on);
    opacity: 0; }
  .switch.switch-text .switch-input:checked ~ .switch-label::before {
    opacity: 0; }
  .switch.switch-text .switch-input:checked ~ .switch-label::after {
    opacity: 1; }
  .switch.switch-text .switch-handle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 1px;
    transition: left .15s ease-out; }
  .switch.switch-text .switch-input:checked ~ .switch-handle {
    left: 26px; }
  .switch.switch-text.switch-lg {
    width: 56px;
    height: 28px; }
    .switch.switch-text.switch-lg .switch-label {
      font-size: 12px; }
    .switch.switch-text.switch-lg .switch-handle {
      width: 24px;
      height: 24px; }
    .switch.switch-text.switch-lg .switch-input:checked ~ .switch-handle {
      left: 30px; }
  .switch.switch-text.switch-sm {
    width: 40px;
    height: 20px; }
    .switch.switch-text.switch-sm .switch-label {
      font-size: 8px; }
    .switch.switch-text.switch-sm .switch-handle {
      width: 16px;
      height: 16px; }
    .switch.switch-text.switch-sm .switch-input:checked ~ .switch-handle {
      left: 22px; }
  .switch.switch-text.switch-xs {
    width: 32px;
    height: 16px; }
    .switch.switch-text.switch-xs .switch-label {
      font-size: 7px; }
    .switch.switch-text.switch-xs .switch-handle {
      width: 12px;
      height: 12px; }
    .switch.switch-text.switch-xs .switch-input:checked ~ .switch-handle {
      left: 18px; }

.switch.switch-icon {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 48px;
  height: 24px;
  background-color: transparent;
  cursor: pointer; }
  .switch.switch-icon .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0; }
  .switch.switch-icon .switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-family: FontAwesome;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: opacity background .15s ease-out; }
  .switch.switch-icon .switch-label::before,
  .switch.switch-icon .switch-label::after {
    position: absolute;
    top: 50%;
    width: 50%;
    margin-top: -.5em;
    line-height: 1;
    text-align: center;
    transition: inherit; }
  .switch.switch-icon .switch-label::before {
    right: 1px;
    color: #e9ecef;
    content: attr(data-off); }
  .switch.switch-icon .switch-label::after {
    left: 1px;
    color: #fff;
    content: attr(data-on);
    opacity: 0; }
  .switch.switch-icon .switch-input:checked ~ .switch-label::before {
    opacity: 0; }
  .switch.switch-icon .switch-input:checked ~ .switch-label::after {
    opacity: 1; }
  .switch.switch-icon .switch-handle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 1px;
    transition: left .15s ease-out; }
  .switch.switch-icon .switch-input:checked ~ .switch-handle {
    left: 26px; }
  .switch.switch-icon.switch-lg {
    width: 56px;
    height: 28px; }
    .switch.switch-icon.switch-lg .switch-label {
      font-size: 12px; }
    .switch.switch-icon.switch-lg .switch-handle {
      width: 24px;
      height: 24px; }
    .switch.switch-icon.switch-lg .switch-input:checked ~ .switch-handle {
      left: 30px; }
  .switch.switch-icon.switch-sm {
    width: 40px;
    height: 20px; }
    .switch.switch-icon.switch-sm .switch-label {
      font-size: 8px; }
    .switch.switch-icon.switch-sm .switch-handle {
      width: 16px;
      height: 16px; }
    .switch.switch-icon.switch-sm .switch-input:checked ~ .switch-handle {
      left: 22px; }
  .switch.switch-icon.switch-xs {
    width: 32px;
    height: 16px; }
    .switch.switch-icon.switch-xs .switch-label {
      font-size: 7px; }
    .switch.switch-icon.switch-xs .switch-handle {
      width: 12px;
      height: 12px; }
    .switch.switch-icon.switch-xs .switch-input:checked ~ .switch-handle {
      left: 18px; }

.switch.switch-3d {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 40px;
  height: 24px;
  background-color: transparent;
  cursor: pointer; }
  .switch.switch-3d .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0; }
  .switch.switch-3d .switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: opacity background .15s ease-out; }
  .switch.switch-3d .switch-input:checked ~ .switch-label::before {
    opacity: 0; }
  .switch.switch-3d .switch-input:checked ~ .switch-label::after {
    opacity: 1; }
  .switch.switch-3d .switch-handle {
    position: absolute;
    top: 0;
    left: 0;
    width: 24px;
    height: 24px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 1px;
    transition: left .15s ease-out;
    border: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); }
  .switch.switch-3d .switch-input:checked ~ .switch-handle {
    left: 16px; }
  .switch.switch-3d.switch-lg {
    width: 48px;
    height: 28px; }
    .switch.switch-3d.switch-lg .switch-label {
      font-size: 12px; }
    .switch.switch-3d.switch-lg .switch-handle {
      width: 28px;
      height: 28px; }
    .switch.switch-3d.switch-lg .switch-input:checked ~ .switch-handle {
      left: 20px; }
  .switch.switch-3d.switch-sm {
    width: 32px;
    height: 20px; }
    .switch.switch-3d.switch-sm .switch-label {
      font-size: 8px; }
    .switch.switch-3d.switch-sm .switch-handle {
      width: 20px;
      height: 20px; }
    .switch.switch-3d.switch-sm .switch-input:checked ~ .switch-handle {
      left: 12px; }
  .switch.switch-3d.switch-xs {
    width: 24px;
    height: 16px; }
    .switch.switch-3d.switch-xs .switch-label {
      font-size: 7px; }
    .switch.switch-3d.switch-xs .switch-handle {
      width: 16px;
      height: 16px; }
    .switch.switch-3d.switch-xs .switch-input:checked ~ .switch-handle {
      left: 8px; }

.switch-pill .switch-label, .switch.switch-3d .switch-label,
.switch-pill .switch-handle,
.switch.switch-3d .switch-handle {
  border-radius: 50em !important; }
.switch-pill .switch-label::before, .switch.switch-3d .switch-label::before {
  right: 2px !important; }
.switch-pill .switch-label::after, .switch.switch-3d .switch-label::after {
  left: 2px !important; }

.switch-primary > .switch-input:checked ~ .switch-label {
  background: #007bff !important;
  border-color: #0062cc; }
.switch-primary > .switch-input:checked ~ .switch-handle {
  border-color: #0062cc; }

.switch-primary-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #007bff; }
  .switch-primary-outline > .switch-input:checked ~ .switch-label::after {
    color: #007bff; }
.switch-primary-outline > .switch-input:checked ~ .switch-handle {
  border-color: #007bff; }

.switch-primary-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #007bff; }
  .switch-primary-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #007bff; }
.switch-primary-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #007bff !important;
  border-color: #007bff; }

.switch-secondary > .switch-input:checked ~ .switch-label {
  background: #868e96 !important;
  border-color: #6c757d; }
.switch-secondary > .switch-input:checked ~ .switch-handle {
  border-color: #6c757d; }

.switch-secondary-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #868e96; }
  .switch-secondary-outline > .switch-input:checked ~ .switch-label::after {
    color: #868e96; }
.switch-secondary-outline > .switch-input:checked ~ .switch-handle {
  border-color: #868e96; }

.switch-secondary-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #868e96; }
  .switch-secondary-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #868e96; }
.switch-secondary-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #868e96 !important;
  border-color: #868e96; }

.switch-success > .switch-input:checked ~ .switch-label {
  background: #28a745 !important;
  border-color: #1e7e34; }
.switch-success > .switch-input:checked ~ .switch-handle {
  border-color: #1e7e34; }

.switch-success-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #28a745; }
  .switch-success-outline > .switch-input:checked ~ .switch-label::after {
    color: #28a745; }
.switch-success-outline > .switch-input:checked ~ .switch-handle {
  border-color: #28a745; }

.switch-success-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #28a745; }
  .switch-success-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #28a745; }
.switch-success-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #28a745 !important;
  border-color: #28a745; }

.switch-info > .switch-input:checked ~ .switch-label {
  background: #17a2b8 !important;
  border-color: #117a8b; }
.switch-info > .switch-input:checked ~ .switch-handle {
  border-color: #117a8b; }

.switch-info-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #17a2b8; }
  .switch-info-outline > .switch-input:checked ~ .switch-label::after {
    color: #17a2b8; }
.switch-info-outline > .switch-input:checked ~ .switch-handle {
  border-color: #17a2b8; }

.switch-info-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #17a2b8; }
  .switch-info-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #17a2b8; }
.switch-info-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #17a2b8 !important;
  border-color: #17a2b8; }

.switch-warning > .switch-input:checked ~ .switch-label {
  background: #ffc107 !important;
  border-color: #d39e00; }
.switch-warning > .switch-input:checked ~ .switch-handle {
  border-color: #d39e00; }

.switch-warning-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #ffc107; }
  .switch-warning-outline > .switch-input:checked ~ .switch-label::after {
    color: #ffc107; }
.switch-warning-outline > .switch-input:checked ~ .switch-handle {
  border-color: #ffc107; }

.switch-warning-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #ffc107; }
  .switch-warning-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #ffc107; }
.switch-warning-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #ffc107 !important;
  border-color: #ffc107; }

.switch-danger > .switch-input:checked ~ .switch-label {
  background: #dc3545 !important;
  border-color: #bd2130; }
.switch-danger > .switch-input:checked ~ .switch-handle {
  border-color: #bd2130; }

.switch-danger-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #dc3545; }
  .switch-danger-outline > .switch-input:checked ~ .switch-label::after {
    color: #dc3545; }
.switch-danger-outline > .switch-input:checked ~ .switch-handle {
  border-color: #dc3545; }

.switch-danger-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #dc3545; }
  .switch-danger-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #dc3545; }
.switch-danger-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #dc3545 !important;
  border-color: #dc3545; }

.switch-light > .switch-input:checked ~ .switch-label {
  background: #f8f9fa !important;
  border-color: #dae0e5; }
.switch-light > .switch-input:checked ~ .switch-handle {
  border-color: #dae0e5; }

.switch-light-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #f8f9fa; }
  .switch-light-outline > .switch-input:checked ~ .switch-label::after {
    color: #f8f9fa; }
.switch-light-outline > .switch-input:checked ~ .switch-handle {
  border-color: #f8f9fa; }

.switch-light-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #f8f9fa; }
  .switch-light-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #f8f9fa; }
.switch-light-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #f8f9fa !important;
  border-color: #f8f9fa; }

.switch-dark > .switch-input:checked ~ .switch-label {
  background: #343a40 !important;
  border-color: #1d2124; }
.switch-dark > .switch-input:checked ~ .switch-handle {
  border-color: #1d2124; }

.switch-dark-outline > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #343a40; }
  .switch-dark-outline > .switch-input:checked ~ .switch-label::after {
    color: #343a40; }
.switch-dark-outline > .switch-input:checked ~ .switch-handle {
  border-color: #343a40; }

.switch-dark-outline-alt > .switch-input:checked ~ .switch-label {
  background: #fff !important;
  border-color: #343a40; }
  .switch-dark-outline-alt > .switch-input:checked ~ .switch-label::after {
    color: #343a40; }
.switch-dark-outline-alt > .switch-input:checked ~ .switch-handle {
  background: #343a40 !important;
  border-color: #343a40; }

.social-box {
  min-height: 160px;
  margin-bottom: 1.5rem;
  text-align: center;
  background: #fff; }
  .social-box i {
    display: block;
    margin: -1px -1px 0;
    font-size: 40px;
    line-height: 90px;
    background: #e9ecef; }
  .social-box .chart-wrapper {
    height: 90px;
    margin: -90px 0 0; }
    .social-box .chart-wrapper canvas {
      width: 100% !important;
      height: 90px !important; }
  .social-box ul {
    padding: 10px 0;
    list-style: none; }
    .social-box ul li {
      display: block;
      float: left;
      width: 50%;
      padding-top: 10px;
      font-size: 18px; }
      .social-box ul li:first-child {
        border-right: 1px solid #c2cfd6; }
      .social-box ul li strong {
        display: block;
        font-size: 20px; }
      .social-box ul li span {
        font-size: 18px;
        font-weight: 500;
        color: #949CA0;
        text-transform: uppercase; }
  .social-box.facebook i {
    color: #fff;
    background: #3b5998; }
  .social-box.twitter i {
    color: #fff;
    background: #00aced; }
  .social-box.linkedin i {
    color: #fff;
    background: #4875b4; }
  .social-box.google-plus i {
    color: #fff;
    background: #d34836; }

.horizontal-bars {
  padding: 0;
  margin: 0;
  list-style: none; }
  .horizontal-bars li {
    position: relative;
    height: 40px;
    line-height: 40px;
    vertical-align: middle; }
    .horizontal-bars li .title {
      width: 100px;
      font-size: 12px;
      font-weight: 600;
      color: #868e96;
      vertical-align: middle; }
    .horizontal-bars li .bars {
      position: absolute;
      top: 15px;
      width: 100%;
      padding-left: 100px; }
      .horizontal-bars li .bars .progress:first-child {
        margin-bottom: 2px; }
    .horizontal-bars li.legend {
      text-align: center; }
      .horizontal-bars li.legend .badge {
        display: inline-block;
        width: 8px;
        height: 8px;
        padding: 0; }
    .horizontal-bars li.divider {
      height: 40px; }
      .horizontal-bars li.divider i {
        margin: 0 !important; }
  .horizontal-bars.type-2 li {
    overflow: hidden; }
    .horizontal-bars.type-2 li i {
      display: inline-block;
      margin-right: 1rem;
      margin-left: 5px;
      font-size: 18px;
      line-height: 40px; }
    .horizontal-bars.type-2 li .title {
      display: inline-block;
      width: auto;
      margin-top: -9px;
      font-size: 1rem;
      font-weight: normal;
      line-height: 40px;
      color: #212529; }
    .horizontal-bars.type-2 li .value {
      float: right;
      font-weight: 600; }
    .horizontal-bars.type-2 li .bars {
      position: absolute;
      top: auto;
      bottom: 0;
      padding: 0; }

.icons-list {
  padding: 0;
  margin: 0;
  list-style: none; }
  .icons-list li {
    position: relative;
    height: 40px;
    vertical-align: middle; }
    .icons-list li i {
      display: block;
      float: left;
      width: 35px !important;
      height: 35px !important;
      margin: 2px;
      line-height: 35px !important;
      text-align: center; }
    .icons-list li .desc {
      height: 40px;
      margin-left: 50px;
      border-bottom: 1px solid #e9ecef; }
      .icons-list li .desc .title {
        padding: 2px 0 0;
        margin: 0; }
      .icons-list li .desc small {
        display: block;
        margin-top: -4px;
        color: #868e96; }
    .icons-list li .value {
      position: absolute;
      top: 2px;
      right: 45px;
      text-align: right; }
      .icons-list li .value strong {
        display: block;
        margin-top: -3px; }
    .icons-list li .actions {
      position: absolute;
      top: -4px;
      right: 10px;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center; }
      .icons-list li .actions i {
        float: none;
        width: auto;
        height: auto;
        padding: 0;
        margin: 0;
        line-height: normal; }
    .icons-list li.divider {
      height: 40px; }
      .icons-list li.divider i {
        width: auto;
        height: auto;
        margin: 2px 0 0;
        font-size: 18px; }

.bg-flat-color-1 {
  background: #20a8d8; }

.bg-flat-color-2 {
  background: #63c2de; }

.bg-flat-color-3 {
  background: #ffc107; }

.bg-flat-color-4 {
  background: #f86c6b; }

.bg-flat-color-5 {
  background: #4dbd74; }

.transition {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease; }

body {
  background: #f1f2f7;
  display: table;
  font-family: 'Open Sans' sans-serif !important;
  font-size: 16px;
  width: 100%; }

div[class*="col-"] {
  float: left; }

p {
  font-size: 16px;
  font-family: 'Open Sans' sans-serif;
  font-weight: 400;
  line-height: 24px;
  color: #878787; }

p:focus {
  border: none;
  outline: 0; }

a, button {
  text-decoration: none;
  outline: none !important;
  color: #878787;
  -webkit-transition: all 0.25s ease;
  -moz-transition: all 0.25s ease;
  -ms-transition: all 0.25s ease;
  -o-transition: all 0.25s ease;
  transition: all 0.25s ease; }

a:hover,
a:focus {
  text-decoration: none;
  color: #000; }

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0; }

ul,
ol {
  padding-left: 0; }

.btn:focus,
button:focus {
  box-shadow: none !important;
  outline: 0; }

img {
  max-width: 100%; }

.btn,
button,
input,
textarea {
  box-shadow: none;
  outline: 0 !important; }

.no-padding {
  padding: 0 !important; }

/* Global Styles */
/* Main Styles */
.basix-container {
  display: table;
  min-height: 100vh;
  position: relative;
  width: 100%; }

aside.left-panel {
  background: #272c33;
  display: table-cell;
  height: 100vh;
  min-height: 100%;
  padding: 0 25px;
  vertical-align: top;
  width: 280px;
  transition: width 0.3s ease; }

.navbar {
  background: #272c33;
  border-radius: 0;
  border: none;
  display: block;
  margin: 0;
  margin-bottom: 100px;
  padding: 0; }
  .navbar .navbar-header {
    float: none;
    text-align: center;
    width: 100%; }
  .navbar .navbar-brand {
    border-bottom: 1px solid #4e4e52;
    color: #f1f2f7 !important;
    font-family: 'Open Sans';
    font-size: 22px;
    float: none;
    line-height: 50px;
    margin: 0;
    text-align: left;
    text-transform: capitalize;
    display: block;
    min-height: 69px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative; }
    .navbar .navbar-brand span {
      font-weight: 600; }
    .navbar .navbar-brand img {
      max-width: 160px; }
    .navbar .navbar-brand.hidden {
      display: none; }
  .navbar .menu-title {
    border-bottom: 1px solid #4e4e52;
    color: #9496a1;
    clear: both;
    display: block;
    font-family: 'Open Sans';
    font-size: 14px;
    font-weight: 700;
    line-height: 50px;
    padding: 15px 0 0 0;
    text-transform: uppercase;
    width: 100%; }
  .navbar .navbar-nav {
    float: none;
    position: relative; }
    .navbar .navbar-nav li {
      width: 100%; }
      .navbar .navbar-nav li.active .menu-icon, .navbar .navbar-nav li:hover .toggle_nav_button:before,
      .navbar .navbar-nav li .toggle_nav_button.nav-open:before {
        color: #fff !important; }
      .navbar .navbar-nav li .dropdown-toggle:after {
        display: none; }
      .navbar .navbar-nav li > a {
        background: none !important;
        color: #c8c9ce !important;
        display: inline-block;
        font-family: 'Open Sans';
        font-size: 14px;
        line-height: 30px;
        padding: 10px 0;
        position: relative;
        width: 100%; }
        .navbar .navbar-nav li > a:hover, .navbar .navbar-nav li > a:hover .menu-icon {
          color: #fff !important; }
        .navbar .navbar-nav li > a .menu-icon {
          color: #8b939b;
          float: left;
          margin-top: 8px;
          width: 55px;
          text-align: left;
          z-index: 9; }
        .navbar .navbar-nav li > a .menu-title-text {
          font-size: 14px; }
        .navbar .navbar-nav li > a .badge {
          border-radius: 0;
          font-family: 'Open Sans';
          font-weight: 600;
          float: right;
          margin: 6px 0 0 0;
          padding: 0.4em 0.5em; }
      .navbar .navbar-nav li.menu-item-has-children {
        position: relative; }
        .navbar .navbar-nav li.menu-item-has-children a {
          line-height: 30px; }
          .navbar .navbar-nav li.menu-item-has-children a:before {
            content: "\f105";
            color: #c8c9ce;
            font-family: 'Fontawesome';
            font-size: 16px;
            position: absolute;
            top: 10px;
            right: 0;
            text-align: right;
            -webkit-transition: all .25s ease;
            -moz-transition: all .25s ease;
            -ms-transition: all .25s ease;
            -o-transition: all .25s ease;
            transition: all .25s ease; }
          .navbar .navbar-nav li.menu-item-has-children a:hover:before {
            color: #fff; }
        .navbar .navbar-nav li.menu-item-has-children .sub-menu {
          background: #272c33;
          border: none;
          box-shadow: none;
          overflow-y: hidden;
          padding: 0 0 0 35px; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu li {
            position: relative; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu i {
            color: #c8c9ce;
            float: left;
            padding: 0;
            position: absolute;
            left: 0;
            font-size: 14px;
            top: 9px; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu a {
            padding: 2px 0 2px 30px; }
            .navbar .navbar-nav li.menu-item-has-children .sub-menu a:before {
              content: '';
              display: none; }
            .navbar .navbar-nav li.menu-item-has-children .sub-menu a .menu-icon {
              top: 13px;
              text-align: left;
              width: 25px; }
        .navbar .navbar-nav li.menu-item-has-children.show a:before {
          content: "\f107"; }
        .navbar .navbar-nav li.menu-item-has-children.show .sub-menu {
          max-height: 1000px;
          opacity: 1;
          position: static !important; }

.navbar .navbar-nav > .active > a,
.navbar .navbar-nav > .active > a:focus,
.navbar .navbar-nav > .active > a:hover {
  color: #d7d9e3 !important; }

.navbar-nav li span.count {
  background: #a9d86e;
  border-radius: 50%;
  color: #fff;
  font-family: 'Open Sans';
  font-size: 9px;
  font-weight: 700;
  float: right;
  height: 20px;
  width: 20px;
  line-height: 20px;
  margin-right: 15px;
  text-align: center; }

body.open .navbar .navbar-brand.hidden {
  display: block; }

.open aside.left-panel {
  max-width: 70px;
  width: 70px; }
  .open aside.left-panel .navbar .navbar-brand {
    display: none; }
    .open aside.left-panel .navbar .navbar-brand.hidden {
      display: flex !important;
      justify-content: center;
      align-items: center;
      padding-left: 0;
      padding-right: 0;
      text-align: center; }
      .open aside.left-panel .navbar .navbar-brand.hidden img {
        max-width: 30px;
        margin: 0 auto; }
    .open aside.left-panel .navbar .navbar-brand.d-md-none {
      display: block !important;
      margin: 13px 0 0;
      min-height: 67px;
      padding: 0;
      text-align: center; }
  .open aside.left-panel .navbar .navbar-nav:before {
    display: none !important; }
  .open aside.left-panel .navbar .navbar-nav li {
    position: relative; }
    .open aside.left-panel .navbar .navbar-nav li a {
      font-size: 0;
      z-index: 0;
      transition: none; }
      .open aside.left-panel .navbar .navbar-nav li a .menu-icon {
        font-size: 20px;
        z-index: -1;
        width: inherit; }
      .open aside.left-panel .navbar .navbar-nav li a .menu-title-text {
        font-size: 0; }
      .open aside.left-panel .navbar .navbar-nav li a .badge {
        display: none; }
    .open aside.left-panel .navbar .navbar-nav li > a {
      max-width: 60px;
      padding-left: 0; }
    .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children {
      overflow: hidden; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children a:before {
        content: '';
        display: none; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children ul {
        padding-left: 0; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu {
        display: block;
        left: inherit;
        right: -180px;
        top: 0; }
        .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu li a {
          display: block;
          font-size: 14px;
          max-width: inherit;
          padding: 2px 15px 2px 25px;
          width: 100%; }
          .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu li a .menu-icon {
            text-align: center; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children.show {
        overflow: visible; }
        .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children.show .sub-menu {
          position: absolute !important; }
    .open aside.left-panel .navbar .navbar-nav li span.count {
      display: none;
      margin-right: 5px;
      z-index: 1; }
    .open aside.left-panel .navbar .navbar-nav li.active a:after {
      content: '';
      display: none; }
  .open aside.left-panel .navbar .navbar-nav .menu-title {
    font-size: 0;
    line-height: 0;
    opacity: 0;
    padding: 0; }

/* Right panel */
.right-panel {
  display: table-cell;
  padding-left: 0 !important;
  -webkit-transition: all .35s ease;
  -moz-transition: all .35s ease;
  -ms-transition: all .35s ease;
  -o-transition: all .35s ease;
  transition: all .35s ease; }
  .right-panel .breadcrumbs {
    background-color: #fff;
    display: inline-block;
    margin-top: 0;
    padding: 0 5px;
    width: 100%; }
    .right-panel .breadcrumbs .col-lg-8 .page-header {
      float: left; }
  .right-panel .page-header {
    min-height: 50px;
    margin: 0px;
    padding: 0px 15px;
    background: #ffffff;
    border-bottom: 0px; }
    .right-panel .page-header h1 {
      font-size: 18px;
      padding: 15px 0; }
    .right-panel .page-header .breadcrumb {
      margin: 0px;
      padding: 13.5px 0;
      background: #fff;
      text-transform: capitalize; }
    .right-panel .page-header .breadcrumb > li + li:before {
      padding: 0 5px;
      color: #ccc;
      content: "/\00a0"; }

.right-panel header.header {
  background: #fff;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
  clear: both;
  display: inline-block;
  padding: 15px 20px 13px 20px;
  width: 100%; }

.open .right-panel {
  margin-left: -210px; }

header.fixed-top {
  background: #fff;
  padding: 20px; }

.header-menu .col-sm-7 {
  position: inherit; }

.menutoggle {
  background: #e74c3c;
  border-radius: 50%;
  color: #fff !important;
  cursor: pointer;
  font-size: 18px;
  height: 43px;
  line-height: 44px;
  margin: -2px 20px 0 -57px;
  text-align: center;
  width: 43px; }

.open .menutoggle i:before {
  content: "\f0a4"; }

.search-trigger {
  background: transparent;
  border: none;
  color: #272c33;
  cursor: pointer;
  font-size: 16px;
  height: 41px;
  width: 43px;
  line-height: 38px; }

header .form-inline {
  background: #263238;
  display: none;
  height: 70px;
  margin: 0;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 9999; }
  header .form-inline .search-form {
    height: 100%;
    max-width: 1025px;
    margin: 0 auto;
    position: relative; }
    header .form-inline .search-form input[type="text"] {
      background: #263238;
      border: none;
      border-radius: 0;
      box-shadow: none;
      color: #d3d3d3;
      font-size: 16px;
      height: inherit;
      margin-right: 0 !important;
      padding: 10px 36px 10px 15px;
      width: 100%; }
    header .form-inline .search-form input[type="text"].active,
    header .form-inline .search-form input[type="text"]:focus {
      border-color: rgba(0, 0, 0, 0.125);
      outline: 0; }
    header .form-inline .search-form button {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 16px;
      position: absolute;
      right: 15px;
      top: 50%;
      margin-top: -14px !important; }
    header .form-inline .search-form button:active,
    header .form-inline .search-form button:focus,
    header .form-inline .search-form button:visited,
    header .form-inline .search-form .btn-outline-success:hover {
      background: transparent;
      border: none !important;
      box-shadow: none;
      outline: 0 !important; }
    header .form-inline .search-form.close {
      display: none; }

.header-left.open .form-inline {
  display: block; }

.header-left .dropdown {
  display: inline-block; }
  .header-left .dropdown .dropdown-toggle {
    background: transparent;
    border: none;
    color: #272c33;
    font-size: 16px; }
    .header-left .dropdown .dropdown-toggle:after {
      display: none; }
    .header-left .dropdown .dropdown-toggle .count {
      border-radius: 50%;
      color: #fff;
      font-size: 11px;
      height: 15px;
      width: 15px;
      line-height: 15px;
      right: 0;
      top: 0;
      position: absolute; }
    .header-left .dropdown .dropdown-toggle:active, .header-left .dropdown .dropdown-toggle:focus, .header-left .dropdown .dropdown-toggle:visited {
      background: none !important;
      border-color: transparent !important;
      color: #272c33 !important; }
  .header-left .dropdown .dropdown-menu {
    background: #fff;
    border: none;
    border-radius: 0;
    box-shadow: none;
    top: 49px !important; }
    .header-left .dropdown .dropdown-menu p {
      font-size: 15px;
      margin: 0;
      padding: 5px 15px; }
    .header-left .dropdown .dropdown-menu .dropdown-item {
      color: #272c33;
      font-size: 13px;
      padding: 10px 15px 3px;
      text-overflow: ellipsis; }
      .header-left .dropdown .dropdown-menu .dropdown-item .photo {
        float: left;
        margin-right: 15px;
        width: 25px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message .name {
        margin-top: -5px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message .time {
        font-size: 11px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message p {
        clear: both;
        font-size: 14px;
        margin: 0;
        padding: 0;
        text-overflow: ellipsis; }
      .header-left .dropdown .dropdown-menu .dropdown-item:hover {
        background: transparent; }

.dropdown-menu {
  border-radius: 0;
  transform: none !important; }

.for-notification .dropdown-menu .dropdown-item {
  padding: 5px 15px !important;
  text-overflow: ellipsis; }
  .for-notification .dropdown-menu .dropdown-item i {
    float: left;
    font-size: 14px;
    margin: 5px 5px 0 0;
    text-align: left;
    width: 20px; }
  .for-notification .dropdown-menu .dropdown-item p {
    padding: 0 !important;
    text-overflow: ellipsis; }

.user-area {
  float: right;
  padding-right: 0;
  position: relative; }
  .user-area .user-menu {
    background: #fff;
    border: none;
    font-family: 'Open Sans';
    left: inherit !important;
    right: 0;
    top: 55px !important;
    margin: 0;
    max-width: 150px;
    padding: 5px 10px;
    position: absolute;
    width: 100%;
    z-index: 999;
    min-width: 150px; }
    .user-area .user-menu .nav-link {
      color: #272c33;
      display: block;
      font-size: 14px;
      line-height: 22px;
      padding: 5px 0; }
  .user-area .user-avatar {
    float: right;
    margin-top: 4px;
    width: 32px; }
  .user-area .user-info .name {
    color: #8c8c8c;
    font-size: 14px;
    position: relative;
    text-transform: uppercase; }
  .user-area .count {
    background: #d9534f;
    border-radius: 50%;
    color: #fff;
    font-family: 'Open Sans';
    font-size: 9px;
    font-weight: 700;
    float: right;
    height: 20px;
    width: 20px;
    line-height: 20px;
    text-align: center; }
  .user-area .dropdown-toggle:after {
    display: none; }

#menuToggle2 {
  padding-left: 25px; }

#language-select {
  color: #f1f2f7;
  float: right;
  margin: 7px 20px 0 0;
  max-width: 80px; }
  #language-select:focus, #language-select:visited {
    border: none;
    outline: 0; }
  #language-select .dropdown-toggle::after {
    display: none; }
  #language-select .dropdown-menu {
    background: #fff;
    border: none;
    border-radius: 0;
    left: -8px !important;
    min-width: inherit;
    padding: 0 5px;
    top: 46px !important; }
    #language-select .dropdown-menu .dropdown-item {
      margin-right: 0;
      max-width: 25px;
      padding: 0; }
      #language-select .dropdown-menu .dropdown-item:hover {
        background: #fff; }
      #language-select .dropdown-menu .dropdown-item .flag-icon {
        margin-right: 0;
        width: 25px; }

.notification-show + .dropdown-menu,
.message-show + .dropdown-menu,
.language-show + .dropdown-menu {
  display: block; }

.content {
  float: left;
  padding: 0 20px;
  width: 100%; }

.card {
  margin-bottom: 1.5rem;
  border-radius: 0; }
  .card h4 {
    font-size: 1.1rem; }
  .card .user-header .media img {
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    -webkit-border-radius: 50%; }
  .card .card-header .card-actions button {
    display: block;
    float: left;
    width: 50px;
    padding: .75rem 0;
    margin: 0 !important;
    color: #fff;
    outline: 0;
    text-align: center;
    background: transparent;
    border: 0;
    border-left: 1px solid rgba(120, 130, 140, 0.4); }
  .card .card-footer {
    padding: 0.65rem 1.25rem;
    background-color: #f0f3f5;
    border-top: 1px solid #c2cfd6; }
    .card .card-footer ul li {
      display: table-cell;
      padding: 0 1rem;
      text-align: center; }

.breadcrumbs {
  margin-top: 0; }

/* Tabs */
.nav-tabs a.active {
  color: #555;
  cursor: default;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent; }
.nav-tabs .dropdown .dropdown-menu {
  top: 100% !important; }

.custom-tab .nav-tabs > a.active, .custom-tab .nav-tabs > .active > a:focus, .custom-tab .nav-tabs > li.active > a:hover {
  border-color: transparent transparent;
  color: #ff2e44;
  position: relative; }

.custom-tab .nav-tabs > a.active > a:after, .custom-tab .nav-tabs > li.active > a:focus:after, .custom-tab .nav-tabs > li.active > a:hover:after {
  background: #ff2e44;
  bottom: -1px;
  content: "";
  height: 2px;
  left: 0;
  position: absolute;
  right: 0;
  width: 100%;
  z-index: 999; }

.card .card-header .card-actions {
  float: right; }
  .card .card-header .card-actions [class*="btn"] {
    border-left: 1px solid rgba(120, 130, 140, 0.4);
    color: #878787;
    display: inline-block;
    font-size: 16px;
    float: left;
    padding: 0 7px;
    width: inherit;
    text-align: center; }

.social-buttons .card-body p button {
  padding-top: 0;
  padding-left: 0;
  padding-bottom: 0; }
.social-buttons .only-icon .card-body p button {
  padding: 0; }
.social-buttons .social i {
  padding: 0 10px;
  width: inherit !important; }
.social-buttons .only-text p button {
  padding: 0 .5rem; }

.buttons button {
  margin: 2px 0; }

/* Ribons */
.corner-ribon {
  text-align: center;
  width: 71px;
  height: 71px;
  position: absolute;
  right: 0;
  top: 0;
  font-size: 20px; }

.corner-ribon i {
  padding: 10px 0 0 35px;
  color: #fff; }

.black-ribon {
  background: url("../../images/twitter_corner_black.png") no-repeat; }

.blue-ribon {
  background: url("../../images/twitter_corner_blue.png") no-repeat; }

.twt-feed .wtt-mark {
  color: rgba(255, 255, 255, 0.15);
  font-size: 160px;
  position: absolute;
  top: 10px;
  left: 40%; }

.twt-feed {
  -webkit-border-radius: 4px 4px 0 0;
  color: #FFFFFF;
  padding: 40px 10px 10px;
  position: relative;
  min-height: 170px; }

.weather-category {
  padding: 15px 0;
  color: #74829C; }
  .weather-category ul li {
    width: 32%;
    text-align: center;
    border-right: 1px solid #e6e6e6;
    display: inline-block; }

.twt-feed.blue-bg {
  background: #58C9F3; }

.twt-category {
  display: inline-block;
  margin-bottom: 11px;
  margin-top: 10px;
  width: 100%; }
  .twt-category ul li {
    color: #bdbdbd;
    font-size: 13px; }

.twt-footer {
  padding: 12px 15px; }

.twt-footer, .twt-footer a {
  color: #d2d2d2; }

/* Button Reset */
.btn, .button {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  transition: all .15s ease-in-out;
  border-radius: 0;
  cursor: pointer; }

/* Icons */
.icon-section {
  margin: 0 0 3em;
  clear: both;
  overflow: hidden; }

.icon-container {
  width: 240px;
  padding: .7em 0;
  float: left;
  position: relative;
  text-align: left; }

.icon-container [class^="ti-"],
.icon-container [class*=" ti-"] {
  color: #000;
  position: absolute;
  margin-top: 3px;
  transition: .3s; }

.icon-container:hover [class^="ti-"],
.icon-container:hover [class*=" ti-"] {
  font-size: 2.2em;
  margin-top: -5px; }

.icon-container:hover .icon-name {
  color: #000; }

.icon-name {
  color: #aaa;
  margin-left: 35px;
  font-size: 14px;
  transition: .3s; }

.icon-container:hover .icon-name {
  margin-left: 45px; }

.fontawesome-icon-list .page-header {
  border-bottom: 1px solid #C9CDD7;
  padding-bottom: 9px;
  margin: 30px 0px 27px 0px; }
.fontawesome-icon-list h2 {
  margin-top: 0;
  font-size: 20px;
  font-weight: 300; }
.fontawesome-icon-list i {
  font-style: 16px;
  padding-right: 10px; }

.social-box i {
  line-height: 110px; }
.social-box ul {
  display: inline-block;
  margin: 7px 0 0;
  padding: 10px;
  width: 100%; }
  .social-box ul li {
    color: #949CA0;
    font-size: 14px;
    font-weight: 700;
    padding: 0 10px 0 0;
    text-align: right; }
    .social-box ul li:last-child {
      padding-left: 10px;
      padding-right: 0;
      text-align: left; }
    .social-box ul li span {
      font-size: 14px; }

.login-logo {
  text-align: center;
  margin-bottom: 15px; }
  .login-logo span {
    color: #ffffff;
    font-size: 24px; }

.login-content {
  max-width: 540px;
  margin: 8vh auto; }

.login-form {
  background: #ffffff;
  padding: 30px 30px 20px;
  border-radius: 2px; }

.login-form h4 {
  color: #878787;
  text-align: center;
  margin-bottom: 50px; }

.login-form .checkbox {
  color: #878787; }

.login-form .checkbox label {
  text-transform: none; }

.login-form .btn {
  width: 100%;
  text-transform: uppercase;
  font-size: 14px;
  padding: 15px;
  border: 0px; }

.login-form label {
  color: #878787;
  text-transform: uppercase; }

.login-form label a {
  color: #ff2e44; }

.social-login-content {
  margin: 0px -30px;
  border-top: 1px solid #e7e7e7;
  border-bottom: 1px solid #e7e7e7;
  padding: 30px 0px;
  background: #fcfcfc; }

.social-button {
  padding: 0 30px; }
  .social-button .facebook {
    background: #3b5998;
    color: #fff; }
    .social-button .facebook:hover {
      background: #344e86; }
  .social-button .twitter {
    background: #00aced;
    color: #fff; }
    .social-button .twitter:hover {
      background: #0099d4; }

.social-button i {
  padding: 19px; }

.register-link a {
  color: #ff2e44; }

.cpu-load {
  width: 100%;
  height: 272px;
  font-size: 14px;
  line-height: 1.2em; }

.cpu-load-data-content {
  font-size: 18px;
  font-weight: 400;
  line-height: 40px; }

.cpu-load-data {
  margin-bottom: 30px; }

.cpu-load-data li {
  display: inline-block;
  width: 32.5%;
  text-align: center;
  border-right: 1px solid #e7e7e7; }

.cpu-load-data li:last-child {
  border-right: 0px; }

.nestable-cart {
  overflow: hidden; }

/* Forms */
.input-group-addon {
  background-color: transparent;
  border-left: 0; }

.input-group-addon, .input-group-btn {
  white-space: nowrap;
  vertical-align: middle; }

.input-group-addon {
  padding: .5rem .75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.25;
  color: #495057;
  text-align: center;
  background-color: #e9ecef;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: .25rem; }

.flotTip {
  background: #252525;
  border: 1px solid #252525;
  padding: 5px 15px;
  color: #ffffff; }

.flot-container {
  box-sizing: border-box;
  width: 100%;
  height: 275px;
  padding: 20px 15px 15px;
  margin: 15px auto 30px;
  background: transparent; }

.flot-pie-container {
  height: 275px; }

.flotBar-container {
  height: 275px; }

.flot-line {
  width: 100%;
  height: 100%;
  font-size: 14px;
  line-height: 1.2em; }

.legend table {
  border-spacing: 5px; }

#chart1,
#flotBar,
#flotCurve {
  width: 100%;
  height: 275px; }

.morris-hover {
  position: absolute;
  z-index: 1; }

.morris-hover.morris-default-style .morris-hover-row-label {
  font-weight: bold;
  margin: 0.25em 0; }

.morris-hover.morris-default-style .morris-hover-point {
  white-space: nowrap;
  margin: 0.1em 0; }

.morris-hover.morris-default-style {
  border-radius: 2px;
  padding: 10px 12px;
  color: #666;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff !important; }

.morris-hover-point {
  color: rgba(255, 255, 255, 0.8) !important; }

#morris-bar-chart {
  height: 285px; }

.map, .vmap {
  width: 100%;
  height: 400px; }

.btn-toolbar {
  float: left !important; }
  .btn-toolbar .btn-outline-secondary:not([disabled]):not(.disabled):active,
  .btn-toolbar .btn-outline-secondary:not([disabled]):not(.disabled).active,
  .btn-toolbar .show > .btn-outline-secondary.dropdown-toggle {
    background-color: #212529;
    border-color: #212529;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #fff; }
  .btn-toolbar .btn-outline-secondary:hover {
    background-color: #212529;
    border-color: #212529;
    color: #fff; }

/*    Widget One
---------------------------*/
.dib {
  display: inline-block; }

.stat-widget-one .stat-icon {
  vertical-align: top; }

.stat-widget-one .stat-icon i {
  font-size: 30px;
  border-width: 3px;
  border-style: solid;
  border-radius: 100px;
  padding: 15px;
  font-weight: 900;
  display: inline-block; }

.stat-widget-one .stat-content {
  margin-left: 30px;
  margin-top: 7px; }

.stat-widget-one .stat-text {
  font-size: 14px;
  color: #868e96; }

.stat-widget-one .stat-digit {
  font-size: 24px;
  color: #373757; }

/*    Widget Two
---------------------------*/
.stat-widget-two {
  text-align: center; }

.stat-widget-two .stat-digit {
  font-size: 1.75rem;
  font-weight: 500;
  color: #373757; }

.stat-widget-two .stat-digit i {
  font-size: 18px;
  margin-right: 5px; }

.stat-widget-two .stat-text {
  font-size: 16px;
  margin-bottom: 5px;
  color: #868e96; }

.stat-widget-two .progress {
  height: 8px;
  margin-bottom: 0;
  margin-top: 20px;
  box-shadow: none; }

.stat-widget-two .progress-bar {
  box-shadow: none; }

/*    Widget Three
---------------------------*/
.stat-widget-three .stat-icon {
  display: inline-block;
  padding: 33px;
  position: absolute;
  line-height: 21px; }

.stat-widget-three .stat-icon i {
  font-size: 30px;
  color: #ffffff; }

.stat-widget-three .stat-content {
  text-align: center;
  padding: 15px;
  margin-left: 90px; }

.stat-widget-three .stat-digit {
  font-size: 30px; }

.stat-widget-three .stat-text {
  padding-top: 4px; }

.home-widget-three .stat-icon {
  line-height: 19px;
  padding: 27px; }

.home-widget-three .stat-digit {
  font-size: 24px;
  font-weight: 300;
  color: #373757; }

.home-widget-three .stat-content {
  text-align: center;
  margin-left: 60px;
  padding: 13px; }

.stat-widget-four {
  position: relative; }

.stat-widget-four .stat-icon {
  display: inline-block;
  position: absolute;
  top: 5px; }

.stat-widget-four i {
  display: block;
  font-size: 36px; }

.stat-widget-four .stat-content {
  margin-left: 40px;
  text-align: center; }

.stat-widget-four .stat-heading {
  font-size: 20px; }

.stat-widget-five .stat-icon {
  border-radius: 100px;
  display: inline-block;
  position: absolute; }

.stat-widget-five i {
  border-radius: 100px;
  display: block;
  font-size: 36px;
  padding: 30px; }

.stat-widget-five .stat-content {
  margin-left: 100px;
  padding: 24px 0;
  position: relative;
  text-align: right;
  vertical-align: middle; }

.stat-widget-five .stat-heading {
  text-align: right;
  padding-left: 80px;
  font-size: 20px;
  font-weight: 200; }

.stat-widget-six {
  position: relative; }

.stat-widget-six .stat-icon {
  display: inline-block;
  position: absolute;
  top: 5px; }

.stat-widget-six i {
  display: block;
  font-size: 36px; }

.stat-widget-six .stat-content {
  margin-left: 40px;
  text-align: center; }

.stat-widget-six .stat-heading {
  font-size: 16px;
  font-weight: 300; }

.stat-widget-six .stat-text {
  font-size: 12px;
  padding-top: 4px; }

.stat-widget-seven .stat-heading {
  text-align: center; }

.stat-widget-seven .gradient-circle {
  text-align: center;
  position: relative;
  margin: 30px auto;
  display: inline-block;
  width: 100%; }

.stat-widget-seven .gradient-circle i {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  top: 35px;
  font-size: 30px; }

.stat-widget-seven .stat-footer {
  text-align: center;
  margin-top: 30px; }

.stat-widget-seven .stat-footer .stat-count {
  padding-left: 5px; }

.stat-widget-seven .count-header {
  color: #252525;
  font-size: 12px;
  font-weight: 400;
  line-height: 30px; }

.stat-widget-seven .stat-count {
  font-size: 18px;
  font-weight: 400;
  color: #252525; }

.stat-widget-seven .analytic-arrow {
  position: relative; }

.stat-widget-seven .analytic-arrow i {
  font-size: 12px; }

/* Stat widget Eight
--------------------------- */
.stat-widget-eight {
  padding: 15px; }

.stat-widget-eight .header-title {
  font-size: 20px;
  font-weight: 300; }

.stat-widget-eight .ti-more-alt {
  color: #878787;
  cursor: pointer;
  left: -5px;
  position: absolute;
  transform: rotate(90deg); }

.stat-widget-eight .stat-content {
  margin-top: 50px; }

.stat-widget-eight .stat-content .ti-arrow-up {
  font-size: 30px;
  color: #28a745; }

.stat-widget-eight .stat-content .stat-digit {
  font-size: 24px;
  font-weight: 300;
  margin-left: 15px; }

.stat-widget-eight .stat-content .progress-stats {
  color: #aaadb2;
  font-weight: 400;
  position: relative;
  top: 10px; }

.stat-widget-eight .progress {
  margin-bottom: 0;
  margin-top: 30px;
  height: 7px;
  background: #EAEAEA;
  box-shadow: none; }

.stat-widget-nine .all-like {
  float: right; }

.stat-widget-nine .stat-icon i {
  font-size: 22px; }

.stat-widget-nine .stat-text {
  font-size: 14px; }

.stat-widget-nine .stat-digit {
  font-size: 14px; }

.stat-widget-nine .like-count {
  font-size: 30px; }

.horizontal {
  position: relative; }

.horizontal:before {
  background: #ffffff;
  bottom: 0;
  content: "";
  height: 38px;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  width: 1px; }

.widget-ten span i {
  color: #ffffff;
  opacity: 0.5; }

.widget-ten h5 {
  color: #ffffff; }

.widget-ten p {
  color: #ffffff !important;
  opacity: 0.75; }

/* Mixed Styles */
.badges h1, .badges h2, .badges h3, .badges h4, .badges h5, .badges h6 {
  margin: 5px 0; }

.vue-lists ul, .vue-lists ol {
  padding-left: 30px; }

.card .dropdown.float-right .dropdown-menu {
  left: inherit !important;
  right: 0 !important;
  top: 93% !important; }

.dataTables_paginate .pagination {
  border-radius: 0; }
  .dataTables_paginate .pagination li {
    border-radius: 0 !important; }
    .dataTables_paginate .pagination li a {
      border-radius: 0 !important;
      color: #272c33; }
    .dataTables_paginate .pagination li.active a {
      background: #272c33;
      border-color: #272c33;
      color: #fff; }
    .dataTables_paginate .pagination li:hover a {
      background: #272c33;
      border-color: #272c33;
      color: #fff; }

@media (max-width: 1368px) {
  .content {
    padding: 0 15px; }

  .twt-category {
    margin-bottom: 0; }

  .twt-feed {
    max-height: 155px; }
    .twt-feed img {
      height: 75px;
      width: 75px; }

  .stat-widget-one .stat-content {
    margin-left: 15px; }

  .card-body {
    padding: 15px; }

  .badges button {
    margin: 2px 0; } }
@media (max-width: 1024px) {
  aside.left-panel {
    padding: 0 20px;
    width: 200px; }

  .navbar .navbar-nav li > a .menu-icon {
    width: 30px; }

  .navbar .navbar-nav li.menu-item-has-children .sub-menu {
    padding: 0 0 0 30px; }

  .navbar .navbar-nav li.menu-item-has-children .sub-menu a {
    padding: 2px 0 2px 25px; }

  .card .card-header {
    position: relative; }
    .card .card-header strong {
      display: block; }
    .card .card-header small {
      float: left; }
    .card .card-header .card-actions {
      right: 0;
      top: .75rem;
      position: absolute; } }
@media (max-width: 992px) {
  [class*="col"].no-padding {
    flex: none; } }
@media (max-width: 575.99px) {
  body {
    display: block; }

  aside.left-panel {
    display: block;
    height: auto;
    min-height: inherit;
    padding: 0 15px;
    width: 100%; }
    aside.left-panel .navbar {
      margin-bottom: 0; }
      aside.left-panel .navbar .navbar-header {
        height: 50px; }
      aside.left-panel .navbar .navbar-brand {
        border-bottom: none;
        display: inline-block;
        float: left;
        line-height: 1;
        margin-top: 11px;
        min-height: inherit; }
        aside.left-panel .navbar .navbar-brand.hidden {
          display: none; }
      aside.left-panel .navbar .navbar-toggler {
        float: right;
        margin-top: 8px; }
      aside.left-panel .navbar .navbar-nav li > a {
        padding: 5px 0; }
      aside.left-panel .navbar .navbar-nav li.menu-item-has-children a:before {
        top: 5px; }
      aside.left-panel .navbar .menu-title {
        line-height: 30px;
        padding: 0; }

  .menutoggle {
    display: none; }

  .right-panel {
    display: block; }
    .right-panel header.header {
      padding: 5px 10px 1px 5px; }
      .right-panel header.header div[class*="col"] {
        padding: 0;
        width: initial; }
      .right-panel header.header .col-sm-7 {
        float: left; }
      .right-panel header.header .col-sm-5 {
        float: right; }
    .right-panel .breadcrumbs {
      padding: 10px 15px; }
      .right-panel .breadcrumbs div[class*="col"] {
        padding: 0; }
      .right-panel .breadcrumbs .page-header {
        min-height: inherit;
        padding: 0; }
        .right-panel .breadcrumbs .page-header h1 {
          padding: 5px 0; }
        .right-panel .breadcrumbs .page-header.float-right {
          float: left;
          text-align: left;
          width: 100%; }
          .right-panel .breadcrumbs .page-header.float-right .breadcrumb {
            padding: 0; }
    .right-panel .content {
      padding: 0 10px; }
      .right-panel .content .card .card-title {
        margin-bottom: 0; }
      .right-panel .content .card .card-footer {
        padding: 15px 5px; }
        .right-panel .content .card .card-footer ul {
          margin: 0; }
          .right-panel .content .card .card-footer ul li {
            float: left;
            margin: 5px 0;
            padding: 0 10px;
            width: 33.33%; }
      .right-panel .content div[class*="col"] {
        padding: 0; }
      .right-panel .content .row div[class*="col"] {
        padding: 0 10px; }
      .right-panel .content .nav-tabs .nav-link,
      .right-panel .content .nav-pills .nav-link {
        padding: .5rem; }
      .right-panel .content .tab-content.pl-3 {
        padding-left: 0 !important; }
      .right-panel .content #bootstrap-data-table_wrapper {
        font-size: 14px;
        padding: 0; }
        .right-panel .content #bootstrap-data-table_wrapper div[class*="col"] {
          padding: 0; }
        .right-panel .content #bootstrap-data-table_wrapper .table td, .right-panel .content #bootstrap-data-table_wrapper .table th {
          padding: 5px; } }

/*# sourceMappingURL=style.css.map */

The file style.css.map contains:
{
"version": 3,
"mappings": "AAAA;;;;;;;;;;;4DAW4D;ACG5D,eAAe;ADGP,+BAAoB;AEjB5B,aAAc;EACZ,QAAQ,EAAE,QAAQ;EAClB,MAAM,EAAE,MAAM;EACd,4BAAe;IACb,KAAK,EAAE,eAAe;IACtB,MAAM,EAAE,eAAe;EAEzB,+CAAsB;IACpB,GAAG,EAAE,GAAG;IACR,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,MAAM;EAEpB,eAAE;IACA,QAAQ,EAAE,QAAQ;IAClB,IAAI,EAAE,CAAC;IACP,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE,IAAI;EAGf,2BAAO;IACL,OAAO,EAAE,KAAK;IACd,UAAU,EAAE,KAAK;EAEnB,0BAAM;IACJ,OAAO,EAAE,KAAK;IACd,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE,IAAI;IACf,WAAW,EAAE,GAAG;IAChB,KAAK,EAAE,OAAO;IACd,cAAc,EAAE,SAAS;EAG7B,uBAAY;IACV,QAAQ,EAAE,QAAQ;IAClB,8BAAO;MACL,QAAQ,EAAE,QAAQ;MAClB,UAAU,EAAE,IAAI;MAChB,SAAS,EAAE,IAAI;MACf,WAAW,EAAE,IAAI;;ACwNvB,sBAAuB;EA7OrB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,YAAY;EACrB,cAAc,EAAE,GAAG;EACnB,KAAK,EA0LQ,IAAI;EAzLjB,MAAM,EA0LQ,IAAI;EAzLlB,gBAAgB,EAAE,WAAW;EAC7B,MAAM,EAAE,OAAO;EAEf,oCAAc;IACZ,QAAQ,EAAE,QAAQ;IAClB,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,OAAO,EAAE,CAAC;EAGZ,oCAAc;IACZ,QAAQ,EAAE,QAAQ;IAClB,OAAO,EAAE,KAAK;IACd,MAAM,EAAE,OAAO;IAIf,SAAS,EAyKM,IAAI;IAxKnB,WAAW,EAAE,GAAG;IAChB,cAAc,EAAE,SAAS;IAIvB,gBAAgB,EAAE,IAAI;IAExB,MAAM,EAAE,iBAAuB;IAC/B,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,gCAAgC;EA4B9C,oEAA8C;IAC5C,OAAO,EAAE,CAAC;EAEZ,mEAA6C;IAC3C,OAAO,EAAE,CAAC;EAGZ,qCAAe;IACb,QAAQ,EAAE,QAAQ;IAClB,GAAG,EA4HS,GAAG;IA3Hf,IAAI,EA2HQ,GAAG;IA1Hf,KAAK,EAAE,IAA4B;IACnC,MAAM,EAAE,IAA4B;IACpC,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,iBAAuB;IAC/B,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,kBAAkB;EAOhC,6DAAuC;IACrC,IAAI,EAAE,IAAiC;EAWvC,gCAAY;IAjHd,KAAK,EA2MW,IAAI;IA1MpB,MAAM,EA2MW,IAAI;IAzMrB,8CAAc;MACZ,SAAS,EAyMS,IAAI;IAtMxB,+CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,uEAAuC;MACrC,IAAI,EAAE,IAAiC;EAuGvC,gCAAY;IApHd,KAAK,EAqNW,IAAI;IApNpB,MAAM,EAqNW,IAAI;IAnNrB,8CAAc;MACZ,SAAS,EAmNS,GAAG;IAhNvB,+CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,uEAAuC;MACrC,IAAI,EAAE,IAAiC;EA0GvC,gCAAY;IAvHd,KAAK,EAyNW,IAAI;IAxNpB,MAAM,EAyNW,IAAI;IAvNrB,8CAAc;MACZ,SAAS,EAuNS,GAAG;IApNvB,+CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,uEAAuC;MACrC,IAAI,EAAE,IAAiC;;AAsP3C,mBAAoB;EAjPlB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,YAAY;EACrB,cAAc,EAAE,GAAG;EACnB,KAAK,EA6Ma,IAAI;EA5MtB,MAAM,EA6Ma,IAAI;EA5MvB,gBAAgB,EAAE,WAAW;EAC7B,MAAM,EAAE,OAAO;EAEf,iCAAc;IACZ,QAAQ,EAAE,QAAQ;IAClB,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,OAAO,EAAE,CAAC;EAGZ,iCAAc;IACZ,QAAQ,EAAE,QAAQ;IAClB,OAAO,EAAE,KAAK;IACd,MAAM,EAAE,OAAO;IAIf,SAAS,EA4LW,IAAI;IA3LxB,WAAW,EAAE,GAAG;IAChB,cAAc,EAAE,SAAS;IAIvB,gBAAgB,EAAE,IAAI;IAExB,MAAM,EAAE,iBAAuB;IAC/B,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,gCAAgC;EAG5C;0CACqB;IACnB,QAAQ,EAAE,QAAQ;IAClB,GAAG,EAAE,GAAG;IACR,KAAK,EAAE,GAAG;IACV,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,CAAC;IACd,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,OAAO;EAErB,yCAAsB;IACpB,KAAK,EAAE,GAAG;IACV,KAAK,EF9B2B,OAAS;IE+BzC,OAAO,EAAE,cAAc;EAEzB,wCAAqB;IACnB,IAAI,EAAE,GAAG;IACT,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE,CAAC;EAMd,iEAA8C;IAC5C,OAAO,EAAE,CAAC;EAEZ,gEAA6C;IAC3C,OAAO,EAAE,CAAC;EAGZ,kCAAe;IACb,QAAQ,EAAE,QAAQ;IAClB,GAAG,EA4HS,GAAG;IA3Hf,IAAI,EA2HQ,GAAG;IA1Hf,KAAK,EAAE,IAA4B;IACnC,MAAM,EAAE,IAA4B;IACpC,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,iBAAuB;IAC/B,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,kBAAkB;EAOhC,0DAAuC;IACrC,IAAI,EAAE,IAAiC;EAuBvC,6BAAY;IA7Hd,KAAK,EA8NgB,IAAI;IA7NzB,MAAM,EA8NgB,IAAI;IA5N1B,2CAAc;MACZ,SAAS,EA4Nc,IAAI;IAzN7B,4CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,oEAAuC;MACrC,IAAI,EAAE,IAAiC;EAmHvC,6BAAY;IAhId,KAAK,EAsOgB,IAAI;IArOzB,MAAM,EAsOgB,IAAI;IApO1B,2CAAc;MACZ,SAAS,EAoOc,GAAG;IAjO5B,4CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,oEAAuC;MACrC,IAAI,EAAE,IAAiC;EAsHvC,6BAAY;IAnId,KAAK,EA0OgB,IAAI;IAzOzB,MAAM,EA0OgB,IAAI;IAxO1B,2CAAc;MACZ,SAAS,EAwOc,GAAG;IArO5B,4CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,oEAAuC;MACrC,IAAI,EAAE,IAAiC;;AA0P3C,mBAAoB;EArPlB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,YAAY;EACrB,cAAc,EAAE,GAAG;EACnB,KAAK,EA8Na,IAAI;EA7NtB,MAAM,EA8Na,IAAI;EA7NvB,gBAAgB,EAAE,WAAW;EAC7B,MAAM,EAAE,OAAO;EAEf,iCAAc;IACZ,QAAQ,EAAE,QAAQ;IAClB,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,OAAO,EAAE,CAAC;EAGZ,iCAAc;IACZ,QAAQ,EAAE,QAAQ;IAClB,OAAO,EAAE,KAAK;IACd,MAAM,EAAE,OAAO;IAEb,WAAW,EAAE,WAAW;IAE1B,SAAS,EA6MW,IAAI;IA5MxB,WAAW,EAAE,GAAG;IAChB,cAAc,EAAE,SAAS;IAIvB,gBAAgB,EAAE,IAAI;IAExB,MAAM,EAAE,iBAAuB;IAC/B,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,gCAAgC;EAG5C;0CACqB;IACnB,QAAQ,EAAE,QAAQ;IAClB,GAAG,EAAE,GAAG;IACR,KAAK,EAAE,GAAG;IACV,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,CAAC;IACd,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,OAAO;EAErB,yCAAsB;IACpB,KAAK,EAAE,GAAG;IACV,KAAK,EF9B2B,OAAS;IE+BzC,OAAO,EAAE,cAAc;EAEzB,wCAAqB;IACnB,IAAI,EAAE,GAAG;IACT,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE,CAAC;EAMd,iEAA8C;IAC5C,OAAO,EAAE,CAAC;EAEZ,gEAA6C;IAC3C,OAAO,EAAE,CAAC;EAGZ,kCAAe;IACb,QAAQ,EAAE,QAAQ;IAClB,GAAG,EA4HS,GAAG;IA3Hf,IAAI,EA2HQ,GAAG;IA1Hf,KAAK,EAAE,IAA4B;IACnC,MAAM,EAAE,IAA4B;IACpC,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,iBAAuB;IAC/B,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,kBAAkB;EAOhC,0DAAuC;IACrC,IAAI,EAAE,IAAiC;EAmCvC,6BAAY;IAzId,KAAK,EA+OgB,IAAI;IA9OzB,MAAM,EA+OgB,IAAI;IA7O1B,2CAAc;MACZ,SAAS,EA6Oc,IAAI;IA1O7B,4CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,oEAAuC;MACrC,IAAI,EAAE,IAAiC;EA+HvC,6BAAY;IA5Id,KAAK,EAuPgB,IAAI;IAtPzB,MAAM,EAuPgB,IAAI;IArP1B,2CAAc;MACZ,SAAS,EAqPc,GAAG;IAlP5B,4CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,oEAAuC;MACrC,IAAI,EAAE,IAAiC;EAkIvC,6BAAY;IA/Id,KAAK,EA2PgB,IAAI;IA1PzB,MAAM,EA2PgB,IAAI;IAzP1B,2CAAc;MACZ,SAAS,EAyPc,GAAG;IAtP5B,4CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,oEAAuC;MACrC,IAAI,EAAE,IAAiC;;AA8P3C,iBAAkB;EAzPhB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,YAAY;EACrB,cAAc,EAAE,GAAG;EACnB,KAAK,EA0LQ,IAAI;EAzLjB,MAAM,EA0LQ,IAAI;EAzLlB,gBAAgB,EAAE,WAAW;EAC7B,MAAM,EAAE,OAAO;EAEf,+BAAc;IACZ,QAAQ,EAAE,QAAQ;IAClB,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,OAAO,EAAE,CAAC;EAGZ,+BAAc;IACZ,QAAQ,EAAE,QAAQ;IAClB,OAAO,EAAE,KAAK;IACd,MAAM,EAAE,OAAO;IAIf,SAAS,EAyKM,IAAI;IAxKnB,WAAW,EAAE,GAAG;IAChB,cAAc,EAAE,SAAS;IAEvB,gBAAgB,EFmEN,OAAS;IE/DrB,MAAM,EAAE,iBAAuB;IAC/B,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,gCAAgC;EA4B9C,+DAA8C;IAC5C,OAAO,EAAE,CAAC;EAEZ,8DAA6C;IAC3C,OAAO,EAAE,CAAC;EAGZ,gCAAe;IACb,QAAQ,EAAE,QAAQ;IAClB,GAAG,EAqLoE,CAAC;IApLxE,IAAI,EAoLmE,CAAC;IAnLxE,KAAK,EAAE,IAA4B;IACnC,MAAM,EAAE,IAA4B;IACpC,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,iBAAuB;IAC/B,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,kBAAkB;IAE5B,MAAM,EAAE,CAAC;IACT,UAAU,EAAE,4BAA2B;EAI3C,wDAAuC;IACrC,IAAI,EAAE,IAAiC;EA+CvC,2BAAY;IArJd,KAAK,EA2MW,IAAI;IA1MpB,MAAM,EA2MW,IAAI;IAzMrB,yCAAc;MACZ,SAAS,EAyMS,IAAI;IAtMxB,0CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,kEAAuC;MACrC,IAAI,EAAE,IAAiC;EA2IvC,2BAAY;IAxJd,KAAK,EAqNW,IAAI;IApNpB,MAAM,EAqNW,IAAI;IAnNrB,yCAAc;MACZ,SAAS,EAmNS,GAAG;IAhNvB,0CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,kEAAuC;MACrC,IAAI,EAAE,IAAiC;EA8IvC,2BAAY;IA3Jd,KAAK,EAyNW,IAAI;IAxNpB,MAAM,EAyNW,IAAI;IAvNrB,yCAAc;MACZ,SAAS,EAuNS,GAAG;IApNvB,0CAAe;MACb,KAAK,EAAE,IAA4B;MACnC,MAAM,EAAE,IAA4B;IAGtC,kEAAuC;MACrC,IAAI,EAAE,GAAiC;;AAoQzC;;gCACe;EACb,aAAa,EAAE,eAAe;AAGhC,2EAAsB;EACpB,KAAK,EAAE,cAAc;AAEvB,yEAAqB;EACnB,IAAI,EAAE,cAAc;;AAxHtB,uDAAwC;EACtC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EAAE,OAAkB;AAGlC,wDAAyC;EACvC,YAAY,EAAE,OAAkB;;AAKlC,+DAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFtEA,OAAK;EEwEjB,sEAAS;IACP,KAAK,EFzEK,OAAK;AE6EnB,gEAAyC;EACvC,YAAY,EF9EA,OAAK;;AEmFnB,mEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFrFA,OAAK;EEuFjB,0EAAS;IACP,KAAK,EFxFK,OAAK;AE4FnB,oEAAyC;EACvC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EF9FA,OAAK;;AEyDnB,yDAAwC;EACtC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EAAE,OAAkB;AAGlC,0DAAyC;EACvC,YAAY,EAAE,OAAkB;;AAKlC,iEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFrEA,OAAS;EEuErB,wEAAS;IACP,KAAK,EFxEK,OAAS;AE4EvB,kEAAyC;EACvC,YAAY,EF7EA,OAAS;;AEkFvB,qEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFpFA,OAAS;EEsFrB,4EAAS;IACP,KAAK,EFvFK,OAAS;AE2FvB,sEAAyC;EACvC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EF7FA,OAAS;;AEwDvB,uDAAwC;EACtC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EAAE,OAAkB;AAGlC,wDAAyC;EACvC,YAAY,EAAE,OAAkB;;AAKlC,+DAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFpEA,OAAM;EEsElB,sEAAS;IACP,KAAK,EFvEK,OAAM;AE2EpB,gEAAyC;EACvC,YAAY,EF5EA,OAAM;;AEiFpB,mEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFnFA,OAAM;EEqFlB,0EAAS;IACP,KAAK,EFtFK,OAAM;AE0FpB,oEAAyC;EACvC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EF5FA,OAAM;;AEuDpB,oDAAwC;EACtC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EAAE,OAAkB;AAGlC,qDAAyC;EACvC,YAAY,EAAE,OAAkB;;AAKlC,4DAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFnEA,OAAK;EEqEjB,mEAAS;IACP,KAAK,EFtEK,OAAK;AE0EnB,6DAAyC;EACvC,YAAY,EF3EA,OAAK;;AEgFnB,gEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFlFA,OAAK;EEoFjB,uEAAS;IACP,KAAK,EFrFK,OAAK;AEyFnB,iEAAyC;EACvC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EF3FA,OAAK;;AEsDnB,uDAAwC;EACtC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EAAE,OAAkB;AAGlC,wDAAyC;EACvC,YAAY,EAAE,OAAkB;;AAKlC,+DAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFlEA,OAAO;EEoEnB,sEAAS;IACP,KAAK,EFrEK,OAAO;AEyErB,gEAAyC;EACvC,YAAY,EF1EA,OAAO;;AE+ErB,mEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFjFA,OAAO;EEmFnB,0EAAS;IACP,KAAK,EFpFK,OAAO;AEwFrB,oEAAyC;EACvC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EF1FA,OAAO;;AEqDrB,sDAAwC;EACtC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EAAE,OAAkB;AAGlC,uDAAyC;EACvC,YAAY,EAAE,OAAkB;;AAKlC,8DAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFjEA,OAAI;EEmEhB,qEAAS;IACP,KAAK,EFpEK,OAAI;AEwElB,+DAAyC;EACvC,YAAY,EFzEA,OAAI;;AE8ElB,kEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFhFA,OAAI;EEkFhB,yEAAS;IACP,KAAK,EFnFK,OAAI;AEuFlB,mEAAyC;EACvC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EFzFA,OAAI;;AEoDlB,qDAAwC;EACtC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EAAE,OAAkB;AAGlC,sDAAyC;EACvC,YAAY,EAAE,OAAkB;;AAKlC,6DAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EFhEA,OAAS;EEkErB,oEAAS;IACP,KAAK,EFnEK,OAAS;AEuEvB,8DAAyC;EACvC,YAAY,EFxEA,OAAS;;AE6EvB,iEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EF/EA,OAAS;EEiFrB,wEAAS;IACP,KAAK,EFlFK,OAAS;AEsFvB,kEAAyC;EACvC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EFxFA,OAAS;;AEmDvB,oDAAwC;EACtC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EAAE,OAAkB;AAGlC,qDAAyC;EACvC,YAAY,EAAE,OAAkB;;AAKlC,4DAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EF/DA,OAAS;EEiErB,mEAAS;IACP,KAAK,EFlEK,OAAS;AEsEvB,6DAAyC;EACvC,YAAY,EFvEA,OAAS;;AE4EvB,gEAAwC;EACtC,UAAU,EAAE,eAAe;EAC3B,YAAY,EF9EA,OAAS;EEgFrB,uEAAS;IACP,KAAK,EFjFK,OAAS;AEqFvB,iEAAyC;EACvC,UAAU,EAAE,kBAAiB;EAC7B,YAAY,EFvFA,OAAS;;AGhHzB,WAAY;EACV,UAAU,EAAE,KAAK;EACjB,aAAa,EAAE,MAAU;EACzB,UAAU,EAAE,MAAM;EAClB,UAAU,EAAE,IAAI;EAIhB,aAAE;IACA,OAAO,EAAE,KAAK;IACd,MAAM,EAAE,WAAW;IACnB,SAAS,EAAE,IAAI;IACf,WAAW,EAAE,IAAI;IACjB,UAAU,EAAE,OAAO;EAKrB,0BAAe;IACb,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,SAAS;IAEjB,iCAAO;MACL,KAAK,EAAE,eAAe;MACtB,MAAM,EAAE,eAAe;EAI3B,cAAG;IACD,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,IAAI;IAGhB,iBAAG;MACD,OAAO,EAAE,KAAK;MACd,KAAK,EAAE,IAAI;MACX,KAAK,EAAE,GAAG;MACV,WAAW,EAAE,IAAI;MACjB,SAAS,EAAE,IAAI;MAEf,6BAAc;QACZ,YAAY,EAAE,iBAAiB;MAGjC,wBAAO;QACL,OAAO,EAAE,KAAK;QACd,SAAS,EAAE,IAAI;MAGjB,sBAAK;QACH,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,GAAG;QAChB,KAAK,EAAE,OAAO;QACd,cAAc,EAAE,SAAS;EAM7B,sBAAE;IACA,KAAK,EAAE,IAAI;IACX,UAAU,EHdsB,OAAO;EGmBzC,qBAAE;IACA,KAAK,EAAE,IAAI;IACX,UAAU,EHpBsB,OAAO;EGyBzC,sBAAE;IACA,KAAK,EAAE,IAAI;IACX,UAAU,EH1BsB,OAAO;EG+BzC,yBAAE;IACA,KAAK,EAAE,IAAI;IACX,UAAU,EHhCsB,OAAO;;AGqC7C,gBAAiB;EACf,OAAO,EAAE,CAAC;EACV,MAAM,EAAE,CAAC;EACT,UAAU,EAAE,IAAI;EAEhB,mBAAG;IACD,QAAQ,EAAE,QAAQ;IAClB,MAAM,EAAE,IAAI;IACZ,WAAW,EAAE,IAAI;IACjB,cAAc,EAAE,MAAM;IAEtB,0BAAO;MACL,KAAK,EAAE,KAAK;MACZ,SAAS,EAAE,IAAI;MACf,WAAW,EAAE,GAAG;MAChB,KAAK,EHIK,OAAS;MGHnB,cAAc,EAAE,MAAM;IAGxB,yBAAM;MACJ,QAAQ,EAAE,QAAQ;MAClB,GAAG,EAAE,IAAI;MACT,KAAK,EAAE,IAAI;MACX,YAAY,EAAE,KAAK;MAEnB,+CAAsB;QACpB,aAAa,EAAE,GAAG;IAItB,0BAAS;MACP,UAAU,EAAE,MAAM;MAElB,iCAAO;QACL,OAAO,EAAE,YAAY;QACrB,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,OAAO,EAAE,CAAC;IAId,2BAAU;MACR,MAAM,EAAE,IAAI;MAEZ,6BAAE;QACA,MAAM,EAAE,YAAY;EAOxB,0BAAG;IACD,QAAQ,EAAE,MAAM;IAEhB,4BAAE;MACA,OAAO,EAAE,YAAY;MACrB,YAAY,EHlEX,IAAI;MGmEL,WAAW,EAAE,GAAG;MAChB,SAAS,EAAE,IAAI;MACf,WAAW,EAAE,IAAI;IAGnB,iCAAO;MACL,OAAO,EAAE,YAAY;MACrB,KAAK,EAAE,IAAI;MACX,UAAU,EAAE,IAAI;MAChB,SAAS,EHlEa,IAAI;MGmE1B,WAAW,EAAE,MAAM;MACnB,WAAW,EAAE,IAAI;MACjB,KAAK,EHhEe,OAAS;IGmE/B,iCAAO;MACL,KAAK,EAAE,KAAK;MACZ,WAAW,EAAE,GAAG;IAGlB,gCAAM;MACJ,QAAQ,EAAE,QAAQ;MAClB,GAAG,EAAE,IAAI;MACT,MAAM,EAAE,CAAC;MACT,OAAO,EAAE,CAAC;;AAMlB,WAAY;EACV,OAAO,EAAE,CAAC;EACV,MAAM,EAAE,CAAC;EACT,UAAU,EAAE,IAAI;EAEhB,cAAG;IACD,QAAQ,EAAE,QAAQ;IAClB,MAAM,EAAE,IAAI;IACZ,cAAc,EAAE,MAAM;IAEtB,gBAAE;MACA,OAAO,EAAE,KAAK;MACd,KAAK,EAAE,IAAI;MACX,KAAK,EAAE,eAAe;MACtB,MAAM,EAAE,eAAe;MACvB,MAAM,EAAE,GAAG;MACX,WAAW,EAAE,eAAe;MAC5B,UAAU,EAAE,MAAM;IAGpB,oBAAM;MACJ,MAAM,EAAE,IAAI;MACZ,WAAW,EAAE,IAAI;MACjB,aAAa,EAAE,iBAAuB;MAEtC,2BAAO;QACL,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,CAAC;MAEX,0BAAM;QACJ,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,IAAI;QAChB,KAAK,EHrGG,OAAS;IGyGrB,qBAAO;MACL,QAAQ,EAAE,QAAQ;MAClB,GAAG,EAAE,GAAG;MACR,KAAK,EAAE,IAAI;MACX,UAAU,EAAE,KAAK;MAEjB,4BAAO;QACL,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,IAAI;IAIpB,uBAAS;MACP,QAAQ,EAAE,QAAQ;MAClB,GAAG,EAAE,IAAI;MACT,KAAK,EAAE,IAAI;MACX,KAAK,EAAE,IAAI;MACX,MAAM,EAAE,IAAI;MACZ,WAAW,EAAE,IAAI;MACjB,UAAU,EAAE,MAAM;MAElB,yBAAE;QACA,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,CAAC;QACV,MAAM,EAAE,CAAC;QACT,WAAW,EAAE,MAAM;IAIvB,sBAAU;MACR,MAAM,EAAE,IAAI;MAEZ,wBAAE;QACA,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,OAAO;QACf,SAAS,EAAE,IAAI;;AJvOvB,gBAAiB;EAAC,UAAU,EAAE,OAAO;;AACrC,gBAAiB;EAAC,UAAU,EAAE,OAAO;;AACrC,gBAAiB;EAAC,UAAU,EAAE,OAAO;;AACrC,gBAAiB;EAAC,UAAU,EAAE,OAAO;;AACrC,gBAAiB;EAAC,UAAU,EAAE,OAAO;;AAGrC,WAAY;EACR,kBAAkB,EAAE,aAAa;EACjC,eAAe,EAAE,aAAa;EAC9B,cAAc,EAAE,aAAa;EAC7B,aAAa,EAAE,aAAa;EAC5B,UAAU,EAAE,aAAa;;AAG7B,IAAK;EACD,UAAU,ECnCC,OAAO;EDoClB,OAAO,EAAE,KAAK;EACd,WAAW,EAAE,iCAAiC;EAC9C,SAAS,EAAE,IAAI;EACf,KAAK,EAAE,IAAI;;AAGf,kBAAmB;EACf,KAAK,EAAE,IAAI;;AAGf,CAAE;EACE,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,sBAAsB;EACnC,WAAW,EAAE,GAAG;EAChB,WAAW,EAAE,IAAI;EACjB,KAAK,EAAE,OAAO;;AAElB,OAAQ;EACJ,MAAM,EAAE,IAAI;EACZ,OAAO,EAAE,CAAC;;AAEd,SAAU;EACN,eAAe,EAAE,IAAI;EACrB,OAAO,EAAE,eAAc;EACvB,KAAK,EAAE,OAAO;EAEd,kBAAkB,EAAE,cAAc;EAClC,eAAe,EAAE,cAAc;EAC/B,cAAc,EAAE,cAAc;EAC9B,aAAa,EAAE,cAAc;EAC7B,UAAU,EAAE,cAAc;;AAG9B;OACQ;EACJ,eAAe,EAAE,IAAI;EACrB,KAAK,EAAE,IAAI;;AAGf;;;;;EAKG;EACC,MAAM,EAAE,CAAC;;AAEb;EACG;EACC,YAAY,EAAE,CAAC;;AAEnB;YACa;EACT,UAAU,EAAE,eAAe;EAC3B,OAAO,EAAE,CAAC;;AAEd,GAAI;EACA,SAAS,EAAE,IAAI;;AAEnB;;;QAGS;EACL,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,YAAY;;AAEzB,WAAY;EACR,OAAO,EAAE,YAAY;;AAGzB,mBAAmB;AAEnB,iBAAiB;AACjB,gBAAiB;EACb,OAAO,EAAE,KAAK;EACd,UAAU,EAAE,KAAK;EACjB,QAAQ,EAAE,QAAQ;EAClB,KAAK,EAAE,IAAI;;AAEf,gBAAiB;EACb,UAAU,ECnHJ,OAAO;EDoHb,OAAO,EAAE,UAAU;EACnB,MAAM,EAAE,KAAK;EACb,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,MAAM;EACf,cAAc,EAAE,GAAG;EACnB,KAAK,EAAE,KAAK;EACZ,UAAU,EAAE,eAAe;;AAG/B,OAAQ;EACJ,UAAU,EAAE,OAAO;EACnB,aAAa,EAAE,CAAC;EAChB,MAAM,EAAE,IAAI;EACZ,OAAO,EAAE,KAAK;EACd,MAAM,EAAE,CAAC;EACT,aAAa,EAAE,KAAK;EACpB,OAAO,EAAE,CAAC;EAEV,sBAAe;IACX,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,MAAM;IAClB,KAAK,EAAE,IAAI;EAEf,qBAAc;IACV,aAAa,EAAE,iBAAiB;IAChC,KAAK,EAAE,kBAAkB;IACzB,WAAW,EAAE,WAAW;IACxB,SAAS,EAAE,IAAI;IACf,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,IAAI;IACjB,MAAM,EAAE,CAAC;IACT,UAAU,EAAE,IAAI;IAChB,cAAc,EAAE,UAAU;IAC1B,OAAO,EAAE,KAAK;IACd,UAAU,EAAE,IAAI;IAChB,OAAO,EAAE,CAAC;IACV,OAAO,EAAE,IAAI;IACb,eAAe,EAAE,MAAM;IACvB,WAAW,EAAE,MAAM;IACnB,QAAQ,EAAE,QAAQ;IAClB,0BAAK;MACD,WAAW,EAAE,GAAG;IAEpB,yBAAI;MACA,SAAS,EAAE,KAAK;IAEpB,4BAAS;MACL,OAAO,EAAE,IAAI;EAIrB,mBAAY;IACR,aAAa,EAAE,iBAAiB;IAChC,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,KAAK;IACd,WAAW,EAAE,WAAW;IACxB,SAAS,EAAE,IAAI;IACf,WAAW,EAAE,GAAG;IAChB,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,UAAU;IACnB,cAAc,EAAE,SAAS;IACzB,KAAK,EAAE,IAAI;EAEf,mBAAY;IACR,KAAK,EAAE,IAAI;IACX,QAAQ,EAAE,QAAQ;IAClB,sBAAG;MACC,KAAK,EAAE,IAAI;MACX;+DAEmC;QAC/B,KAAK,EAAE,eAAe;MAE1B,6CAAuB;QACnB,OAAO,EAAE,IAAI;MAEjB,0BAAG;QACC,UAAU,EAAE,eAAe;QAC3B,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE,YAAY;QACrB,WAAW,EAAE,WAAW;QACxB,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE,QAAQ;QAClB,KAAK,EAAE,IAAI;QACX,6EACmB;UACf,KAAK,EAAE,eAAe;QAE1B,qCAAW;UACP,KAAK,EAAE,OAAO;UACd,KAAK,EAAE,IAAI;UACX,UAAU,EAAE,GAAG;UACf,KAAK,EAAE,IAAI;UACX,UAAU,EAAE,IAAI;UAChB,OAAO,EAAE,CAAC;QAEd,2CAAiB;UACb,SAAS,EAAE,IAAI;QAEnB,iCAAO;UACH,aAAa,EAAE,CAAC;UAChB,WAAW,EAAE,WAAW;UACxB,WAAW,EAAE,GAAG;UAChB,KAAK,EAAE,KAAK;UACZ,MAAM,EAAE,SAAS;UACjB,OAAO,EAAE,WAAW;MAG5B,6CAAyB;QACrB,QAAQ,EAAE,QAAQ;QAClB,+CAAE;UACE,WAAW,EAAE,IAAI;UACjB,sDAAS;YACL,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,OAAO;YACd,WAAW,EAAE,aAAa;YAC1B,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,QAAQ;YAClB,GAAG,EAAE,IAAI;YACT,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,KAAK;YAEjB,kBAAkB,EAAE,aAAa;YACjC,eAAe,EAAE,aAAa;YAC9B,cAAc,EAAE,aAAa;YAC7B,aAAa,EAAE,aAAa;YAC5B,UAAU,EAAE,aAAa;UAE7B,4DAAe;YACX,KAAK,EAAE,IAAI;QAGnB,uDAAU;UACN,UAAU,EC5PpB,OAAO;UD6PG,MAAM,EAAE,IAAI;UACZ,UAAU,EAAE,IAAI;UAChB,UAAU,EAAE,MAAM;UAClB,OAAO,EAAE,UAAU;UACnB,0DAAG;YACC,QAAQ,EAAE,QAAQ;UAEtB,yDAAE;YACE,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,IAAI;YACX,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,CAAC;YACP,SAAS,EAAE,IAAI;YACf,GAAG,EAAE,GAAG;UAEZ,yDAAE;YACE,OAAO,EAAE,cAAc;YACvB,gEAAS;cACL,OAAO,EAAE,EAAE;cACX,OAAO,EAAE,IAAI;YAEjB,oEAAW;cACP,GAAG,EAAE,IAAI;cACT,UAAU,EAAE,IAAI;cAChB,KAAK,EAAE,IAAI;QAKnB,2DAAS;UACL,OAAO,EAAE,OAAO;QAEpB,4DAAU;UACN,UAAU,EAAE,MAAM;UAClB,OAAO,EAAE,CAAC;UACV,QAAQ,EAAE,iBAAiB;;AAOnD;;uCAEoC;EAChC,KAAK,EAAE,kBAAkB;;AAE7B,yBAA0B;EACtB,UAAU,EAAE,OAAO;EACnB,aAAa,EAAE,GAAG;EAClB,KAAK,EAAE,IAAI;EACX,WAAW,EAAE,WAAW;EACxB,SAAS,EAAE,GAAG;EACd,WAAW,EAAE,GAAG;EAChB,KAAK,EAAE,KAAK;EACZ,MAAM,EAAE,IAAI;EACZ,KAAK,EAAE,IAAI;EACX,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,UAAU,EAAE,MAAM;;AAKV,sCAAS;EACL,OAAO,EAAE,KAAK;;AAK9B,sBAAuB;EACnB,SAAS,EAAE,IAAI;EACf,KAAK,EAAE,IAAI;EAEP,4CAAc;IACV,OAAO,EAAE,IAAI;IACb,mDAAS;MACL,OAAO,EAAE,eAAe;MACxB,eAAe,EAAE,MAAM;MACvB,WAAW,EAAE,MAAM;MACnB,YAAY,EAAE,CAAC;MACf,aAAa,EAAE,CAAC;MAChB,UAAU,EAAE,MAAM;MAClB,uDAAI;QACA,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,MAAM;IAGtB,sDAAY;MACR,OAAO,EAAE,gBAAgB;MACzB,MAAM,EAAE,QAAQ;MAChB,UAAU,EAAE,IAAI;MAChB,OAAO,EAAE,CAAC;MACV,UAAU,EAAE,MAAM;EAItB,iDAAS;IACL,OAAO,EAAE,eAAe;EAE5B,6CAAG;IACC,QAAQ,EAAE,QAAQ;IAClB,+CAAE;MACE,SAAS,EAAE,CAAC;MACZ,OAAO,EAAE,CAAC;MACV,UAAU,EAAE,IAAI;MAChB,0DAAW;QACP,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,EAAE;QACX,KAAK,EAAE,OAAO;MAElB,gEAAiB;QACb,SAAS,EAAE,CAAC;MAEhB,sDAAO;QACH,OAAO,EAAE,IAAI;IAGrB,iDAAG;MACC,SAAS,EAAE,IAAI;MACf,YAAY,EAAE,CAAC;IAEnB,oEAAyB;MACrB,QAAQ,EAAE,MAAM;MAEZ,6EAAS;QACL,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,IAAI;MAGrB,uEAAG;QACC,YAAY,EAAE,CAAC;MAEnB,8EAAU;QACN,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,OAAO;QACb,KAAK,EAAE,MAAM;QACb,GAAG,EAAE,CAAC;QAEF,mFAAE;UACE,OAAO,EAAE,KAAK;UACd,SAAS,EAAE,IAAI;UACf,SAAS,EAAE,OAAO;UAClB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,IAAI;UACX,8FAAW;YACP,UAAU,EAAE,MAAM;MAKlC,yEAAO;QACH,QAAQ,EAAE,OAAO;QACjB,mFAAU;UACN,QAAQ,EAAE,mBAAmB;IAIzC,wDAAW;MACP,OAAO,EAAE,IAAI;MACb,YAAY,EAAE,GAAG;MACjB,OAAO,EAAE,CAAC;IAGV,4DAAQ;MACJ,OAAO,EAAE,EAAE;MACX,OAAO,EAAE,IAAI;EAIzB,sDAAY;IACR,SAAS,EAAE,CAAC;IACZ,WAAW,EAAE,CAAC;IACd,OAAO,EAAE,CAAC;IACV,OAAO,EAAE,CAAC;;AAM1B,iBAAiB;AAEjB,YAAa;EACT,OAAO,EAAE,UAAU;EACnB,YAAY,EAAE,YAAY;EAE1B,kBAAkB,EAAE,aAAa;EACjC,eAAe,EAAE,aAAa;EAC9B,cAAc,EAAE,aAAa;EAC7B,aAAa,EAAE,aAAa;EAC5B,UAAU,EAAE,aAAa;EACzB,yBAAa;IACT,gBAAgB,EAAE,IAAI;IACtB,OAAO,EAAE,YAAY;IACrB,UAAU,EAAE,CAAC;IACb,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,IAAI;IAEP,gDAAa;MACT,KAAK,EAAE,IAAI;EAIvB,yBAAY;IACR,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,QAAQ;IACjB,UAAU,EAAE,OAAO;IACnB,aAAa,EAAE,GAAG;IAClB,4BAAE;MACE,SAAS,EAAE,IAAI;MACf,OAAO,EAAE,MAAM;IAEnB,qCAAW;MACP,MAAM,EAAE,GAAG;MACX,OAAO,EAAE,QAAQ;MACjB,UAAU,EAAE,IAAI;MAChB,cAAc,EAAE,UAAU;IAE9B,sDAAwB;MACpB,OAAO,EAAE,KAAK;MACd,KAAK,EAAE,IAAI;MACX,OAAO,EAAE,QAAQ;;AAI7B,0BAA2B;EACvB,UAAU,EC/dF,IAAI;EDgeZ,UAAU,EAAE,+BAA+B;EAC3C,KAAK,EAAE,IAAI;EACX,OAAO,EAAE,YAAY;EACrB,OAAO,EAAE,mBAAmB;EAC5B,KAAK,EAAE,IAAI;;AAEf,kBAAmB;EACf,WAAW,EAAE,MAAM;;AAEvB,gBAAiB;EACb,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;;AAEjB,sBAAuB;EACnB,QAAQ,EAAE,OAAO;;AAErB,WAAY;EACR,UAAU,EAAE,OAAO;EACnB,aAAa,EAAE,GAAG;EAClB,KAAK,EAAE,eAAe;EACtB,MAAM,EAAE,OAAO;EACf,SAAS,EAAE,IAAI;EACf,MAAM,EAAE,IAAI;EACZ,WAAW,EAAE,IAAI;EACjB,MAAM,EAAE,iBAAiB;EACzB,UAAU,EAAE,MAAM;EAClB,KAAK,EAAE,IAAI;;AAEf,0BAA2B;EACvB,OAAO,EAAE,OAAO;;AAEpB,eAAgB;EACZ,UAAU,EAAE,WAAW;EACvB,MAAM,EAAE,IAAI;EACZ,KAAK,ECjgBI,OAAO;EDkgBhB,MAAM,EAAE,OAAO;EACf,SAAS,EAAE,IAAI;EACf,MAAM,EAAE,IAAI;EACZ,KAAK,EAAE,IAAI;EACX,WAAW,EAAE,IAAI;;AAErB,mBAAoB;EAChB,UAAU,EAAE,OAAO;EACnB,OAAO,EAAE,IAAI;EACb,MAAM,EAAE,IAAI;EACZ,MAAM,EAAE,CAAC;EACT,KAAK,EAAE,IAAI;EACX,QAAQ,EAAE,QAAQ;EAClB,IAAI,EAAE,CAAC;EACP,GAAG,EAAE,CAAC;EACN,OAAO,EAAE,IAAI;EACb,gCAAa;IACT,MAAM,EAAE,IAAI;IACZ,SAAS,EAAE,MAAM;IACjB,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,QAAQ;IAClB,mDAAmB;MACf,UAAU,EAAE,OAAO;MACnB,MAAM,EAAE,IAAI;MACZ,aAAa,EAAE,CAAC;MAChB,UAAU,EAAE,IAAI;MAChB,KAAK,EAAE,OAAO;MACd,SAAS,EAAE,IAAI;MACf,MAAM,EAAE,OAAO;MACf,YAAY,EAAE,YAAY;MAC1B,OAAO,EAAE,mBAAmB;MAC5B,KAAK,EAAE,IAAI;IAEf;6DACyB;MACrB,YAAY,EAAE,oBAAoB;MAClC,OAAO,EAAE,CAAC;IAEd,uCAAO;MACH,UAAU,EAAE,WAAW;MACvB,MAAM,EAAE,IAAI;MACZ,KAAK,EAAE,IAAI;MACX,SAAS,EAAE,IAAI;MACf,QAAQ,EAAE,QAAQ;MAClB,KAAK,EAAE,IAAI;MACX,GAAG,EAAE,GAAG;MACR,UAAU,EAAE,gBAAgB;IAEhC;;;+DAG2B;MACvB,UAAU,EAAE,WAAW;MACvB,MAAM,EAAE,eAAe;MACvB,UAAU,EAAE,IAAI;MAChB,OAAO,EAAE,YAAY;IAEzB,sCAAQ;MACJ,OAAO,EAAE,IAAI;;AAIzB,8BAA+B;EAC3B,OAAO,EAAE,KAAK;;AAElB,sBAAuB;EACnB,OAAO,EAAE,YAAY;EACrB,uCAAiB;IACb,UAAU,EAAE,WAAW;IACvB,MAAM,EAAE,IAAI;IACZ,KAAK,ECxkBA,OAAO;IDykBZ,SAAS,EAAE,IAAI;IACf,6CAAQ;MACJ,OAAO,EAAE,IAAI;IAEjB,8CAAO;MACH,aAAa,EAAE,GAAG;MAClB,KAAK,EAAE,IAAI;MACX,SAAS,EAAE,IAAI;MACf,MAAM,EAAE,IAAI;MACZ,KAAK,EAAE,IAAI;MACX,WAAW,EAAE,IAAI;MACjB,KAAK,EAAE,CAAC;MACR,GAAG,EAAE,CAAC;MACN,QAAQ,EAAE,QAAQ;IAEtB,8IAEU;MACN,UAAU,EAAE,eAAe;MAC3B,YAAY,EAAE,sBAAsB;MACpC,KAAK,EAAE,kBAAkB;EAGjC,qCAAe;IACX,UAAU,EClmBN,IAAI;IDmmBR,MAAM,EAAE,IAAI;IACZ,aAAa,EAAE,CAAC;IAChB,UAAU,EAAE,IAAI;IAChB,GAAG,EAAE,eAAe;IACpB,uCAAE;MACE,SAAS,EAAE,IAAI;MACf,MAAM,EAAE,CAAC;MACT,OAAO,EAAE,QAAQ;IAErB,oDAAe;MACX,KAAK,EC5mBJ,OAAO;MD6mBR,SAAS,EAAE,IAAI;MACf,OAAO,EAAE,aAAa;MACtB,aAAa,EAAE,QAAQ;MACvB,2DAAO;QACH,KAAK,EAAE,IAAI;QACX,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,IAAI;MAGX,mEAAM;QACF,UAAU,EAAE,IAAI;MAEpB,mEAAM;QACF,SAAS,EAAE,IAAI;MAEnB,+DAAE;QACE,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,aAAa,EAAE,QAAQ;MAG/B,0DAAQ;QACJ,UAAU,EAAE,WAAW;;AAKvC,cAAe;EACX,aAAa,EAAE,CAAC;EAChB,SAAS,EAAE,eAAe;;AAKtB,+CAAe;EACX,OAAO,EAAE,mBAAmB;EAC5B,aAAa,EAAE,QAAQ;EACvB,iDAAE;IACE,KAAK,EAAE,IAAI;IACX,SAAS,EAAE,IAAI;IACf,MAAM,EAAE,WAAW;IACnB,UAAU,EAAE,IAAI;IAChB,KAAK,EAAE,IAAI;EAEf,iDAAE;IACE,OAAO,EAAE,YAAY;IACrB,aAAa,EAAE,QAAQ;;AAKvC,UAAW;EACP,KAAK,EAAE,KAAK;EACZ,aAAa,EAAE,CAAC;EAChB,QAAQ,EAAE,QAAQ;EAClB,qBAAW;IACP,UAAU,ECxqBN,IAAI;IDyqBR,MAAM,EAAE,IAAI;IACZ,WAAW,EAAE,WAAW;IACxB,IAAI,EAAE,kBAAkB;IACxB,KAAK,EAAE,CAAC;IACR,GAAG,EAAE,eAAe;IACpB,MAAM,EAAE,CAAC;IACT,SAAS,EAAE,KAAK;IAChB,OAAO,EAAE,QAAQ;IACjB,QAAQ,EAAE,QAAQ;IAClB,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,KAAK;IAChB,+BAAU;MACN,KAAK,ECrrBJ,OAAO;MDsrBR,OAAO,EAAE,KAAK;MACd,SAAS,EAAE,IAAI;MACf,WAAW,EAAE,IAAI;MACjB,OAAO,EAAE,KAAK;EAGtB,uBAAa;IACT,KAAK,EAAE,KAAK;IACZ,UAAU,EAAE,GAAG;IACf,KAAK,EAAE,IAAI;EAEf,2BAAiB;IACb,KAAK,EAAE,OAAO;IACd,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,QAAQ;IAClB,cAAc,EAAE,SAAS;EAE7B,iBAAO;IACH,UAAU,EAAE,OAAO;IACnB,aAAa,EAAE,GAAG;IAClB,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,WAAW;IACxB,SAAS,EAAE,GAAG;IACd,WAAW,EAAE,GAAG;IAChB,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,IAAI;IACjB,UAAU,EAAE,MAAM;EAEtB,iCAAuB;IACnB,OAAO,EAAE,IAAI;;AAGrB,YAAa;EACT,YAAY,EAAE,IAAI;;AAEtB,gBAAiB;EACb,KAAK,EAAE,OAAO;EACd,KAAK,EAAE,KAAK;EACZ,MAAM,EAAE,YAAY;EACpB,SAAS,EAAE,IAAI;EACf,gDACU;IACN,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,CAAC;EAEd,wCAAwB;IACpB,OAAO,EAAE,IAAI;EAEjB,+BAAe;IACX,UAAU,EC1uBN,IAAI;ID2uBR,MAAM,EAAE,IAAI;IACZ,aAAa,EAAE,CAAC;IAChB,IAAI,EAAE,eAAe;IACrB,SAAS,EAAE,OAAO;IAClB,OAAO,EAAE,KAAK;IACd,GAAG,EAAE,eAAe;IACpB,8CAAe;MACX,YAAY,EAAE,CAAC;MACf,SAAS,EAAE,IAAI;MACf,OAAO,EAAE,CAAC;MACV,oDAAQ;QACJ,UAAU,ECtvBd,IAAI;MDwvBJ,yDAAW;QACP,YAAY,EAAE,CAAC;QACf,KAAK,EAAE,IAAI;;AAK3B;;+BAEiC;EAC7B,OAAO,EAAE,KAAK;;AAElB,QAAS;EACL,KAAK,EAAE,IAAI;EACX,OAAO,EAAE,MAAM;EACf,KAAK,EAAE,IAAI;;AAEf,KAAK;EACD,aAAa,EAAE,MAAM;EACrB,aAAa,EAAE,CAAC;EAChB,QAAE;IACE,SAAS,EAAE,MAAM;EAIb,6BAAG;IACC,MAAM,EAAE,kCAA+B;IACvC,aAAa,EAAE,GAAG;IAClB,qBAAqB,EAAE,GAAG;EAM9B,uCAAM;IACF,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,QAAQ;IACjB,MAAM,EAAE,YAAW;IACnB,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,CAAC;IACV,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,WAAW;IACvB,MAAM,EAAE,CAAC;IACT,WAAW,EAAE,kCAA6B;EAItD,kBAAY;IACR,OAAO,EAAE,eAAe;IACxB,gBAAgB,EAAE,OAAO;IACzB,UAAU,EAAE,iBAAiB;IAE7B,wBAAQ;MACJ,OAAO,EAAE,UAAU;MACnB,OAAO,EAAE,MAAM;MACf,UAAU,EAAE,MAAM;;AAM9B,YAAa;EACT,UAAU,EAAE,CAAC;;AAGjB,UAAU;AAEN,kBAAQ;EACJ,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,OAAO;EACf,gBAAgB,EAAE,IAAI;EACtB,MAAM,EAAE,cAAc;EACtB,mBAAmB,EAAE,WAAW;AAGhC,kCAAe;EACX,GAAG,EAAE,eAAe;;AAIhC,wHAAyH;EACrH,YAAY,EAAE,uBAAuB;EACrC,KAAK,EAAE,OAAO;EACd,QAAQ,EAAE,QAAQ;;AAEtB,gJAAgJ;EAC5I,UAAU,EAAE,OAAO;EACnB,MAAM,EAAE,IAAI;EACZ,OAAO,EAAE,EAAE;EACX,MAAM,EAAE,GAAG;EACX,IAAI,EAAE,CAAC;EACP,QAAQ,EAAE,QAAQ;EAClB,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,IAAI;EACX,OAAO,EAAE,GAAG;;AAIR,gCAAc;EACV,KAAK,EAAE,KAAK;EACZ,+CAAe;IACX,WAAW,EAAE,kCAAkC;IAC/C,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,YAAY;IACrB,SAAS,EAAE,IAAI;IACf,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,OAAO;IACd,UAAU,EAAE,MAAM;;AAQtB,mCAAO;EACH,WAAW,EAAE,CAAC;EACd,YAAY,EAAE,CAAC;EACf,cAAc,EAAE,CAAC;AAOjB,8CAAO;EACH,OAAO,EAAE,CAAC;AAK1B,yBAAU;EACN,OAAO,EAAE,MAAM;EACf,KAAK,EAAE,kBAAkB;AAIrB,mCAAO;EACH,OAAO,EAAE,OAAO;;AAM5B,eAAO;EACH,MAAM,EAAE,KAAK;;AAKrB,YAAY;AACZ,aAAc;EACV,UAAU,EAAC,MAAM;EACjB,KAAK,EAAC,IAAI;EACV,MAAM,EAAC,IAAI;EACX,QAAQ,EAAC,QAAQ;EACjB,KAAK,EAAC,CAAC;EACP,GAAG,EAAC,CAAC;EACL,SAAS,EAAC,IAAI;;AAElB,eAAgB;EACZ,OAAO,EAAE,aAAa;EACtB,KAAK,EAAE,IAAI;;AAEf,YAAa;EACT,UAAU,EAAE,sDAAsD;;AAEtE,WAAY;EACR,UAAU,EAAC,qDAAqD;;AAEpE,mBAAoB;EAChB,KAAK,EAAE,yBAAsB;EAC7B,SAAS,EAAE,KAAK;EAChB,QAAQ,EAAE,QAAQ;EAClB,GAAG,EAAE,IAAI;EACT,IAAI,EAAE,GAAG;;AAEb,SAAU;EACN,qBAAqB,EAAE,WAAW;EAClC,KAAK,EAAE,OAAO;EACd,OAAO,EAAE,cAAc;EACvB,QAAQ,EAAE,QAAQ;EAClB,UAAU,EAAE,KAAK;;AAErB,iBAAkB;EACd,OAAO,EAAE,MAAM;EACf,KAAK,EAAE,OAAO;EACd,uBAAK;IACD,KAAK,EAAE,GAAG;IACV,UAAU,EAAE,MAAM;IAClB,YAAY,EAAE,iBAAiB;IAC/B,OAAO,EAAE,YAAY;;AAG7B,iBAAkB;EACd,UAAU,EAAE,OAAO;;AAEvB,aAAa;EACT,OAAO,EAAE,YAAY;EACrB,aAAa,EAAE,IAAI;EACnB,UAAU,EAAE,IAAI;EAChB,KAAK,EAAE,IAAI;EACX,mBAAK;IACD,KAAK,EAAE,OAAO;IACd,SAAS,EAAE,IAAI;;AAGvB,WAAY;EACR,OAAO,EAAE,SAAS;;AAEtB,0BAA2B;EACvB,KAAK,EAAE,OAAO;;AAGlB,kBAAkB;AAClB,aAAc;EACV,OAAO,EAAE,YAAY;EACrB,WAAW,EAAE,GAAG;EAChB,UAAU,EAAE,MAAM;EAClB,WAAW,EAAE,MAAM;EACnB,cAAc,EAAE,MAAM;EACtB,UAAU,EAAE,oBAAoB;EAChC,aAAa,EAAE,CAAC;EAChB,MAAM,EAAE,OAAO;;AAGnB,WAAW;AACX,aAAc;EACV,MAAM,EAAE,OAAO;EACf,KAAK,EAAE,IAAI;EACX,QAAQ,EAAE,MAAM;;AAEpB,eAAgB;EACZ,KAAK,EAAE,KAAK;EACZ,OAAO,EAAE,MAAM;EACf,KAAK,EAAE,IAAI;EACX,QAAQ,EAAE,QAAQ;EAClB,UAAU,EAAE,IAAI;;AAEpB;+BACgC;EAC5B,KAAK,EAAE,IAAI;EACX,QAAQ,EAAE,QAAQ;EAClB,UAAU,EAAE,GAAG;EACf,UAAU,EAAE,GAAG;;AAEnB;qCACsC;EAClC,SAAS,EAAE,KAAK;EAChB,UAAU,EAAE,IAAI;;AAEpB,gCAAiC;EAC7B,KAAK,EAAE,IAAI;;AAEf,UAAW;EACP,KAAK,EAAE,IAAI;EACX,WAAW,EAAE,IAAI;EACjB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,GAAG;;AAEnB,gCAAiC;EAC7B,WAAW,EAAE,IAAI;;AAIjB,mCAAY;EACR,aAAa,EAAE,iBAAiB;EAChC,cAAc,EAAE,GAAG;EACnB,MAAM,EAAE,iBAAiB;AAE7B,yBAAE;EACE,UAAU,EAAE,CAAC;EACb,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;AAEpB,wBAAC;EACG,UAAU,EAAE,IAAI;EAChB,aAAa,EAAE,IAAI;;AAMvB,aAAE;EACE,WAAW,EAAE,KAAK;AAEtB,cAAG;EACC,OAAO,EAAE,YAAY;EACrB,MAAM,EAAE,OAAO;EACf,OAAO,EAAE,IAAI;EACb,KAAK,EAAE,IAAI;EACX,iBAAG;IACC,KAAK,EAAE,OAAO;IACd,SAAS,EAAE,IAAI;IACf,WAAW,EAAE,GAAG;IAChB,OAAO,EAAE,UAAU;IACnB,UAAU,EAAE,KAAK;IACjB,4BAAa;MACT,YAAY,EAAE,IAAI;MAClB,aAAa,EAAE,CAAC;MAChB,UAAU,EAAE,IAAI;IAEpB,sBAAK;MACD,SAAS,EAAE,IAAI;;AAQ/B,WAAY;EACV,UAAU,EAAE,MAAM;EAClB,aAAa,EAAE,IAAI;EACnB,gBAAI;IACA,KAAK,EAAE,OAAO;IACd,SAAS,EAAE,IAAI;;AAIrB,cAAe;EACX,SAAS,EAAE,KAAK;EAChB,MAAM,EAAE,QAAQ;;AAEpB,WAAY;EACV,UAAU,EAAE,OAAO;EACnB,OAAO,EAAE,cAAc;EACvB,aAAa,EAAE,GAAG;;AAEpB,cAAe;EACb,KAAK,EAAE,OAAO;EACd,UAAU,EAAE,MAAM;EAClB,aAAa,EAAE,IAAI;;AAErB,qBAAsB;EACpB,KAAK,EAAE,OAAO;;AAEhB,2BAA4B;EAC1B,cAAc,EAAE,IAAI;;AAEtB,gBAAiB;EACf,KAAK,EAAE,IAAI;EACX,cAAc,EAAE,SAAS;EACzB,SAAS,EAAE,IAAI;EACf,OAAO,EAAE,IAAI;EACb,MAAM,EAAE,GAAG;;AAEb,iBAAkB;EAChB,KAAK,EAAE,OAAO;EACd,cAAc,EAAE,SAAS;;AAE3B,mBAAoB;EAClB,KAAK,EAAE,OAAO;;AAEhB,qBAAsB;EACpB,MAAM,EAAE,SAAS;EACjB,UAAU,EAAE,iBAAiB;EAC7B,aAAa,EAAE,iBAAiB;EAChC,OAAO,EAAE,QAAQ;EACjB,UAAU,EAAE,OAAO;;AAErB,cAAe;EACb,OAAO,EAAE,MAAM;EACf,wBAAS;IACP,UAAU,EAAE,OAAO;IACnB,KAAK,EAAE,IAAI;IACX,8BAAO;MACH,UAAU,EAAE,OAAO;EAG3B,uBAAQ;IACJ,UAAU,EAAE,OAAO;IACnB,KAAK,EAAE,IAAI;IACX,6BAAO;MACH,UAAU,EAAE,OAAO;;AAK3B,gBAAiB;EACf,OAAO,EAAE,IAAI;;AAEf,gBAAiB;EACf,KAAK,EAAE,OAAO;;AAEhB,SAAU;EACR,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,KAAK;EACb,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,KAAK;;AAEpB,sBAAuB;EACrB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,WAAW,EAAE,IAAI;;AAEnB,cAAe;EACb,aAAa,EAAE,IAAI;;AAErB,iBAAkB;EAChB,OAAO,EAAE,YAAY;EACrB,KAAK,EAAE,KAAK;EACZ,UAAU,EAAE,MAAM;EAClB,YAAY,EAAE,iBAAiB;;AAEjC,4BAA6B;EAC3B,YAAY,EAAE,GAAG;;AAEnB,cAAe;EACb,QAAQ,EAAE,MAAM;;AAGlB,WAAW;AACX,kBAAmB;EACf,gBAAgB,EAAE,WAAW;EAC7B,WAAW,EAAE,CAAC;;AAElB,oCAAqC;EACjC,WAAW,EAAE,MAAM;EACnB,cAAc,EAAE,MAAM;;AAE1B,kBAAmB;EACf,OAAO,EAAE,YAAY;EACrB,aAAa,EAAE,CAAC;EAChB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,WAAW,EAAE,IAAI;EACjB,KAAK,EAAE,OAAO;EACd,UAAU,EAAE,MAAM;EAClB,gBAAgB,EAAE,OAAO;EACzB,MAAM,EAAE,6BAAyB;EACjC,aAAa,EAAE,MAAM;;AAIzB,QAAS;EACP,UAAU,EAAE,OAAO;EACnB,MAAM,EAAE,iBAAiB;EACzB,OAAO,EAAE,QAAQ;EACjB,KAAK,EAAE,OAAO;;AAEhB,eAAgB;EACd,UAAU,EAAE,UAAU;EACtB,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,KAAK;EACb,OAAO,EAAE,cAAc;EACvB,MAAM,EAAE,cAAc;EACtB,UAAU,EAAE,WAAW;;AAEzB,mBAAoB;EAClB,MAAM,EAAE,KAAK;;AAEf,kBAAmB;EACjB,MAAM,EAAE,KAAK;;AAEf,UAAW;EACT,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,IAAI;EACZ,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,KAAK;;AAEpB,aAAc;EACZ,cAAc,EAAE,GAAG;;AAErB;;UAEW;EACT,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,KAAK;;AAEf,aAAc;EACZ,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,CAAC;;AAEZ,0DAA2D;EACzD,WAAW,EAAE,IAAI;EACjB,MAAM,EAAE,QAAQ;;AAElB,sDAAuD;EACrD,WAAW,EAAE,MAAM;EACnB,MAAM,EAAE,OAAO;;AAEjB,kCAAmC;EACjC,aAAa,EAAE,GAAG;EAClB,OAAO,EAAE,SAAS;EAClB,KAAK,EAAE,IAAI;EACX,UAAU,EAAE,kBAAkB;EAC9B,MAAM,EAAE,IAAI;EACZ,KAAK,EAAE,eAAc;;AAEvB,mBAAoB;EAClB,KAAK,EAAE,mCAAmC;;AAE5C,iBAAkB;EAChB,MAAM,EAAE,KAAK;;AAGf,WAAY;EACV,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,KAAK;;AAGf,YAAa;EACT,KAAK,EAAE,eAAe;EACtB;;6DAE+C;IAC3C,gBAAgB,EAAE,OAAO;IACzB,YAAY,EAAE,OAAO;IACrB,kBAAkB,EAAE,IAAI;IACxB,UAAU,EAAE,IAAI;IAChB,KAAK,EAAE,IAAI;EAEf,yCAA6B;IACzB,gBAAgB,EAAE,OAAO;IACzB,YAAY,EAAE,OAAO;IACrB,KAAK,EAAE,IAAI;;AAanB;6BAC6B;AAC7B,IAAI;EACA,OAAO,EAAE,YAAY;;AAEzB,2BAA4B;EAC1B,cAAc,EAAE,GAAG;;AAErB,6BAA8B;EAC5B,SAAS,EAAE,IAAI;EACf,YAAY,EAAE,GAAG;EACjB,YAAY,EAAE,KAAK;EACnB,aAAa,EAAE,KAAK;EACpB,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,GAAG;EAChB,OAAO,EAAE,YAAY;;AAEvB,8BAA+B;EAC7B,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,GAAG;;AAEjB,2BAA4B;EAC1B,SAAS,EAAE,IAAI;EACf,KAAK,EAAE,OAAO;;AAEhB,4BAA6B;EAC3B,SAAS,EAAE,IAAI;EACf,KAAK,EAAE,OAAO;;AAEhB;6BAC6B;AAC7B,gBAAiB;EACf,UAAU,EAAE,MAAM;;AAEpB,4BAA6B;EAC3B,SAAS,EAAE,OAAO;EAClB,WAAW,EAAE,GAAG;EAChB,KAAK,EAAE,OAAO;;AAEhB,8BAA+B;EAC7B,SAAS,EAAE,IAAI;EACf,YAAY,EAAE,GAAG;;AAEnB,2BAA4B;EAC1B,SAAS,EAAE,IAAI;EACf,aAAa,EAAE,GAAG;EAClB,KAAK,EAAE,OAAO;;AAEhB,0BAA2B;EACzB,MAAM,EAAE,GAAG;EACX,aAAa,EAAE,CAAC;EAChB,UAAU,EAAE,IAAI;EAChB,UAAU,EAAE,IAAI;;AAElB,8BAA+B;EAC7B,UAAU,EAAE,IAAI;;AAElB;6BAC6B;AAC7B,6BAA8B;EAC5B,OAAO,EAAE,YAAY;EACrB,OAAO,EAAE,IAAI;EACb,QAAQ,EAAE,QAAQ;EAClB,WAAW,EAAE,IAAI;;AAEnB,+BAAgC;EAC9B,SAAS,EAAE,IAAI;EACf,KAAK,EAAE,OAAO;;AAEhB,gCAAiC;EAC/B,UAAU,EAAE,MAAM;EAClB,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,IAAI;;AAEnB,8BAA+B;EAC7B,SAAS,EAAE,IAAI;;AAEjB,6BAA8B;EAC5B,WAAW,EAAE,GAAG;;AAElB,6BAA8B;EAC5B,WAAW,EAAE,IAAI;EACjB,OAAO,EAAE,IAAI;;AAEf,8BAA+B;EAC7B,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,KAAK,EAAE,OAAO;;AAEhB,gCAAiC;EAC/B,UAAU,EAAE,MAAM;EAClB,WAAW,EAAE,IAAI;EACjB,OAAO,EAAE,IAAI;;AAEf,iBAAkB;EAChB,QAAQ,EAAE,QAAQ;;AAEpB,4BAA6B;EAC3B,OAAO,EAAE,YAAY;EACrB,QAAQ,EAAE,QAAQ;EAClB,GAAG,EAAE,GAAG;;AAEV,mBAAoB;EAClB,OAAO,EAAE,KAAK;EACd,SAAS,EAAE,IAAI;;AAEjB,+BAAgC;EAC9B,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,MAAM;;AAEpB,+BAAgC;EAC9B,SAAS,EAAE,IAAI;;AAEjB,4BAA6B;EAC3B,aAAa,EAAE,KAAK;EACpB,OAAO,EAAE,YAAY;EACrB,QAAQ,EAAE,QAAQ;;AAEpB,mBAAoB;EAClB,aAAa,EAAE,KAAK;EACpB,OAAO,EAAE,KAAK;EACd,SAAS,EAAE,IAAI;EACf,OAAO,EAAE,IAAI;;AAEf,+BAAgC;EAC9B,WAAW,EAAE,KAAK;EAClB,OAAO,EAAE,MAAM;EACf,QAAQ,EAAE,QAAQ;EAClB,UAAU,EAAE,KAAK;EACjB,cAAc,EAAE,MAAM;;AAExB,+BAAgC;EAC9B,UAAU,EAAE,KAAK;EACjB,YAAY,EAAE,IAAI;EAClB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;;AAElB,gBAAiB;EACf,QAAQ,EAAE,QAAQ;;AAEpB,2BAA4B;EAC1B,OAAO,EAAE,YAAY;EACrB,QAAQ,EAAE,QAAQ;EAClB,GAAG,EAAE,GAAG;;AAEV,kBAAmB;EACjB,OAAO,EAAE,KAAK;EACd,SAAS,EAAE,IAAI;;AAEjB,8BAA+B;EAC7B,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,MAAM;;AAEpB,8BAA+B;EAC7B,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;;AAElB,2BAA4B;EAC1B,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;;AAElB,gCAAiC;EAC/B,UAAU,EAAE,MAAM;;AAEpB,mCAAoC;EAClC,UAAU,EAAE,MAAM;EAClB,QAAQ,EAAE,QAAQ;EAClB,MAAM,EAAE,SAAS;EACjB,OAAO,EAAE,YAAY;EACrB,KAAK,EAAE,IAAI;;AAEb,qCAAsC;EACpC,QAAQ,EAAE,QAAQ;EAClB,IAAI,EAAE,CAAC;EACP,KAAK,EAAE,CAAC;EACR,UAAU,EAAE,MAAM;EAClB,GAAG,EAAE,IAAI;EACT,SAAS,EAAE,IAAI;;AAEjB,+BAAgC;EAC9B,UAAU,EAAE,MAAM;EAClB,UAAU,EAAE,IAAI;;AAElB,2CAA4C;EAC1C,YAAY,EAAE,GAAG;;AAEnB,gCAAiC;EAC/B,KAAK,EAAE,OAAO;EACd,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,WAAW,EAAE,IAAI;;AAEnB,8BAA+B;EAC7B,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,KAAK,EAAE,OAAO;;AAEhB,kCAAmC;EACjC,QAAQ,EAAE,QAAQ;;AAEpB,oCAAqC;EACnC,SAAS,EAAE,IAAI;;AAEjB;8BAC8B;AAC9B,kBAAmB;EACjB,OAAO,EAAE,IAAI;;AAEf,gCAAiC;EAC/B,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;;AAElB,+BAAgC;EAC9B,KAAK,EAAE,OAAO;EACd,MAAM,EAAE,OAAO;EACf,IAAI,EAAE,IAAI;EACV,QAAQ,EAAE,QAAQ;EAClB,SAAS,EAAE,aAAa;;AAE1B,gCAAiC;EAC/B,UAAU,EAAE,IAAI;;AAElB,6CAA8C;EAC5C,SAAS,EAAE,IAAI;EACf,KAAK,EAAE,OAAO;;AAEhB,4CAA6C;EAC3C,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,WAAW,EAAE,IAAI;;AAEnB,gDAAiD;EAC/C,KAAK,EAAE,OAAO;EACd,WAAW,EAAE,GAAG;EAChB,QAAQ,EAAE,QAAQ;EAClB,GAAG,EAAE,IAAI;;AAEX,4BAA6B;EAC3B,aAAa,EAAE,CAAC;EAChB,UAAU,EAAE,IAAI;EAChB,MAAM,EAAE,GAAG;EACX,UAAU,EAAE,OAAO;EACnB,UAAU,EAAE,IAAI;;AAElB,2BAA4B;EAC1B,KAAK,EAAE,KAAK;;AAEd,8BAA+B;EAC7B,SAAS,EAAE,IAAI;;AAEjB,4BAA6B;EAC3B,SAAS,EAAE,IAAI;;AAEjB,6BAA8B;EAC5B,SAAS,EAAE,IAAI;;AAEjB,6BAA8B;EAC5B,SAAS,EAAE,IAAI;;AAEjB,WAAY;EACV,QAAQ,EAAE,QAAQ;;AAEpB,kBAAmB;EACjB,UAAU,EAAE,OAAO;EACnB,MAAM,EAAE,CAAC;EACT,OAAO,EAAE,EAAE;EACX,MAAM,EAAE,IAAI;EACZ,IAAI,EAAE,CAAC;EACP,MAAM,EAAE,MAAM;EACd,QAAQ,EAAE,QAAQ;EAClB,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,GAAG;;AAEZ,kBAAmB;EACjB,KAAK,EAAE,OAAO;EACd,OAAO,EAAE,GAAG;;AAEd,cAAe;EACb,KAAK,EAAE,OAAO;;AAEhB,aAAc;EACZ,KAAK,EAAE,kBAAkB;EACzB,OAAO,EAAE,IAAI;;AAGf,kBAAkB;AAGd,sEAAkB;EACd,MAAM,EAAE,KAAK;;AAIjB,4BAAM;EACF,YAAY,EAAE,IAAI;;AAItB,0CAAe;EACX,IAAI,EAAE,kBAAkB;EACxB,KAAK,EAAE,YAAY;EACnB,GAAG,EAAE,cAAc;;AAIvB,gCAAY;EACR,aAAa,EAAE,CAAC;EAChB,mCAAG;IACC,aAAa,EAAE,YAAY;IAC3B,qCAAE;MACE,aAAa,EAAE,YAAY;MAC3B,KAAK,EAAE,OAAO;IAGd,4CAAE;MACE,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,OAAO;MACrB,KAAK,EAAE,IAAI;IAIf,2CAAE;MACE,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,OAAO;MACrB,KAAK,EAAE,IAAI;;AAW/B,0BAA2B;EACvB,QAAS;IACL,OAAO,EAAE,MAAM;;EAEnB,aAAc;IACV,aAAa,EAAE,CAAC;;EAEpB,SAAU;IACN,UAAU,EAAE,KAAK;IACjB,aAAI;MACA,MAAM,EAAE,IAAI;MACZ,KAAK,EAAE,IAAI;;EAGnB,8BAA+B;IAC3B,WAAW,EAAE,IAAI;;EAErB,UAAW;IACP,OAAO,EAAE,IAAI;;EAEjB,cAAe;IACX,MAAM,EAAE,KAAK;AAIrB,0BAA2B;EACvB,gBAAiB;IACb,OAAO,EAAE,MAAM;IACf,KAAK,EAAE,KAAK;;EAEhB,qCAAsC;IAClC,KAAK,EAAE,IAAI;;EAEf,uDAAwD;IACpD,OAAO,EAAE,UAAU;;EAEvB,yDAA0D;IACtD,OAAO,EAAE,cAAc;;EAGvB,kBAAa;IACT,QAAQ,EAAE,QAAQ;IAClB,yBAAO;MACH,OAAO,EAAE,KAAK;IAElB,wBAAM;MACF,KAAK,EAAE,IAAI;IAEf,gCAAc;MACV,KAAK,EAAE,CAAC;MACR,GAAG,EAAE,MAAM;MACX,QAAQ,EAAE,QAAQ;AAMlC,yBAA0B;EACtB,yBAA0B;IACtB,IAAI,EAAE,IAAI;AAGlB,4BAA6B;EACzB,IAAK;IACD,OAAO,EAAE,KAAK;;EAElB,gBAAiB;IACb,OAAO,EAAE,KAAK;IACd,MAAM,EAAE,IAAI;IACZ,UAAU,EAAE,OAAO;IACnB,OAAO,EAAE,MAAM;IACf,KAAK,EAAE,IAAI;IACX,wBAAQ;MACJ,aAAa,EAAE,CAAC;MAChB,uCAAe;QACX,MAAM,EAAE,IAAI;MAEhB,sCAAc;QACV,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,YAAY;QACrB,KAAK,EAAE,IAAI;QACX,WAAW,EAAE,CAAC;QACd,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,OAAO;QACnB,6CAAS;UACL,OAAO,EAAE,IAAI;MAGrB,wCAAgB;QACZ,KAAK,EAAE,KAAK;QACZ,UAAU,EAAE,GAAG;MAGf,2CAAI;QACA,OAAO,EAAE,KAAK;MAElB,uEAAkC;QAC9B,GAAG,EAAE,GAAG;MAGhB,oCAAY;QACR,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,CAAC;;EAItB,WAAY;IACR,OAAO,EAAE,IAAI;;EAEjB,YAAa;IACT,OAAO,EAAE,KAAK;IACd,0BAAc;MACV,OAAO,EAAE,gBAAgB;MACzB,4CAAkB;QACd,OAAO,EAAE,CAAC;QACV,KAAK,EAAE,OAAO;MAElB,oCAAU;QACN,KAAK,EAAE,IAAI;MAEf,oCAAU;QACN,KAAK,EAAE,KAAK;IAGpB,yBAAa;MACT,OAAO,EAAE,SAAS;MAClB,2CAAkB;QACd,OAAO,EAAE,CAAC;MAEd,sCAAa;QACT,UAAU,EAAE,OAAO;QACnB,OAAO,EAAE,CAAC;QACV,yCAAG;UACC,OAAO,EAAE,KAAK;QAElB,kDAAc;UACV,KAAK,EAAE,IAAI;UACX,UAAU,EAAE,IAAI;UAChB,KAAK,EAAE,IAAI;UACX,8DAAY;YACR,OAAO,EAAE,CAAC;IAK1B,qBAAS;MACL,OAAO,EAAE,MAAM;MAEX,uCAAY;QACR,aAAa,EAAE,CAAC;MAEpB,wCAAa;QACT,OAAO,EAAE,QAAQ;QACjB,2CAAG;UACC,MAAM,EAAE,CAAC;UACT,8CAAG;YACC,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,MAAM;MAK7B,uCAAkB;QACd,OAAO,EAAE,CAAC;MAGV,4CAAkB;QACd,OAAO,EAAE,MAAM;MAKnB;gDAAU;QACN,OAAO,EAAE,KAAK;MAGtB,uCAAkB;QACd,YAAY,EAAE,YAAY;MAE9B,mDAA8B;QAC1B,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,CAAC;QACV,qEAAkB;UACd,OAAO,EAAE,CAAC;QAEd,4HAAqB;UACjB,OAAO,EAAE,GAAG",
"sources": ["style.scss","variables.scss","_gauge.scss","_switches.scss","_widgets.scss"],
"names": [],
"file": "style.css"
}
The file style.scss contains:
/* This css file is to over write bootstarp css
--------------------------------------------------------- /
* Theme Name: Sufee-Admin Admin Template
* Theme URI: http://demos.jeweltheme.com/Sufee-Admin/
* Author: jewel_theme
* Author URI: http://themeforest.net/user/jewel_theme/portfolio
* Description:
* Version: 1.0.0
* License: GNU General Public License v2 or later
* License URI: http://www.gnu.org/licenses/gpl-2.0.html
* Tags: html, themplate, Sufee-Admin
--------------------------------------------------------- */

@import "variables";
@import "gauge";
@import "switches";
@import "widgets";
@import "../css/animate.css";

.bg-flat-color-1 {background: #20a8d8};
.bg-flat-color-2 {background: #63c2de};
.bg-flat-color-3 {background: #ffc107};
.bg-flat-color-4 {background: #f86c6b};
.bg-flat-color-5 {background: #4dbd74};


.transition {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

body {
    background: $container-bg;
    display: table;
    font-family: 'Open Sans' sans-serif !important;
    font-size: 16px;
    width: 100%
}

div[class*="col-"] {
    float: left;
}

p {
    font-size: 16px;
    font-family: 'Open Sans' sans-serif;
    font-weight: 400;
    line-height: 24px;
    color: #878787;
}
p:focus {
    border: none;
    outline: 0;
}
a, button {
    text-decoration: none;
    outline: none!important;
    color: #878787;

    -webkit-transition: all 0.25s ease;
    -moz-transition: all 0.25s ease;
    -ms-transition: all 0.25s ease;
    -o-transition: all 0.25s ease;
    transition: all 0.25s ease;

}
a:hover,
a:focus {
    text-decoration: none;
    color: #000;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}
ul,
ol {
    padding-left: 0;
}
.btn:focus,
button:focus {
    box-shadow: none !important;
    outline: 0;
}
img {
    max-width: 100%;
}
.btn,
button,
input,
textarea {
    box-shadow: none;
    outline: 0 !important;
}
.no-padding {
    padding: 0 !important;
}

/* Global Styles */

/* Main Styles */
.basix-container {
    display: table;
    min-height: 100vh;
    position: relative;
    width: 100%;
}
aside.left-panel {
    background: $menu-bg;
    display: table-cell;
    height: 100vh;
    min-height: 100%;
    padding: 0 25px;
    vertical-align: top;
    width: 280px;
    transition: width 0.3s ease;
}

.navbar {
    background: #272c33;
    border-radius: 0;
    border: none;
    display: block;
    margin: 0;
    margin-bottom: 100px;
    padding: 0;

    .navbar-header {
        float: none;
        text-align: center;
        width: 100%;
    }
    .navbar-brand {
        border-bottom: 1px solid #4e4e52;
        color: #f1f2f7 !important;
        font-family: 'Open Sans';
        font-size: 22px;
        float: none;
        line-height: 50px;
        margin: 0;
        text-align: left;
        text-transform: capitalize;
        display: block;
        min-height: 69px;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        span {
            font-weight: 600;
        }
        img {
            max-width: 160px;
        }
        &.hidden {
            display: none;
        }
    }

    .menu-title {
        border-bottom: 1px solid #4e4e52;
        color: #9496a1;
        clear: both;
        display: block;
        font-family: 'Open Sans';
        font-size: 14px;
        font-weight: 700;
        line-height: 50px;
        padding: 15px 0 0 0;
        text-transform: uppercase;
        width: 100%;
    }
    .navbar-nav {
        float: none;
        position: relative;
        li {
            width: 100%;
            &.active .menu-icon,
            &:hover .toggle_nav_button:before,
            .toggle_nav_button.nav-open:before {
                color: #fff !important;
            }
            .dropdown-toggle:after {
                display: none;
            }
            >a {
                background: none !important;
                color: #c8c9ce !important;
                display: inline-block;
                font-family: 'Open Sans';
                font-size: 14px;
                line-height: 30px;
                padding: 10px 0;
                position: relative;
                width: 100%;
                &:hover,
                &:hover .menu-icon {
                    color: #fff !important;
                }
                .menu-icon {
                    color: #8b939b;
                    float: left;
                    margin-top: 8px;
                    width: 55px;
                    text-align: left;
                    z-index: 9;
                }
                .menu-title-text {
                    font-size: 14px;
                }
                .badge {
                    border-radius: 0;
                    font-family: 'Open Sans';
                    font-weight: 600;
                    float: right;
                    margin: 6px 0 0 0;
                    padding: 0.4em 0.5em;
                }
            }
            &.menu-item-has-children {
                position: relative;
                a {
                    line-height: 30px;
                    &:before {
                        content: "\f105";
                        color: #c8c9ce;
                        font-family: 'Fontawesome';
                        font-size: 16px;
                        position: absolute;
                        top: 10px;
                        right: 0;
                        text-align: right;

                        -webkit-transition: all .25s ease;
                        -moz-transition: all .25s ease;
                        -ms-transition: all .25s ease;
                        -o-transition: all .25s ease;
                        transition: all .25s ease;
                    }
                    &:hover:before {
                        color: #fff;
                    }
                }
                .sub-menu {
                    background: $menu-bg;
                    border: none;
                    box-shadow: none;
                    overflow-y: hidden;
                    padding: 0 0 0 35px;
                    li {
                        position: relative;
                    }
                    i {
                        color: #c8c9ce;
                        float: left;
                        padding: 0;
                        position: absolute;
                        left: 0;
                        font-size: 14px;
                        top: 9px;
                    }
                    a {
                        padding: 2px 0 2px 30px;
                        &:before {
                            content: '';
                            display: none;
                        }
                        .menu-icon {
                            top: 13px;
                            text-align: left;
                            width: 25px;
                        }
                    }
                }
                &.show {
                    a:before {
                        content: "\f107";
                    }
                    .sub-menu {
                        max-height: 1000px;
                        opacity: 1;
                        position: static !important;
                    }
                }
            }
        }
    }
}
.navbar .navbar-nav>.active>a,
.navbar .navbar-nav>.active>a:focus,
.navbar .navbar-nav>.active>a:hover {
    color: #d7d9e3 !important;
}
.navbar-nav li span.count {
    background: #a9d86e;
    border-radius: 50%;
    color: #fff;
    font-family: 'Open Sans';
    font-size: 9px;
    font-weight: 700;
    float: right;
    height: 20px;
    width: 20px;
    line-height: 20px;
    margin-right: 15px;
    text-align: center;
}
body.open {
    .navbar {
        .navbar-brand {
            &.hidden {
                display: block;
            }
        }
    }
}
.open aside.left-panel {
    max-width: 70px;
    width: 70px;
    .navbar {
        .navbar-brand {
            display: none;
            &.hidden {
                display: flex !important;
                justify-content: center;
                align-items: center;
                padding-left: 0;
                padding-right: 0;
                text-align: center;
                img {
                    max-width: 30px;
                    margin: 0 auto;
                }
            }
            &.d-md-none {
                display: block !important;
                margin: 13px 0 0;
                min-height: 67px;
                padding: 0;
                text-align: center;
            }
        }
        .navbar-nav {
            &:before {
                display: none !important;
            }
            li {
                position: relative;
                a {
                    font-size: 0;
                    z-index: 0;
                    transition: none;
                    .menu-icon {
                        font-size: 20px;
                        z-index: -1;
                        width: inherit;
                    }
                    .menu-title-text {
                        font-size: 0;
                    }
                    .badge {
                        display: none;
                    }
                }
                >a {
                    max-width: 60px;
                    padding-left: 0;
                }
                &.menu-item-has-children {
                    overflow: hidden;
                    a {
                        &:before {
                            content: '';
                            display: none;
                        }
                    }
                    ul {
                        padding-left: 0;
                    }
                    .sub-menu {
                        display: block;
                        left: inherit;
                        right: -180px;
                        top: 0;
                        li {
                            a {
                                display: block;
                                font-size: 14px;
                                max-width: inherit;
                                padding: 2px 15px 2px 25px;
                                width: 100%;
                                .menu-icon {
                                    text-align: center;
                                }
                            }
                        }
                    }
                    &.show {
                        overflow: visible;
                        .sub-menu {
                            position: absolute !important;
                        }
                    }
                }
                span.count {
                    display: none;
                    margin-right: 5px;
                    z-index: 1;
                }
                &.active {
                    a:after {
                        content: '';
                        display: none;
                    }
                }
            }
            .menu-title {
                font-size: 0;
                line-height: 0;
                opacity: 0;
                padding: 0;
            }
        }
    }
}

/* Right panel */

.right-panel {
    display: table-cell;
    padding-left: 0 !important;

    -webkit-transition: all .35s ease;
    -moz-transition: all .35s ease;
    -ms-transition: all .35s ease;
    -o-transition: all .35s ease;
    transition: all .35s ease;
    .breadcrumbs {
        background-color: #fff;
        display: inline-block;
        margin-top: 0;
        padding: 0 5px;
        width: 100%;
        .col-lg-8 {
            .page-header {
                float: left;
            }
        }
    }
    .page-header{
        min-height: 50px;
        margin: 0px;
        padding: 0px 15px;
        background: #ffffff;
        border-bottom: 0px;
        h1{
            font-size: 18px;
            padding: 15px 0;
        }
        .breadcrumb{
            margin: 0px;
            padding: 13.5px 0;
            background: #fff;
            text-transform: capitalize;
        }
        .breadcrumb>li+li:before{
            padding: 0 5px;
            color: #ccc;
            content: "/\00a0";
        }
    }
}
.right-panel header.header {
    background: $header-bg;
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
    clear: both;
    display: inline-block;
    padding: 15px 20px 13px 20px;
    width: 100%;
}
.open .right-panel {
    margin-left: -210px;
}
header.fixed-top {
    background: #fff;
    padding: 20px;
}
.header-menu .col-sm-7 {
    position: inherit;
}
.menutoggle {
    background: #e74c3c;
    border-radius: 50%;
    color: #fff !important;
    cursor: pointer;
    font-size: 18px;
    height: 43px;
    line-height: 44px;
    margin: -2px 20px 0 -57px;
    text-align: center;
    width: 43px;
}
.open .menutoggle i:before {
    content: "\f0a4";
}
.search-trigger {
    background: transparent;
    border: none;
    color: $menu-color;
    cursor: pointer;
    font-size: 16px;
    height: 41px;
    width: 43px;
    line-height: 38px;
}
header .form-inline {
    background: #263238;
    display: none;
    height: 70px;
    margin: 0;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9999;
    .search-form {
        height: 100%;
        max-width: 1025px;
        margin: 0 auto;
        position: relative;
        input[type="text"] {
            background: #263238;
            border: none;
            border-radius: 0;
            box-shadow: none;
            color: #d3d3d3;
            font-size: 16px;
            height: inherit;
            margin-right: 0 !important;
            padding: 10px 36px 10px 15px;
            width: 100%;
        }
        input[type="text"].active,
        input[type="text"]:focus {
            border-color: rgba(0, 0, 0, 0.125);
            outline: 0;
        }
        button {
            background: transparent;
            border: none;
            color: #fff;
            font-size: 16px;
            position: absolute;
            right: 15px;
            top: 50%;
            margin-top: -14px !important;
        }
        button:active,
        button:focus,
        button:visited,
        .btn-outline-success:hover {
            background: transparent;
            border: none !important;
            box-shadow: none;
            outline: 0 !important;
        }
        &.close {
            display: none;
        }
    }
}
.header-left.open .form-inline {
    display: block;
}
.header-left .dropdown {
    display: inline-block;
    .dropdown-toggle {
        background: transparent;
        border: none;
        color: $menu-color;
        font-size: 16px;
        &:after {
            display: none;
        }
        .count {
            border-radius: 50%;
            color: #fff;
            font-size: 11px;
            height: 15px;
            width: 15px;
            line-height: 15px;
            right: 0;
            top: 0;
            position: absolute;
        }
        &:active,
        &:focus,
        &:visited {
            background: none !important;
            border-color: transparent !important;
            color: #272c33 !important;
        }
    }
    .dropdown-menu {
        background: $header-bg;
        border: none;
        border-radius: 0;
        box-shadow: none;
        top: 49px !important;
        p {
            font-size: 15px;
            margin: 0;
            padding: 5px 15px;
        }
        .dropdown-item {
            color: $menu-color;
            font-size: 13px;
            padding: 10px 15px 3px;
            text-overflow: ellipsis;
            .photo {
                float: left;
                margin-right: 15px;
                width: 25px;
            }
            .message {
                .name {
                    margin-top: -5px;
                }
                .time {
                    font-size: 11px;
                }
                p {
                    clear: both;
                    font-size: 14px;
                    margin: 0;
                    padding: 0;
                    text-overflow: ellipsis;
                }
            }
            &:hover {
                background: transparent;
            }
        }
    }
}
.dropdown-menu {
    border-radius: 0;
    transform: none !important;
}

.for-notification {
    .dropdown-menu {
        .dropdown-item {
            padding: 5px 15px !important;
            text-overflow: ellipsis;
            i {
                float: left;
                font-size: 14px;
                margin: 5px 5px 0 0;
                text-align: left;
                width: 20px;
            }
            p {
                padding: 0 !important;
                text-overflow: ellipsis;
            }
        }
    }
}
.user-area {
    float: right;
    padding-right: 0;
    position: relative;
    .user-menu {
        background: $header-bg;
        border: none;
        font-family: 'Open Sans';
        left: inherit !important;
        right: 0;
        top: 55px !important;
        margin: 0;
        max-width: 150px;
        padding: 5px 10px;
        position: absolute;
        width: 100%;
        z-index: 999;
        min-width: 150px;
        .nav-link {
            color: $menu-color;
            display: block;
            font-size: 14px;
            line-height: 22px;
            padding: 5px 0;
        }
    }
    .user-avatar {
        float: right;
        margin-top: 4px;
        width: 32px;
    }
    .user-info .name {
        color: #8c8c8c;
        font-size: 14px;
        position: relative;
        text-transform: uppercase;
    }
    .count {
        background: #d9534f;
        border-radius: 50%;
        color: #fff;
        font-family: 'Open Sans';
        font-size: 9px;
        font-weight: 700;
        float: right;
        height: 20px;
        width: 20px;
        line-height: 20px;
        text-align: center;
    }
    .dropdown-toggle:after {
        display: none;
    }
}
#menuToggle2 {
    padding-left: 25px;
}
#language-select {
    color: #f1f2f7;
    float: right;
    margin: 7px 20px 0 0;
    max-width: 80px;
    &:focus,
    &:visited {
        border: none;
        outline: 0;
    }
    .dropdown-toggle::after {
        display: none;
    }
    .dropdown-menu {
        background: $header-bg;
        border: none;
        border-radius: 0;
        left: -8px !important;
        min-width: inherit;
        padding: 0 5px;
        top: 46px !important;
        .dropdown-item {
            margin-right: 0;
            max-width: 25px;
            padding: 0;
            &:hover {
                background: $header-bg;
            }
            .flag-icon {
                margin-right: 0;
                width: 25px;
            }
        }
    }
}
.notification-show + .dropdown-menu,
.message-show + .dropdown-menu ,
.language-show + .dropdown-menu  {
    display: block;
}
.content {
    float: left;
    padding: 0 20px;
    width: 100%;
}
.card{
    margin-bottom: 1.5rem;
    border-radius: 0;
    h4{
        font-size: 1.1rem;
    }
    .user-header {
        .media{
            img{
                border: 5px solid rgba(255,255,255,0.3);
                border-radius: 50%;
                -webkit-border-radius: 50%;
            }
        }
    }
    .card-header{
        .card-actions{
            button{
                display: block;
                float: left;
                width: 50px;
                padding: .75rem 0;
                margin: 0!important;
                color: #fff;
                outline: 0;
                text-align: center;
                background: transparent;
                border: 0;
                border-left: 1px solid hsla(210,8%,51%,.4);
            }
        }
    }
    .card-footer{
        padding: 0.65rem 1.25rem;
        background-color: #f0f3f5;
        border-top: 1px solid #c2cfd6;

        & ul li {
            display: table-cell;
            padding: 0 1rem;
            text-align: center;
        }
    }

}

.breadcrumbs {
    margin-top: 0;
}

/* Tabs */
.nav-tabs {
    a.active{
        color: #555;
        cursor: default;
        background-color: #fff;
        border: 1px solid #ddd;
        border-bottom-color: transparent;
    }
    .dropdown {
        .dropdown-menu {
            top: 100% !important;
        }
    }
}
.custom-tab .nav-tabs > a.active, .custom-tab .nav-tabs > .active > a:focus, .custom-tab .nav-tabs > li.active > a:hover {
    border-color: transparent transparent;
    color: #ff2e44;
    position: relative;
}
.custom-tab .nav-tabs > a.active > a:after, .custom-tab .nav-tabs > li.active > a:focus:after, .custom-tab .nav-tabs > li.active > a:hover:after{
    background: #ff2e44;
    bottom: -1px;
    content: "";
    height: 2px;
    left: 0;
    position: absolute;
    right: 0;
    width: 100%;
    z-index: 999;
}
.card {
    .card-header {
        .card-actions {
            float: right;
            [class*="btn"] {
                border-left: 1px solid rgba(120, 130, 140, 0.4);
                color: #878787;
                display: inline-block;
                font-size: 16px;
                float: left;
                padding: 0 7px;
                width: inherit;
                text-align: center;
            }
        }
    }
}
.social-buttons {
    .card-body {
        p {
            button {
                padding-top: 0;
                padding-left: 0;
                padding-bottom: 0;
            }
        }
    }
    .only-icon {
        .card-body {
            p {
                button {
                    padding: 0;
                }
            }
        }
    }
    .social i {
        padding: 0 10px;
        width: inherit !important;
    }
    .only-text {
        p {
            button {
                padding: 0 .5rem;
            }
        }
    }
}
.buttons {
    button {
        margin: 2px 0;
    }
}


/* Ribons */
.corner-ribon {
    text-align:center;
    width:71px;
    height:71px;
    position:absolute;
    right:0;
    top:0;
    font-size:20px;
}
.corner-ribon i {
    padding: 10px 0 0 35px;
    color: #fff;
}
.black-ribon {
    background: url("../../images/twitter_corner_black.png") no-repeat;
}
.blue-ribon {
    background:url("../../images/twitter_corner_blue.png") no-repeat;
}
.twt-feed .wtt-mark {
    color: rgba(255,255,255,0.15);
    font-size: 160px;
    position: absolute;
    top: 10px;
    left: 40%;
}
.twt-feed {
    -webkit-border-radius: 4px 4px 0 0;
    color: #FFFFFF;
    padding: 40px 10px 10px;
    position: relative;
    min-height: 170px;
}
.weather-category {
    padding: 15px 0;
    color: #74829C;
    ul li{
        width: 32%;
        text-align: center;
        border-right: 1px solid #e6e6e6;
        display: inline-block;
    }
}
.twt-feed.blue-bg {
    background: #58C9F3;
}
.twt-category{
    display: inline-block;
    margin-bottom: 11px;
    margin-top: 10px;
    width: 100%;
    ul li{
        color: #bdbdbd;
        font-size: 13px;
    }
}
.twt-footer {
    padding: 12px 15px;
}
.twt-footer, .twt-footer a {
    color: #d2d2d2;
}

/* Button Reset */
.btn, .button {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    transition: all .15s ease-in-out;
    border-radius: 0;
    cursor: pointer;
}

/* Icons */
.icon-section {
    margin: 0 0 3em;
    clear: both;
    overflow: hidden;
}
.icon-container {
    width: 240px;
    padding: .7em 0;
    float: left;
    position: relative;
    text-align: left;
}
.icon-container [class^="ti-"],
.icon-container [class*=" ti-"] {
    color: #000;
    position: absolute;
    margin-top: 3px;
    transition: .3s;
}
.icon-container:hover [class^="ti-"],
.icon-container:hover [class*=" ti-"] {
    font-size: 2.2em;
    margin-top: -5px;
}
.icon-container:hover .icon-name {
    color: #000;
}
.icon-name {
    color: #aaa;
    margin-left: 35px;
    font-size: 14px;
    transition: .3s;
}
.icon-container:hover .icon-name {
    margin-left: 45px;
}

.fontawesome-icon-list {
    .page-header{
        border-bottom: 1px solid #C9CDD7;
        padding-bottom: 9px;
        margin: 30px 0px 27px 0px;
    }
    h2{
        margin-top: 0;
        font-size: 20px;
        font-weight: 300;
    }
    i{
        font-style: 16px;
        padding-right: 10px;
    }
}


.social-box {
    i {
        line-height: 110px;
    }
    ul {
        display: inline-block;
        margin: 7px 0 0;
        padding: 10px;
        width: 100%;
        li {
            color: #949CA0;
            font-size: 14px;
            font-weight: 700;
            padding: 0 10px 0 0;
            text-align: right;
            &:last-child {
                padding-left: 10px;
                padding-right: 0;
                text-align: left;
            }
            span {
                font-size: 14px;
            }
        }
    }
}



.login-logo {
  text-align: center;
  margin-bottom: 15px;
  span{
      color: #ffffff;
      font-size: 24px;
  }
}

.login-content {
    max-width: 540px;
    margin: 8vh auto;
}
.login-form {
  background: #ffffff;
  padding: 30px 30px 20px;
  border-radius: 2px;
}
.login-form h4 {
  color: #878787;
  text-align: center;
  margin-bottom: 50px;
}
.login-form .checkbox {
  color: #878787;
}
.login-form .checkbox label {
  text-transform: none;
}
.login-form .btn {
  width: 100%;
  text-transform: uppercase;
  font-size: 14px;
  padding: 15px;
  border: 0px;
}
.login-form label {
  color: #878787;
  text-transform: uppercase;
}
.login-form label a {
  color: #ff2e44;
}
.social-login-content {
  margin: 0px -30px;
  border-top: 1px solid #e7e7e7;
  border-bottom: 1px solid #e7e7e7;
  padding: 30px 0px;
  background: #fcfcfc;
}
.social-button {
  padding: 0 30px;
  .facebook{
    background: #3b5998;
    color: #fff;
    &:hover{
        background: #344e86;
    }
}
.twitter{
    background: #00aced;
    color: #fff;
    &:hover{
        background: #0099d4;
    }
}

}
.social-button i {
  padding: 19px;
}
.register-link a {
  color: #ff2e44;
}
.cpu-load {
  width: 100%;
  height: 272px;
  font-size: 14px;
  line-height: 1.2em;
}
.cpu-load-data-content {
  font-size: 18px;
  font-weight: 400;
  line-height: 40px;
}
.cpu-load-data {
  margin-bottom: 30px;
}
.cpu-load-data li {
  display: inline-block;
  width: 32.5%;
  text-align: center;
  border-right: 1px solid #e7e7e7;
}
.cpu-load-data li:last-child {
  border-right: 0px;
}
.nestable-cart {
  overflow: hidden;
}

/* Forms */
.input-group-addon {
    background-color: transparent;
    border-left: 0;
}
.input-group-addon, .input-group-btn {
    white-space: nowrap;
    vertical-align: middle;
}
.input-group-addon {
    padding: .5rem .75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.25;
    color: #495057;
    text-align: center;
    background-color: #e9ecef;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: .25rem;
}


.flotTip {
  background: #252525;
  border: 1px solid #252525;
  padding: 5px 15px;
  color: #ffffff;
}
.flot-container {
  box-sizing: border-box;
  width: 100%;
  height: 275px;
  padding: 20px 15px 15px;
  margin: 15px auto 30px;
  background: transparent;
}
.flot-pie-container {
  height: 275px;
}
.flotBar-container {
  height: 275px;
}
.flot-line {
  width: 100%;
  height: 100%;
  font-size: 14px;
  line-height: 1.2em;
}
.legend table {
  border-spacing: 5px;
}
#chart1,
#flotBar,
#flotCurve {
  width: 100%;
  height: 275px;
}
.morris-hover {
  position: absolute;
  z-index: 1;
}
.morris-hover.morris-default-style .morris-hover-row-label {
  font-weight: bold;
  margin: 0.25em 0;
}
.morris-hover.morris-default-style .morris-hover-point {
  white-space: nowrap;
  margin: 0.1em 0;
}
.morris-hover.morris-default-style {
  border-radius: 2px;
  padding: 10px 12px;
  color: #666;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff!important;
}
.morris-hover-point {
  color: rgba(255, 255, 255, 0.8) !important;
}
#morris-bar-chart {
  height: 285px;
}

.map, .vmap {
  width: 100%;
  height: 400px;
}

.btn-toolbar {
    float: left !important;
    .btn-outline-secondary:not([disabled]):not(.disabled):active,
    .btn-outline-secondary:not([disabled]):not(.disabled).active,
    .show > .btn-outline-secondary.dropdown-toggle {
        background-color: #212529;
        border-color: #212529;
        -webkit-box-shadow: none;
        box-shadow: none;
        color: #fff;
    }
    .btn-outline-secondary:hover {
        background-color: #212529;
        border-color: #212529;
        color: #fff;
    }
}










/*    Widget One
---------------------------*/
.dib{
    display: inline-block;
}
.stat-widget-one .stat-icon {
  vertical-align: top;
}
.stat-widget-one .stat-icon i {
  font-size: 30px;
  border-width: 3px;
  border-style: solid;
  border-radius: 100px;
  padding: 15px;
  font-weight: 900;
  display: inline-block;
}
.stat-widget-one .stat-content {
  margin-left: 30px;
  margin-top: 7px;
}
.stat-widget-one .stat-text {
  font-size: 14px;
  color: #868e96;
}
.stat-widget-one .stat-digit {
  font-size: 24px;
  color: #373757;
}
/*    Widget Two
---------------------------*/
.stat-widget-two {
  text-align: center;
}
.stat-widget-two .stat-digit {
  font-size: 1.75rem;
  font-weight: 500;
  color: #373757;
}
.stat-widget-two .stat-digit i {
  font-size: 18px;
  margin-right: 5px;
}
.stat-widget-two .stat-text {
  font-size: 16px;
  margin-bottom: 5px;
  color: #868e96;
}
.stat-widget-two .progress {
  height: 8px;
  margin-bottom: 0;
  margin-top: 20px;
  box-shadow: none;
}
.stat-widget-two .progress-bar {
  box-shadow: none;
}
/*    Widget Three
---------------------------*/
.stat-widget-three .stat-icon {
  display: inline-block;
  padding: 33px;
  position: absolute;
  line-height: 21px;
}
.stat-widget-three .stat-icon i {
  font-size: 30px;
  color: #ffffff;
}
.stat-widget-three .stat-content {
  text-align: center;
  padding: 15px;
  margin-left: 90px;
}
.stat-widget-three .stat-digit {
  font-size: 30px;
}
.stat-widget-three .stat-text {
  padding-top: 4px;
}
.home-widget-three .stat-icon {
  line-height: 19px;
  padding: 27px;
}
.home-widget-three .stat-digit {
  font-size: 24px;
  font-weight: 300;
  color: #373757;
}
.home-widget-three .stat-content {
  text-align: center;
  margin-left: 60px;
  padding: 13px;
}
.stat-widget-four {
  position: relative;
}
.stat-widget-four .stat-icon {
  display: inline-block;
  position: absolute;
  top: 5px;
}
.stat-widget-four i {
  display: block;
  font-size: 36px;
}
.stat-widget-four .stat-content {
  margin-left: 40px;
  text-align: center;
}
.stat-widget-four .stat-heading {
  font-size: 20px;
}
.stat-widget-five .stat-icon {
  border-radius: 100px;
  display: inline-block;
  position: absolute;
}
.stat-widget-five i {
  border-radius: 100px;
  display: block;
  font-size: 36px;
  padding: 30px;
}
.stat-widget-five .stat-content {
  margin-left: 100px;
  padding: 24px 0;
  position: relative;
  text-align: right;
  vertical-align: middle;
}
.stat-widget-five .stat-heading {
  text-align: right;
  padding-left: 80px;
  font-size: 20px;
  font-weight: 200;
}
.stat-widget-six {
  position: relative;
}
.stat-widget-six .stat-icon {
  display: inline-block;
  position: absolute;
  top: 5px;
}
.stat-widget-six i {
  display: block;
  font-size: 36px;
}
.stat-widget-six .stat-content {
  margin-left: 40px;
  text-align: center;
}
.stat-widget-six .stat-heading {
  font-size: 16px;
  font-weight: 300;
}
.stat-widget-six .stat-text {
  font-size: 12px;
  padding-top: 4px;
}
.stat-widget-seven .stat-heading {
  text-align: center;
}
.stat-widget-seven .gradient-circle {
  text-align: center;
  position: relative;
  margin: 30px auto;
  display: inline-block;
  width: 100%;
}
.stat-widget-seven .gradient-circle i {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  top: 35px;
  font-size: 30px;
}
.stat-widget-seven .stat-footer {
  text-align: center;
  margin-top: 30px;
}
.stat-widget-seven .stat-footer .stat-count {
  padding-left: 5px;
}
.stat-widget-seven .count-header {
  color: #252525;
  font-size: 12px;
  font-weight: 400;
  line-height: 30px;
}
.stat-widget-seven .stat-count {
  font-size: 18px;
  font-weight: 400;
  color: #252525;
}
.stat-widget-seven .analytic-arrow {
  position: relative;
}
.stat-widget-seven .analytic-arrow i {
  font-size: 12px;
}
/* Stat widget Eight
--------------------------- */
.stat-widget-eight {
  padding: 15px;
}
.stat-widget-eight .header-title {
  font-size: 20px;
  font-weight: 300;
}
.stat-widget-eight .ti-more-alt {
  color: #878787;
  cursor: pointer;
  left: -5px;
  position: absolute;
  transform: rotate(90deg);
}
.stat-widget-eight .stat-content {
  margin-top: 50px;
}
.stat-widget-eight .stat-content .ti-arrow-up {
  font-size: 30px;
  color: #28a745;
}
.stat-widget-eight .stat-content .stat-digit {
  font-size: 24px;
  font-weight: 300;
  margin-left: 15px;
}
.stat-widget-eight .stat-content .progress-stats {
  color: #aaadb2;
  font-weight: 400;
  position: relative;
  top: 10px;
}
.stat-widget-eight .progress {
  margin-bottom: 0;
  margin-top: 30px;
  height: 7px;
  background: #EAEAEA;
  box-shadow: none;
}
.stat-widget-nine .all-like {
  float: right;
}
.stat-widget-nine .stat-icon i {
  font-size: 22px;
}
.stat-widget-nine .stat-text {
  font-size: 14px;
}
.stat-widget-nine .stat-digit {
  font-size: 14px;
}
.stat-widget-nine .like-count {
  font-size: 30px;
}
.horizontal {
  position: relative;
}
.horizontal:before {
  background: #ffffff;
  bottom: 0;
  content: "";
  height: 38px;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  width: 1px;
}
.widget-ten span i {
  color: #ffffff;
  opacity: 0.5;
}
.widget-ten h5 {
  color: #ffffff;
}
.widget-ten p {
  color: #ffffff !important;
  opacity: 0.75;
}

/* Mixed Styles */

.badges {
    h1,h2,h3,h4,h5,h6 {
        margin: 5px 0;
    }
}
.vue-lists {
    ul,ol {
        padding-left: 30px;
    }
}
.card .dropdown.float-right {
    .dropdown-menu {
        left: inherit !important;
        right: 0 !important;
        top: 93% !important;
    }
}
.dataTables_paginate {
    .pagination {
        border-radius: 0;
        li {
            border-radius: 0 !important;
            a {
                border-radius: 0 !important;
                color: #272c33;
            }
            &.active {
                a {
                    background: #272c33;
                    border-color: #272c33;
                    color: #fff;
                }
            }
            &:hover {
                a {
                    background: #272c33;
                    border-color: #272c33;
                    color: #fff;
                }
            }
        }
    }
}



// Responsive Styles 

@media (max-width: 1368px) {
    .content {
        padding: 0 15px;
    }
    .twt-category {
        margin-bottom: 0;
    }
    .twt-feed {
        max-height: 155px;
        img {
            height: 75px;
            width: 75px;
        }
    }
    .stat-widget-one .stat-content {
        margin-left: 15px;
    }
    .card-body {
        padding: 15px;
    }
    .badges button {
        margin: 2px 0;
    }
}

@media (max-width: 1024px) {
    aside.left-panel {
        padding: 0 20px;
        width: 200px;
    }
    .navbar .navbar-nav li > a .menu-icon {
        width: 30px;
    }
    .navbar .navbar-nav li.menu-item-has-children .sub-menu {
        padding: 0 0 0 30px;
    }
    .navbar .navbar-nav li.menu-item-has-children .sub-menu a {
        padding: 2px 0 2px 25px;
    }
    .card {
        .card-header {
            position: relative;
            strong {
                display: block;
            }
            small {
                float: left;
            }
            .card-actions {
                right: 0;
                top: .75rem;
                position: absolute;
            }
        }
    }
}

@media (max-width: 992px) {
    [class*="col"].no-padding {
        flex: none;
    }
}
@media (max-width: 575.99px) { 
    body {
        display: block;
    }
    aside.left-panel {
        display: block;
        height: auto;
        min-height: inherit;
        padding: 0 15px;
        width: 100%;
        .navbar {
            margin-bottom: 0;
            .navbar-header {
                height: 50px;
            }
            .navbar-brand {
                border-bottom: none;
                display: inline-block;
                float: left;
                line-height: 1;
                margin-top: 11px;
                min-height: inherit;
                &.hidden {
                    display: none;
                }
            }
            .navbar-toggler {
                float: right;
                margin-top: 8px;
            }
            .navbar-nav li {
                > a {
                    padding: 5px 0;
                }
                &.menu-item-has-children a:before {
                    top: 5px;
                }
            }
            .menu-title {
                line-height: 30px;
                padding: 0;
            }
        }
    }
    .menutoggle {
        display: none;
    }
    .right-panel {
        display: block;
        header.header {
            padding: 5px 10px 1px 5px;
            div[class*="col"] {
                padding: 0;
                width: initial;
            }
            .col-sm-7 {
                float: left;
            }
            .col-sm-5 {
                float: right;
            }
        }
        .breadcrumbs {
            padding: 10px 15px;
            div[class*="col"] {
                padding: 0;
            }
            .page-header {
                min-height: inherit;
                padding: 0;
                h1 {
                    padding: 5px 0;
                }
                &.float-right {
                    float: left;
                    text-align: left;
                    width: 100%;
                    .breadcrumb {
                        padding: 0;
                    }
                }
            }
        }
        .content {
            padding: 0 10px;
            .card {
                .card-title {
                    margin-bottom: 0;
                }
                .card-footer {
                    padding: 15px 5px;
                    ul {
                        margin: 0;
                        li {
                            float: left;
                            margin: 5px 0;
                            padding: 0 10px;
                            width: 33.33%;
                        }
                    }
                }
            }
            div[class*="col"] {
                padding: 0;
            }
            .row {
                div[class*="col"] {
                    padding: 0 10px;
                }
            }
            .nav-tabs,
            .nav-pills {
                .nav-link {
                    padding: .5rem;
                }
            }
            .tab-content.pl-3 {
                padding-left: 0 !important;
            }
            #bootstrap-data-table_wrapper {
                font-size: 14px;
                padding: 0;
                div[class*="col"] {
                    padding: 0;
                }
                .table td, .table th {
                    padding: 5px;
                }
            }
        }
    }
}
The file variables.css contains:
/* Bootstrap */

The file variables.scss contains:
$container-bg: #f1f2f7;
$menu-bg: #272c33;
$header-color: #00acc1;
$header-bg: #fff;
$menu-color: #272c33;

$color-red-error: rgb(185, 74, 72);
$color-grey-arrow: rgba(204, 204, 204, 0.2);

$width-default: 220px; // 3 960px-grid columns

$zindex-select-dropdown: 1035; // must be lower than a modal background (1040) but higher than the fixed navbar (1030)


/* Bootstrap */
// stylelint-disable
$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #adb5bd !default;
$gray-600: #868e96 !default;
$gray-700: #495057 !default;
$gray-800: #343a40 !default;
$gray-900: #212529 !default;
$black:    #000 !default;

$header-color: #00acc1;
$bg-color: #272c33;
$section-title-bg: #263238;
$dark-text-color: #fff;
$dark-text-second-color: #99abb4;
$dark-border-color: rgba(120, 130, 140, 0.4);
$dark-hover-color: #868e96;

$border-color:                        $gray-200 !default;
$layout-transition-speed:             .25s !default;


// Cards
$card-spacer-y:            .75rem !default;
$card-spacer-x:            1.25rem !default;



// Social Colors

$facebook:                            #3b5998 !default;
$twitter:                             #00aced !default;
$linkedin:                            #4875b4 !default;
$google-plus:                         #d34836 !default;
$flickr:                              #ff0084 !default;
$tumblr:                              #32506d !default;
$xing:                                #026466 !default;
$github:                              #4183c4 !default;
$html5:                               #e34f26 !default;
$openid:                              #f78c40 !default;
$stack-overflow:                      #fe7a15 !default;
$youtube:                             #b00 !default;
$css3:                                #0170ba !default;
$dribbble:                            #ea4c89 !default;
$google-plus:                         #bb4b39 !default;
$instagram:                           #517fa4 !default;
$pinterest:                           #cb2027 !default;
$vk:                                  #45668e !default;
$yahoo:                               #400191 !default;
$behance:                             #1769ff !default;
$dropbox:                             #007ee5 !default;
$reddit:                              #ff4500 !default;
$spotify:                             #7ab800 !default;
$vine:                                #00bf8f !default;
$foursquare:                          #1073af !default;
$vimeo:                               #aad450 !default;

// Default BootStrap Variables
$text-muted:                  $gray-600 !default;


$spacer: 1rem !default;
$spacers: (
  0: 0,
  1: ($spacer * .25),
  2: ($spacer * .5),
  3: $spacer,
  4: ($spacer * 1.5),
  5: ($spacer * 3)
) !default;

$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`
$font-size-lg:                ($font-size-base * 1.25) !default;
$font-size-sm:                ($font-size-base * .875) !default;

$body-bg:                   $white !default;
$body-color:                $gray-900 !default;
$blue:    #007bff !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #dc3545 !default;
$orange:  #fd7e14 !default;
$yellow:  #ffc107 !default;
$green:   #28a745 !default;
$teal:    #20c997 !default;
$cyan:    #17a2b8 !default;

$primary:       $blue !default;
$secondary:     $gray-600 !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-100 !default;
$dark:          $gray-800 !default;

$theme-colors: () !default;
$theme-colors: map-merge((
  "primary":    $primary,
  "secondary":  $secondary,
  "success":    $success,
  "info":       $info,
  "warning":    $warning,
  "danger":     $danger,
  "light":      $light,
  "dark":       $dark
), $theme-colors);
// stylelint-enable
The file widgets.css contains:

The file _gauge.scss contains:
.gaugejs-wrap {
  position: relative;
  margin: 0 auto;
  canvas.gaugejs {
    width: 100% !important;
    height: auto !important;
  }
  i, &.sparkline .value {
    top: 50%;
    display: block;
    width: 100%;
    text-align: center;
  }
  i {
    position: absolute;
    left: 0;
    z-index: 1000;
    margin-top: -15px;
    font-size: 30px;
  }
  &.type-2 {
    .value {
      display: block;
      margin-top: -85px;
    }
    label {
      display: block;
      margin-top: -10px;
      font-size: 10px;
      font-weight: 600;
      color: #9da0a8;
      text-transform: uppercase;
    }
  }
  &.sparkline {
    position: relative;
    .value {
      position: absolute;
      margin-top: -5px;
      font-size: 10px;
      line-height: 10px;
    }
  }
}
The file _switches.scss contains:
@mixin switch-size($width, $height, $font-size, $handle-margin) {
  width: $width;
  height: $height;

  .switch-label {
    font-size: $font-size;
  }

  .switch-handle {
    width: $height - $handle-margin * 2;
    height: $height - $handle-margin * 2;
  }

  .switch-input:checked ~ .switch-handle {
    left: $width - $height + $handle-margin;
  }
}

@mixin switch($type, $width, $height, $font-size, $handle-margin) {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: $width;
  height: $height;
  background-color: transparent;
  cursor: pointer;

  .switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
  }

  .switch-label {
    position: relative;
    display: block;
    height: inherit;
    @if $type == icon {
      font-family: FontAwesome;
    }
    font-size: $font-size;
    font-weight: 600;
    text-transform: uppercase;
    @if $type == ddd {
      background-color: $gray-100;
    } @else {
      background-color: #fff;
    }
    border: 1px solid $border-color;
    border-radius: 2px;
    transition: opacity background .15s ease-out;
  }
  @if $type == text or $type == icon {
    .switch-label::before,
    .switch-label::after {
      position: absolute;
      top: 50%;
      width: 50%;
      margin-top: -.5em;
      line-height: 1;
      text-align: center;
      transition: inherit;
    }
    .switch-label::before {
      right: 1px;
      color: $gray-200;
      content: attr(data-off);
    }
    .switch-label::after {
      left: 1px;
      color: #fff;
      content: attr(data-on);
      opacity: 0;
    }
  }
  .switch-input:checked ~ .switch-label {
    //background: $gray-lightest;
  }
  .switch-input:checked ~ .switch-label::before {
    opacity: 0;
  }
  .switch-input:checked ~ .switch-label::after {
    opacity: 1;
  }

  .switch-handle {
    position: absolute;
    top: $handle-margin;
    left: $handle-margin;
    width: $height - $handle-margin * 2;
    height: $height - $handle-margin * 2;
    background: #fff;
    border: 1px solid $border-color;
    border-radius: 1px;
    transition: left .15s ease-out;
    @if $type == ddd {
      border: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, .3);
    }
  }

  .switch-input:checked ~ .switch-handle {
    left: $width - $height + $handle-margin;
  }


  @if $type == ddd {
    @extend .switch-pill;
  }

  //size variations
  @if $type == default {

    &.switch-lg {
      @include switch-size($switch-lg-width, $switch-lg-height, $switch-lg-font-size, $handle-margin);
    }
    &.switch-sm {
      @include switch-size($switch-sm-width, $switch-sm-height, $switch-sm-font-size, $handle-margin);
    }
    &.switch-xs {
      @include switch-size($switch-xs-width, $switch-xs-height, $switch-xs-font-size, $handle-margin);
    }

  } @else if $type == text {

    &.switch-lg {
      @include switch-size($switch-text-lg-width, $switch-text-lg-height, $switch-text-lg-font-size, $handle-margin);
    }
    &.switch-sm {
      @include switch-size($switch-text-sm-width, $switch-text-sm-height, $switch-text-sm-font-size, $handle-margin);
    }
    &.switch-xs {
      @include switch-size($switch-text-xs-width, $switch-text-xs-height, $switch-text-xs-font-size, $handle-margin);
    }

  } @else if $type == icon {

    &.switch-lg {
      @include switch-size($switch-icon-lg-width, $switch-icon-lg-height, $switch-icon-lg-font-size, $handle-margin);
    }
    &.switch-sm {
      @include switch-size($switch-icon-sm-width, $switch-icon-sm-height, $switch-icon-sm-font-size, $handle-margin);
    }
    &.switch-xs {
      @include switch-size($switch-icon-xs-width, $switch-icon-xs-height, $switch-icon-xs-font-size, $handle-margin);
    }

  } @else if $type == ddd {

    &.switch-lg {
      @include switch-size($switch-lg-width, $switch-lg-height, $switch-lg-font-size, 0);
    }
    &.switch-sm {
      @include switch-size($switch-sm-width, $switch-sm-height, $switch-sm-font-size, 0);
    }
    &.switch-xs {
      @include switch-size($switch-xs-width, $switch-xs-height, $switch-xs-font-size, 0);
    }
  }
}

@mixin switch-variant($color) {
  > .switch-input:checked ~ .switch-label {
    background: $color !important;
    border-color: darken($color,10%);
  }

  > .switch-input:checked ~ .switch-handle {
    border-color: darken($color,10%);
  }
}

@mixin switch-outline-variant($color) {
  > .switch-input:checked ~ .switch-label {
    background: #fff !important;
    border-color: $color;

    &::after {
      color: $color;
    }
  }

  > .switch-input:checked ~ .switch-handle {
    border-color: $color;
  }
}

@mixin switch-outline-alt-variant($color) {
  > .switch-input:checked ~ .switch-label {
    background: #fff !important;
    border-color: $color;

    &::after {
      color: $color;
    }
  }

  > .switch-input:checked ~ .switch-handle {
    background: $color !important;
    border-color: $color;
  }
}

$switch-lg-width: 48px;
$switch-lg-height: 28px;
$switch-lg-font-size: 12px;

$switch-width: 40px;
$switch-height: 24px;
$switch-font-size: 10px;

$handle-margin: 2px;

$switch-sm-width: 32px;
$switch-sm-height: 20px;
$switch-sm-font-size: 8px;

$switch-xs-width: 24px;
$switch-xs-height: 16px;
$switch-xs-font-size: 7px;


$switch-text-lg-width: 56px;
$switch-text-lg-height: 28px;
$switch-text-lg-font-size: 12px;

$switch-text-width: 48px;
$switch-text-height: 24px;
$switch-text-font-size: 10px;

$switch-text-sm-width: 40px;
$switch-text-sm-height: 20px;
$switch-text-sm-font-size: 8px;

$switch-text-xs-width: 32px;
$switch-text-xs-height: 16px;
$switch-text-xs-font-size: 7px;


$switch-icon-lg-width: 56px;
$switch-icon-lg-height: 28px;
$switch-icon-lg-font-size: 12px;

$switch-icon-width: 48px;
$switch-icon-height: 24px;
$switch-icon-font-size: 10px;

$switch-icon-sm-width: 40px;
$switch-icon-sm-height: 20px;
$switch-icon-sm-font-size: 8px;

$switch-icon-xs-width: 32px;
$switch-icon-xs-height: 16px;
$switch-icon-xs-font-size: 7px;

.switch.switch-default {
  @include switch('default', $switch-width, $switch-height, $switch-font-size, $handle-margin);
}

.switch.switch-text {
  @include switch('text', $switch-text-width, $switch-text-height, $switch-text-font-size, $handle-margin);
}

.switch.switch-icon {
  @include switch('icon', $switch-icon-width, $switch-icon-height, $switch-icon-font-size, $handle-margin);
}

.switch.switch-3d {
  @include switch('ddd', $switch-width, $switch-height, $switch-font-size, 0);
}

//pills style
.switch-pill {
  .switch-label,
  .switch-handle {
    border-radius: 50em !important;
  }

  .switch-label::before {
    right: 2px !important;
  }
  .switch-label::after {
    left: 2px !important;
  }
}

@each $color, $value in $theme-colors {
  //normal style
  .switch-#{$color} {
    @include switch-variant($value);
  }
  //outline style
  .switch-#{$color}-outline {
    @include switch-outline-variant($value);
  }
  //outline alternative style
  .switch-#{$color}-outline-alt {
    @include switch-outline-alt-variant($value);
  }
}

The file _widgets.scss contains:
// .social-box
.social-box {
  min-height: 160px;
  margin-bottom: 2 * .75rem;
  text-align: center;
  background: #fff;
  //border: 1px solid $card-border-color;
  //@include border-radius($card-border-radius);

  i {
    display: block;
    margin: -1px -1px 0;
    font-size: 40px;
    line-height: 90px;
    background: #e9ecef;

    //@include border-radius($card-border-radius $card-border-radius 0 0);
  }

  .chart-wrapper {
    height: 90px;
    margin: -90px 0 0;

    canvas {
      width: 100% !important;
      height: 90px !important;
    }
  }

  ul {
    padding: 10px 0;
    list-style: none;


    li {
      display: block;
      float: left;
      width: 50%;
      padding-top: 10px;
      font-size: 18px;

      &:first-child {
        border-right: 1px solid #c2cfd6;
      }

      strong {
        display: block;
        font-size: 20px;
      }

      span {
        font-size: 18px;
        font-weight: 500;
        color: #949CA0;
        text-transform: uppercase;
      }
    }
  }

  &.facebook {
    i {
      color: #fff;
      background: $facebook;
    }
  }

  &.twitter {
    i {
      color: #fff;
      background: $twitter;
    }
  }

  &.linkedin {
    i {
      color: #fff;
      background: $linkedin;
    }
  }

  &.google-plus {
    i {
      color: #fff;
      background: $google-plus;
    }
  }
}

.horizontal-bars {
  padding: 0;
  margin: 0;
  list-style: none;

  li {
    position: relative;
    height: 40px;
    line-height: 40px;
    vertical-align: middle;

    .title {
      width: 100px;
      font-size: 12px;
      font-weight: 600;
      color: $text-muted;
      vertical-align: middle;
    }

    .bars {
      position: absolute;
      top: 15px;
      width: 100%;
      padding-left: 100px;

      .progress:first-child {
        margin-bottom: 2px;
      }
    }

    &.legend {
      text-align: center;

      .badge {
        display: inline-block;
        width: 8px;
        height: 8px;
        padding: 0;
      }
    }

    &.divider {
      height: 40px;

      i {
        margin: 0 !important;
      }
    }
  }

  &.type-2 {

    li {
      overflow: hidden;

      i {
        display: inline-block;
        margin-right: $spacer;
        margin-left: 5px;
        font-size: 18px;
        line-height: 40px;
      }

      .title {
        display: inline-block;
        width: auto;
        margin-top: -9px;
        font-size: $font-size-base;
        font-weight: normal;
        line-height: 40px;
        color: $body-color;
      }

      .value {
        float: right;
        font-weight: 600;
      }

      .bars {
        position: absolute;
        top: auto;
        bottom: 0;
        padding: 0;
      }
    }
  }
}

.icons-list {
  padding: 0;
  margin: 0;
  list-style: none;

  li {
    position: relative;
    height: 40px;
    vertical-align: middle;

    i {
      display: block;
      float: left;
      width: 35px !important;
      height: 35px !important;
      margin: 2px;
      line-height: 35px !important;
      text-align: center;
    }

    .desc {
      height: 40px;
      margin-left: 50px;
      border-bottom: 1px solid $border-color;

      .title {
        padding: 2px 0 0;
        margin: 0;
      }
      small {
        display: block;
        margin-top: -4px;
        color: $text-muted;
      }
    }

    .value {
      position: absolute;
      top: 2px;
      right: 45px;
      text-align: right;

      strong {
        display: block;
        margin-top: -3px;
      }
    }

    .actions {
      position: absolute;
      top: -4px;
      right: 10px;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;

      i {
        float: none;
        width: auto;
        height: auto;
        padding: 0;
        margin: 0;
        line-height: normal;
      }
    }

    &.divider {
      height: 40px;

      i {
        width: auto;
        height: auto;
        margin: 2px 0 0;
        font-size: 18px;
      }
    }
  }
}

