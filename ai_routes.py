from flask import request, jsonify, render_template
from genai_integration import LogAnalysisAI, enhance_analyzer_with_ai
import os

def add_ai_routes(app):
    """Add AI-powered routes to the Flask app"""
    
    @app.route('/ai_analyze_error', methods=['POST'])
    def ai_analyze_error():
        """AI-powered error analysis endpoint"""
        data = request.get_json()
        error_message = data.get('error_message', '')
        api_key = os.getenv('OPENAI_API_KEY')
        
        ai_analyzer = LogAnalysisAI(api_key)
        
        # Get AI analysis
        analysis = ai_analyzer.analyze_error_pattern([error_message])
        kb_suggestions = ai_analyzer.suggest_kb_articles(error_message)
        
        return jsonify({
            'analysis': analysis,
            'kb_suggestions': kb_suggestions
        })
    
    @app.route('/ai_enhanced_upload', methods=['POST'])
    def ai_enhanced_upload():
        """Enhanced upload with AI analysis"""
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'})
        
        # Save file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Run AI-enhanced analysis
        api_key = os.getenv('OPENAI_API_KEY')
        results = enhance_analyzer_with_ai(filepath, api_key)
        
        if len(results) > 11:  # Enhanced results with AI
            ret_code, main_errors_stack, fullerrorstack, total_errors_count, \
            total_errors_removed, total_errors_left, percentage_reduction, \
            enhanced_threads, log_start_time, log_end_time, logage, \
            pattern_analysis, incident_report = results
            
            return render_template('ai_analyzed.html',
                                 main_errors_stack=main_errors_stack,
                                 fullerrorstack=fullerrorstack,
                                 total_errors_count=total_errors_count,
                                 total_errors_removed=total_errors_removed,
                                 total_errors_left=total_errors_left,
                                 percentage_reduction=percentage_reduction,
                                 allthreads_list=enhanced_threads,
                                 log_start_time=log_start_time,
                                 log_end_time=log_end_time,
                                 logage=logage,
                                 pattern_analysis=pattern_analysis,
                                 incident_report=incident_report,
                                 filename=filename)
        else:
            return jsonify({'error': 'Analysis failed'})
    
    @app.route('/ai_thread_insights/<thread_name>')
    def ai_thread_insights(thread_name):
        """Get AI insights for specific thread"""
        api_key = os.getenv('OPENAI_API_KEY')
        ai_analyzer = LogAnalysisAI(api_key)
        
        # This would need thread data from session or database
        # For demo purposes, returning placeholder
        insights = {
            'summary': f"AI analysis for thread {thread_name}",
            'recommendations': "Placeholder recommendations",
            'severity': "Medium"
        }
        
        return jsonify(insights)
    
    @app.route('/ai_dashboard')
    def ai_dashboard():
        """AI-powered dashboard with insights"""
        return render_template('ai_dashboard.html')