import re, os, sys, time, datetime
import fnmatch
from collections import deque

#Let's analyze it now


#These are the list of error codes with Possible solutions
master_error_dict = {'Unable to set timezone on vCenter server':'Failed to set timezone on vCenter Server,Follow KB525866 to resolve this issue','ddadapter.DataDomainPlugin: Failed to execute: elicense update':'Failed to Configure the DDVE License,This could be due to incorrect License File supplied or DDVE License files has issues such as "#" in the address parameter or maybe incorrect storage','Failed to apply DPA license':'Failed to Configure the DPA License,This could be due to incorrect License File supplied or DPA License files has issues such as "#" in the address parameter or extra space or maybe incorrect License Type','Exception org.elasticsearch.transport.BindTransportException: Failed to resolve publish address':'Failure in ElasticSearch Stack for the Publish Address,One of the DPSearch Services such as ES was not able to start. Workaround is to start the DPS Configuration from ACM Dashboard and once DPS Master VM is deployed we need to edit the elasticsearch.yml located in /etc/elasticsearch of this VM. The editing of the file had to be done as soon as the elasticsearch.yml file was created and before the system loaded the file.You need to replace this line : "network.publish_host: _site_" With : "network.publish_host: _local_" We have about 45 seconds from the time that the file is written to the master node to when the system read this file. Clearquest ID CQSDR0006253 opened against DP Search. KB 525083','com.emc.vcedpa.common.exception.ApplianceException: Data Domain data drive configuration failed':'Failed to configure data drive configuration on DDVE,This could be due to bad cleanup of the DDVE do the cleanup again for DDVE or Faulty NVMe Issue or Datastores not listed properly or see Escalation 33581','Proxy Deployment Failed Unable to find task info from vCenter for AVProxy':'Failed to deploy Avamar Proxy,This could be due to network issues therefore recommended to try manual deploy of proxy as a test using MCGUI before retrying the workflow.','Exception while deploying DPA Application':'Failed to deploy DPA,In one of such issues there was a connectivity issue between the vmnic and the switch you can check that using the ping and if you see high latency in time parameter.Another reason can be a password issue where password has special character such as hypen in it.','Failed to get MCLogin Info since there are no EMDB properties loaded':'Failed to configure AVE,This could be due to the company name having space in it','Search failed to update new LDAP':'Failed to update LDAP information on DPSearch,This could be due to payload issues with ACM API call to DPsearch. Try using the command from ACM and DPSearch and compare "ldapsearch -h <<LDAP server hostname or IP>> -p 389 -D <<Query Username>> -b <<Base Domain Name>> -w Idpa_1234"','ERROR: sm_cloudunit_create: cloud unit':'Failed to configure Cloud Tier,This could be due to a timeout issue with AWS. See KB 525492','Unable to connect to rabbit MQ service on localhost':'Failed to get ESXi Details,See Escalation 33273','OVF deployment failed with Exception':'Failed to deploy the inital OVF,This is most likely a DNS issue. Check the network again. See Escalation 33242','Error message Failed to setup Secure OpenLDAP on ACM.':'Failed to configure OpenLDAP,This could be a transient error message and retrying the workflow should automatically fix it. Some commands might have failed.','Failed to add NIC on Virtual Machine':'Failed to add NIC to DDVE VM,This could be due to improper cleanup performed. Make sure you disassociate ESXi from vCenter using actions menu.','Error occurred while creating virtual app DataProtection':'Failed to create the vAPP for IDPA,This could be due to improper cleanup. Login to VCSA and delete the stale vAPPs and then try the workflow again','Incorrect Locking Id value in Data Domain license file':'Incorrect Locking Id value in Data Domain license file,Please check the DDVE License file again','Failed to update LDAP on Search Master':'Failed to update LDAP on Search Master,AD Details are case-sensitive. Please use the in-case sensitive details','Failed to enable cloud':'Workflow Failed to run "enable cloud" command on DDVE,This could be caused due to issues with CDRA Config on DD therefore you ignore CDRA configuration at the beginning of the workflow and can later seperately install it','Failed to add interface':'DDVE experienced a failure in adding the interface ip to group, Unfortunately this is a non-reproducible issue by Engineering Team and most likely could be due to Temporary problem with the IP address assignment therefore recommendation is to re-run the workflow and you should not see this issue or there might be an issue with the IP assigned to DD therefore review the alerts show command on DDVE.','/usr/local/dataprotection/var/configmgr/server_data/config/dpcconfig.xmljava.io.FileNotFoundException':'DPC Deployment Failed due to missing file,If you performed a cleanup maybe you have deleted the dpcconfig.xml file kindly check that','ConfigAVEServerTask: Exception occurred while executing Avamar config server task' : 'Avamar Configuration received an exception,As of now there is no known cause of this error and If the workflow continues you can ignore this problem.','Exception occurred while executing deploy Avamar vApp task':'There was an exception while configuring the vApp for Avamar and therefore the process failed, This could be due to an improper cleanup of vCenter and Avamar vApp still present in the vCenter before the workflow was retried. Make sure to delete the Avamar vApp from the vCenter before retrying the operation.','rate limit response from server':'An exception has occurred when the workflow was trying to set the NTP on vCenter,This could be caused due to NTP Server not responding in timely manner. Review KB 528543 for more info.','Failed to execute: net show hardware':'Workflow failed to run a command on DDVE,This could be caused due to workflow unable to reach to DDVE IP address or there might be some conflict in the IP Address.Review the alerts show command for more information on this.','Failed to configure ports':'Workflow was unable to configure the networking on DDVE,This could be caused due to IP Conflict in the environment.You can try to manually assign the IP to DD Intergace to confirm or you can review alerts on DDVE via alerts show command and see the error message.','Failed to deploy the Proxy':'Failed to deploy Avamar Proxy,This could be due to network or timing issues therefore recommended to try manual deploy of proxy as a test using MCGUI before retrying the workflow. Also check the logs for exact problem if it says that the vCenter could not find the proxy then it is a timing issue.'}

'''
ps -ef | grep -i ovf
root     49432 42500 66 01:18 ?        00:00:20 /usr/lib/vmware-ovftool/ovftool.bin --acceptAllEulas --noSSLVerify --sourceType=OVA --name=DataProtection-VCSA --datastore=DP-appliance-datastore --diskMode=Thin --allowExtraConfig --X:injectOvfEnv --X:enableHiddenProperties --X:waitForIp --net:Network 1=DP-appliance-mgmt --ipAllocationPolicy=fixedPolicy --ipProtocol=IPv4 --powerOn --prop:guestinfo.cis.deployment.autoconfig=True --prop:guestinfo.cis.appliance.root.passwd=dbm3IGqa_loapgt --prop:guestinfo.cis.deployment.node.type=embedded --prop:guestinfo.cis.appliance.net.addr.family=ipv4 --prop:guestinfo.cis.appliance.net.addr=********* --prop:guestinfo.cis.appliance.net.prefix=22 --prop:guestinfo.cis.vmdir.domain-name=vSphere.local --prop:guestinfo.cis.appliance.net.gateway=********** --prop:guestinfo.cis.appliance.ssh.enabled=True --prop:guestinfo.cis.appliance.net.mode=static --prop:guestinfo.cis.vmdir.site-name=Default-First-Site --prop:guestinfo.cis.vmdir.password=dbm3IGqa_loapgt --prop:guestinfo.cis.appliance.net.dns.servers=*********,********* --prop:guestinfo.cis.appliance.net.pnid=norvlpvcb.credigy.net /data01/productrepo/vcsa/VMware-vCenter-Server-Appliance-6.5.0.20000-8307201_OVF10.ova vi://root:Idpa_1234@*********
root     49460  2238  0 01:19 pts/0    00:00:00 grep --color=auto -i ovf
--------------------------------^^^^^ Ricardo

2018-11-06 21:19:38,242 ERROR [pool-282-thread-2]-vi.ViJavaAccess: addNicOnVirtualMachine -->  Failed to add NIC on Virtual Machine: DDVE
2018-11-06 21:19:38,242 ERROR [pool-282-thread-2]-ddadapter.DeployDdveTask: Exception occurred while executing deploy Ddve task.

2018-10-05 15:33:15,750 ERROR [pool-11-thread-19]-vi.ViJavaAccess: getAllTask() -->  Unable to find task for given entity. error Message null
2018-10-05 15:33:15,753 ERROR [pool-11-thread-19]-avadapter.AvamarMcsdkUtil: deployProxyOnHost() Proxy Deployment Failed Unable to find task info from vCenter for AVProxy.
2018-10-05 15:33:15,753 ERROR [pool-11-thread-19]-avadapter.AvamarMcsdkUtil: deployProxy() Failed to deploy the Proxy. Reason: com.emc.vcedpa.common.exception.ApplianceException: Avamar proxy deployment failed.

#######
## 1
#######
stages_for_1_list = ['Executing VCSA deployment task']
stages_for_1_dict = {'Executing VCSA deployment task':'Failed during VCSA deployment,This can be due to failed resolution or other issues with ESXi. Review Logs for more information'}

#######
## 3
#######
stages_for_3_list = ['DD OVF deployed successfully','Ddve VM deployment successfully']
stages_for_3_dict = {'DD OVF deployed successfully':'','Ddve VM deployment successfully':'Failed to Deploy DD OVF,Either there is some issue with connecting with the ESXi or some other issues. Further logs Review Required'}


#######
## 5
#######
stages_for_5_list = ['Adding Nic to DDVE with portgroup name: DP-appliance-mgmt',' Assigning Portgroup to all NIC of DDVE', 'Reconfig task initiated for VM DDVE', 'Portgroup assigned to all NIC of DDVE', 'expandDDVEDataDisks Expanding data disks for DDVE', 'Reconfig task completed for VM DDVE with status success', 'Device configuration is created for 1st DS', 'expandDDVEDataDisks  Device configuration is created for 2nd DS']
stages_for_5_dict = {'Adding Nic to DDVE with portgroup name: DP-appliance-mgmt':'',' Assigning Portgroup to all NIC of DDVE':'', 'Reconfig task initiated for VM DDVE':'', 'Portgroup assigned to all NIC of DDVE':'', 'expandDDVEDataDisks Expanding data disks for DDVE':'', 'Reconfig task completed for VM DDVE with status success':'', 'Device configuration is created for 1st DS':'', 'expandDDVEDataDisks  Device configuration is created for 2nd DS':''}

#######
## 9
#######
stages_for_9_list = ['createCacheDisk  Device configuration is created for SSD datastore. Reconfiguring DDVE VM']

#######
## 10
#######
stages_for_10_list = ['DDVE deployed successfully','getServiceInstanceWithRetries--> Able to connect to vCenter','VCSA deployment is completed','Executing VCSA configuration task','Trying to assign administartor privileges to root user on VCSA = Administrator privileges assigned successfully','Trying to set root user account expiry to never on vCenter server','License assigned successfully']

#######
## 11
#######
stages_for_11_list = ['Datacenter created successfully. DPappliance','Trying to get sslthumbprint for ESX','echo -n | openssl s_client -connect','Successfully added the ESXi host','Check vSphere Profile-Driven Storage Service is not running 1 attempt','vSphere Profile-Driven Storage Service:vmware-sps is running','DeployAndConfigVcsaTask run--> VCSA configuration task is completed','IDPA model is 4400']

#######
## 12
#######
stages_for_12_list = ['Successfully set NTP Server','ntp.server.set --servers', 'timesync.set', 'shell ntpdate -u']

#######
## 13
#######
#Creating a list and then associated value based dict to easily print the values
stages_for_13_list = ['Successfully changed timezone on ACM', 'initializeOpenLdapServer-->Successfully initialized OpenLdap Server.', 'importSchema-->Successfully import OpenLDAP Schema','createBackendLdif--> Successfully setup backend of OpenLdap Server.','Successfully created OpenLDAP Basedomain on ACM','Set the password policy module','Set the password policy config','Set the password policy defaults','Created certificates for Secure OpenLdap','Successfully created group and user']

stages_for_13_dict = {'Successfully changed timezone on ACM':'Failed at Configuring ACM Timezone', 'initializeOpenLdapServer-->Successfully initialized OpenLdap Server.':'Failed to Initialize OpenLDAP Server', 'importSchema-->Successfully import OpenLDAP Schema':'Failed to import OpenLDAP Schema,This could be a transient error message and retrying the workflow should automatically fix it. Some commands might have failed.','createBackendLdif--> Successfully setup backend of OpenLdap Server.':'Failed to setup the Backend for OpenLDAP Server','Successfully created OpenLDAP Basedomain on ACM':'Failed to create OpenLDAP Basedomain','Set the password policy module':'Failed to set the Password Policy','Set the password policy config':'Failed to set the Password Policy','Set the password policy defaults':'Failed to set the Password Policy','Created certificates for Secure OpenLdap':'Failed to generate certificates for OpenLDAP','Successfully created group and user':'Failed to create Group and User for OpenLDAP'}

#######
## 14
#######
##NTP ISSUE DISCOVERED 
#here perform reverse search lookup
stages_for_14_list = ['Unable to set timezone on vCenter server']


#######
## 23  #DDVE License Issues Here
#######
stages_for_23_list = ['ddadapter.DataDomainPlugin: Failed to execute: elicense update']
stages_for_23_dict = {'ddadapter.DataDomainPlugin: Failed to execute: elicense update':'Failed to Configure the DDVE License,This could be due to incorrect License File supplied or DDVE License files has issues such as "#" in the address parameter or maybe incorrect storage'}


Failed to apply DPA license

server.log:2018-10-31 15:31:07,139 ERROR [Thread-971]-dpaadapter.DpaPlugin:  Lincese validation failed. License code mismatche. License Code Input: , Output: DPA_APPD_1TB
server.log:2018-10-31 15:31:07,139 ERROR [Thread-971]-dpaadapter.ConfigDPAApplicationTask: ApplianceException occured while configuring DPA Application server
server.log:2018-10-31 15:31:07,140 ERROR [Thread-971]-configure.DashboardWorkflowManager$1: Dashboard Workflow tasks failed for product DATA_PROTECTION_ADVISOR
===

server.log.1:2018-10-31 13:41:25,393 ERROR [pool-5-thread-17]-util.RestUtil: Return code is not same as expected. Expected Status:Created Response Status:Conflict Received Response :<error>
server.log.1:   <id>exception.license_validation_error_-516</id>
server.log.1:   <message>One or more licenses failed validation. Feature name:&apos;Enterprise Applications&apos; Error code: &apos;-516&apos; Error message &apos;Unable to process license DPA_APPD_1TB: Unexpected error in ELM API: Ma
jorErrorCode= -516, Message= SIGN or SIGN2 Required in License Certificate (-516,4035) ; No valid license string was provided. The string is set to &quot;{{{INCREMENT DPA_APPD_1TB EMCLM 1.0 permanent uncounted
server.log.1:   <entry name="errorCode">-516</entry>
server.log.1:   <entry name="validationErrorMessage">Unable to process license DPA_APPD_1TB: Unexpected error in ELM API: MajorErrorCode= -516, Message= SIGN or SIGN2 Required in License Certificate (-516,4035) ; No valid license stri
ng was provided. The string is set to &quot;{{{INCREMENT DPA_APPD_1TB EMCLM 1.0 permanent uncounted \
server.log.1:</error>
server.log.1:2018-10-31 13:41:25,393 ERROR [pool-5-thread-17]-dpaadapter.DpaPlugin:  Unable to execute request for apply license. Request url https://192.9.203.132:9002/dpa-api/license
=========

2018-10-26 13:33:36,837 INFO  [pool-5-thread-7]-util.SSHUtil:   STDOUT   : [
                    Existing licenses:
                    No licenses found.
                    New licenses:]
2018-10-26 13:33:36,838 INFO  [pool-5-thread-7]-util.SSHUtil:   STDERR   : [
                    **** Licenses file content error:
                    **** DDVE_CAPACITY: not available.
                    **** DDVE_REPLICATION: not available.
                    **** DDVE_ENCRYPTION: not available.
                    **** DDVE_DDBOOST: not available.
                    **** DDVE_CLOUDTIER_CAPACITY: not available.]
2018-10-26 13:33:36,838 ERROR [pool-5-thread-7]-util.SSHUtil: Failed to executed remote command using SSH.
2018-10-26 13:33:36,838 INFO  [pool-5-thread-7]-ddadapter.DataDomainPlugin: Failed to execute: elicense update

============
2018-10-26 13:33:36,839 ERROR [pool-5-thread-7]-ddadapter.ConfigDataDomainTask: ApplianceException occurred while executing Datadomain config task.
com.emc.vcedpa.common.exception.ApplianceException: Failed to configure license.
        at com.emc.vcedpa.ddadapter.DataDomainPlugin.addLicense(DataDomainPlugin.java:635)
        at com.emc.vcedpa.ddadapter.ConfigDataDomainTask.configureDD(ConfigDataDomainTask.java:260)
        at com.emc.vcedpa.ddadapter.ConfigDataDomainTask.run(ConfigDataDomainTask.java:1820)
        at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
        at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
'''


def failed_at_percentage(percentage, sub_file_part_1, sub_file_part_1_list):

    cause = 'Undefined'
    resolution = 'Undefined'

    for key,value in master_error_dict.items():
        if key in sub_file_part_1:
            print('Key Found : ' + str(key))
            print('Cause      : Workflow ' + str(value.split(',')[0]))

            cause = str(value.split(',')[0])
            print('Resolution : ' + str(value.split(',')[1]))
            resolution = str(value.split(',')[1])										
            break	 #Break because we only want the LAST result which is break is outside the loop	

    return 	cause, resolution	
