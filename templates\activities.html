<!doctype html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>LAFI Activities</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="apple-touch-icon" href="apple-icon.png">
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/normalize.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/bootstrap.min.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/font-awesome.min.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/themify-icons.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/flag-icon.min.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/css/cs-skin-elastic.css')}}">
    <link rel="stylesheet" href="{{url_for('static', filename='assets/scss/style.css')}}">
    <link href="{{url_for('static', filename='assets/css/lib/vector-map/jqvmap.min.css')}}" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Open+Sans:400,600,700,800' rel='stylesheet' type='text/css'>

<style> 
.card {
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  transition: 0.3s;
  width: 100%;
  border-radius: 5px;
  background: #F5F5F5;
}

.card:hover {
  box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
}

.card1 {
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  transition: 0.3s;
  width: 100%;
  height: 340px; 
  border-radius: 5px;
  background: white;
}

.card1:hover {
  box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
    background-color: #FFFFFA;
}

.button1 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width:10%;
    padding: 0.5%;
    background: #008CBA;
    margin: 5px ;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
}
.button1:hover {
  background-color: #00CED1;
  color: white;
}

.button3 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width:70%;
    padding: 1%;
    background: #008CBA;
    margin: 5px ;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
}
.button3:hover {
  background-color: #00CED1;
  color: white;
}


.button5 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width:20%;
    padding: 1%;
    background: white;
}
.button5:hover {
  background-color:#FFF8DC;
  color: white;
}


.button7 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width:20%;
    padding: 1%;
    background: white;
}
.button7:hover {
  background-color:#20B2AA;
  color: white;
}



.button2 {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width:70%;
    padding: 3%;
    background: #008CBA;
    margin: 5px ;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;    
    color: #fff;
}
.button2:hover {
  background-color: #00CED1;
  color: white;
}
table
{
    table-layout: fixed;
    width: 350px;

}

table1
{
  table-layout: fixed;
  width: 350px;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  transition: 0.3s;
  width: 100%;
  border-radius: 5px;
  background: #F5F5F5;	
}


.footer {
   position: fixed;
   left: 0;
   bottom: 0;
   width: 100%;
   background-color: #fc3503;
   color: yellow;
   text-align: center;
}
body{font-size: 80%};
#progressbar {
  background-color: black;
  border-radius: 13px;
  /* (height of inner div) / 2 + padding */
  padding: 3px;
}

#progressbar>div {
  background-color: orange;
  width: 40%;
  /* Adjust with JavaScript */
  height: 20px;
  border-radius: 10px;
}


tfoot input {
        width: 100%;
        padding: 3px;
        box-sizing: border-box;
    }
</style>



<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-F72SBSX0JW"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-F72SBSX0JW');
</script>

</head>
<body id="pande">
<!-- Left Panel -->

    <!-- Right Panel -->

    <div id="right-panel" class="right-panel">
      
 

<script>
$(document).ready(function() {
    $('#bootstrap-data-table').DataTable( {
        aaSorting: [[ 0, 'desc' ]]

    } );
} );
</script>
	
	
<div class="content mt-3">
<h3>&nbsp;&nbsp;&nbsp;Activities</h3><br><br>

<table id="bootstrap-data-table" class="table table-striped table-bordered dataTable no-footer" role="grid" aria-describedby="bootstrap-data-table_info" style="background: white;box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);transition: 0.3s;border-radius: 5px;">

    <colgroup>
       <col span="1" style="width: 1%;">
       <col span="1" style="width: 2%;">
       <col span="1" style="width: 1%;">
       <col span="1" style="width: 15%;">
	   
    </colgroup>

    <thead class="sorting_desc">
        <tr role="row">
            {% for item in act_head%}
            <th>{{item}}</th>
            {% endfor %}
        </tr>
    </thead>
    <tbody>
        {% for elem in act_msg %}
        <tr role="row" class="odd">
                {% for item in elem%}
                <td class="sorting_1">
                {{item}}
                </td>
                {% endfor %}
        </tr>
        {% endfor %}
    </tbody>
</table>




<br><br>
<a href="/"><button class="button button1">Home</button></a>

<a href="reviewboard"><button class="button button1">ReviewBoard</button></a>
<br>
<center>__________________________________________________________________________________<br>
IDPA LOG PARSER ACTIVITIES <br>
</center>
<br><br><br>
</div>
<!-- .content -->
    </div><!-- /#right-panel -->

    <!-- Right Panel -->

    <script src="{{url_for('static', filename='assets/js/vendor/jquery-2.1.4.min.js')}}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js"></script>
    <script src="{{url_for('static', filename='assets/js/plugins.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/main.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/widgets.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/dashboard.js')}}"></script>

    <script src="{{url_for('static', filename='assets/js/vendor/jquery-3.5.1.js')}}"></script> 
    <script src="{{url_for('static', filename='assets/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/datatables.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/dataTables.bootstrap.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/dataTables.buttons.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.bootstrap.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/jszip.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/pdfmake.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/vfs_fonts.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.html5.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.print.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/buttons.colVis.min.js')}}"></script>
    <script src="{{url_for('static', filename='assets/js/lib/data-table/datatables-init.js')}}"></script>
	

<script type="text/javascript">
    function ShowLoading_2(e) {
        var elmnt = document.getElementById("pande");	
        elmnt.scrollIntoView();	
        var div = document.createElement('div');
        var img = document.createElement('img');
	    var imgArray = ['{{url_for('static', filename='gear2.gif')}}']
	    var randomImage = Math.floor(Math.random()*imgArray.length);
        //img.src = 'b6a8F5G.gif';
        img.src = imgArray[randomImage];
        div.innerHTML = "<br><br>Running HealthCheck...Please wait.<br>Please do not refresh this screen.<br><br>";
        div.style.cssText = 'position: absolute;top: 30%; left: 0;  right: 0;  margin-top: 0; text-align: center;font-size: 25px; background:rgba(255,255,255,0.9); padding:0 20px;box-sizing:border-box;';
        div.appendChild(img);
        document.body.appendChild(div);
        return true;
    }
</script> 
	
	
		<script type="text/javascript">
    function ShowLoading(e) {
        var elmnt = document.getElementById("pande");	
        elmnt.scrollIntoView();	
        var div = document.createElement('div');
        var img = document.createElement('img');
	    var imgArray = ['{{url_for('static', filename='gear2.gif')}}']
	    var randomImage = Math.floor(Math.random()*imgArray.length);
        //img.src = 'b6a8F5G.gif';
        img.src = imgArray[randomImage];
        div.innerHTML = "";
        //div.style.cssText = 'position: absolute;top: 30%; left: 0;  right: 0;  margin-top: 0; text-align: center;font-size: 25px; background:rgba(255,255,255,0.9); padding:0 20px;box-sizing:border-box;';
        div.style.cssText = 'position: absolute; top: 50%; left: 0;  right: 0;  margin-top: 0; text-align: center;font-size: 25px; ';		
        div.appendChild(img);
        document.body.appendChild(div);
        return true;
        // These 2 lines cancel form submission, so only use if needed.
        //window.event.cancelBubble = true;
        //e.stopPropagation();
    }
	</script>
	
 
<script>
function flushactivities() {
  var txt;
  var r = confirm("This process will delete all activities.");
  if (r == true) {
	
    var xmlHttp = new XMLHttpRequest();
    xmlHttp.open( "GET", '/flushactivities', false ); // false for synchronous request
    xmlHttp.send( null );
  } else {
    txt = "Cancelled the operation...";
  }
  document.getElementById("demo").innerHTML = txt;
}
</script>

 
<script>
function myFunction2() {
  var txt;
  var r = confirm("This process will delete all existing catalogs and their associated log files.");
  if (r == true) {
	
    var xmlHttp = new XMLHttpRequest();
    xmlHttp.open( "GET", '/cleanup_catalog', false ); // false for synchronous request
    xmlHttp.send( null );
  } else {
    txt = "Cancelled the operation...";
  }
  document.getElementById("demo").innerHTML = txt;
}
</script>

<script>
function myFunction() {
  var txt;
  var r = confirm("You are about to perform Factory Reset. This option will delete everything including master database, all existing catalogs and log files.");
  if (r == true) {

    var xmlHttp = new XMLHttpRequest();
    xmlHttp.open( "GET", '/cleanup', false ); // false for synchronous request
    xmlHttp.send( null );
  } else {
    txt = "Cancelled the operation...";
  }
  document.getElementById("demo").innerHTML = txt;
}
</script>

	
    <script>
        ( function ( $ ) {
            "use strict";

            jQuery( '#vmap' ).vectorMap( {
                map: 'world_en',
                backgroundColor: null,
                color: '#ffffff',
                hoverOpacity: 0.7,
                selectedColor: '#1de9b6',
                enableZoom: true,
                showTooltip: true,
                values: sample_data,
                scaleColors: [ '#1de9b6', '#03a9f5' ],
                normalizeFunction: 'polynomial'
            } );
        } )( jQuery );
    </script>

<div class="footer">
</div></body>
</html>
