"""
AI-Enhanced Solution Generator for IDPA Log Analyzer
This module generates intelligent solutions and troubleshooting steps for errors
"""

import openai
import json
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
import sqlite3
from datetime import datetime

@dataclass
class AISolution:
    """Data class for AI-generated solutions"""
    error_message: str
    solution_summary: str
    immediate_actions: List[str]
    detailed_steps: List[str]
    prevention_measures: List[str]
    escalation_criteria: str
    estimated_resolution_time: str
    confidence_score: float
    references: List[str]

class AISolutionGenerator:
    """AI-powered solution generator that creates contextual troubleshooting guides"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo", db_path: str = "masterdb.db"):
        """
        Initialize the AI Solution Generator
        
        Args:
            api_key: OpenAI API key
            model: Model to use
            db_path: Path to the existing solution database
        """
        openai.api_key = api_key
        self.model = model
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # IDPA-specific solution templates
        self.solution_templates = {
            'network': {
                'immediate': ['Check network connectivity', 'Verify firewall rules', 'Test DNS resolution'],
                'escalation': 'Network team if connectivity issues persist > 30 minutes'
            },
            'authentication': {
                'immediate': ['Verify credentials', 'Check account status', 'Review authentication logs'],
                'escalation': 'Security team if authentication failures continue'
            },
            'storage': {
                'immediate': ['Check disk space', 'Verify storage connectivity', 'Review storage logs'],
                'escalation': 'Storage team if capacity or performance issues detected'
            }
        }
    
    def generate_solution(self, error_message: str, error_context: Dict, 
                         system_info: Dict = None) -> AISolution:
        """
        Generate a comprehensive solution for an error
        
        Args:
            error_message: The error message to solve
            error_context: Context including thread info, timestamp, component
            system_info: Optional system configuration information
            
        Returns:
            AISolution object with comprehensive troubleshooting guide
        """
        try:
            # Check if we have similar solutions in the database
            similar_solutions = self._find_similar_solutions(error_message)
            
            # Create AI prompt with context
            prompt = self._create_solution_prompt(
                error_message, error_context, similar_solutions, system_info
            )
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_solution_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,  # Low temperature for consistent solutions
                max_tokens=1500
            )
            
            ai_response = response.choices[0].message.content
            return self._parse_solution_response(error_message, ai_response)
            
        except Exception as e:
            self.logger.error(f"Solution generation failed: {str(e)}")
            return self._create_fallback_solution(error_message)
    
    def generate_bulk_solutions(self, error_list: List[Dict]) -> List[AISolution]:
        """
        Generate solutions for multiple errors efficiently
        
        Args:
            error_list: List of error dictionaries with message and context
            
        Returns:
            List of AISolution objects
        """
        solutions = []
        
        # Group similar errors to optimize API calls
        error_groups = self._group_similar_errors(error_list)
        
        for group in error_groups:
            try:
                # Generate solution for the group representative
                representative_error = group[0]
                solution = self.generate_solution(
                    representative_error['message'],
                    representative_error['context']
                )
                
                # Apply solution to all errors in the group
                for error in group:
                    customized_solution = self._customize_solution_for_error(
                        solution, error['message'], error['context']
                    )
                    solutions.append(customized_solution)
                    
            except Exception as e:
                self.logger.error(f"Bulk solution generation failed for group: {str(e)}")
                # Add fallback solutions for the group
                for error in group:
                    solutions.append(self._create_fallback_solution(error['message']))
        
        return solutions
    
    def create_troubleshooting_workflow(self, thread_data: Dict) -> Dict:
        """
        Create a comprehensive troubleshooting workflow for a thread
        
        Args:
            thread_data: Complete thread information including all errors
            
        Returns:
            Structured troubleshooting workflow
        """
        try:
            thread_name = thread_data.get('thread_name', 'Unknown')
            errors = thread_data.get('errors', [])
            component = thread_data.get('component', 'Other')
            
            prompt = f"""
            Create a comprehensive troubleshooting workflow for this IDPA thread:
            
            Thread: {thread_name}
            Component: {component}
            Errors ({len(errors)} total):
            {chr(10).join([f"- {error}" for error in errors[:10]])}
            
            Create a structured workflow with:
            1. Initial assessment steps
            2. Diagnostic procedures
            3. Resolution steps (ordered by likelihood of success)
            4. Verification procedures
            5. Prevention measures
            6. Escalation path
            
            Format as JSON with clear step-by-step instructions.
            """
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_workflow_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            self.logger.error(f"Workflow generation failed: {str(e)}")
            return self._create_fallback_workflow(thread_data)
    
    def _find_similar_solutions(self, error_message: str, limit: int = 5) -> List[Dict]:
        """Find similar solutions from the existing database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Extract key terms from error message for similarity search
            key_terms = self._extract_key_terms(error_message)
            
            similar_solutions = []
            for term in key_terms[:3]:  # Use top 3 key terms
                cursor.execute("""
                    SELECT ERROR, S360_KB, LIGHTNING_KB, SUPPORT_COMMENT, ENGG_COMMENT 
                    FROM ERRORLOG 
                    WHERE ERROR LIKE ? AND IS_VALID = 'True'
                    LIMIT ?
                """, (f'%{term}%', limit))
                
                results = cursor.fetchall()
                for result in results:
                    similar_solutions.append({
                        'error': result[0],
                        'kb_ref': result[1],
                        'lightning_kb': result[2],
                        'support_comment': result[3],
                        'engg_comment': result[4]
                    })
            
            conn.close()
            return similar_solutions[:limit]
            
        except Exception as e:
            self.logger.error(f"Database search failed: {str(e)}")
            return []
    
    def _create_solution_prompt(self, error_message: str, context: Dict, 
                               similar_solutions: List[Dict], system_info: Dict = None) -> str:
        """Create a detailed prompt for solution generation"""
        similar_text = ""
        if similar_solutions:
            similar_text = "Similar resolved cases:\n"
            for i, sol in enumerate(similar_solutions[:3], 1):
                similar_text += f"{i}. Error: {sol['error'][:100]}...\n"
                if sol['support_comment']:
                    similar_text += f"   Solution: {sol['support_comment'][:150]}...\n"
        
        system_context = ""
        if system_info:
            system_context = f"System Context: {json.dumps(system_info, indent=2)}\n"
        
        return f"""
        Generate a comprehensive solution for this IDPA error:
        
        Error: {error_message}
        Component: {context.get('component', 'Unknown')}
        Thread: {context.get('thread_name', 'Unknown')}
        Timestamp: {context.get('timestamp', 'Unknown')}
        Severity: {context.get('severity', 'Medium')}
        
        {system_context}
        {similar_text}
        
        Provide solution in this JSON format:
        {{
            "solution_summary": "Brief description of the solution approach",
            "immediate_actions": ["Action 1", "Action 2", "Action 3"],
            "detailed_steps": [
                "Step 1: Detailed instruction",
                "Step 2: Detailed instruction",
                "Step 3: Detailed instruction"
            ],
            "prevention_measures": ["Prevention 1", "Prevention 2"],
            "escalation_criteria": "When to escalate to next level support",
            "estimated_resolution_time": "15-30 minutes",
            "confidence_score": 0.85,
            "references": ["KB article", "Documentation link"]
        }}
        
        Focus on IDPA-specific procedures and Dell EMC best practices.
        """
    
    def _get_solution_system_prompt(self) -> str:
        """System prompt for solution generation"""
        return """
        You are a senior Dell EMC IDPA support engineer with 10+ years of experience.
        You specialize in creating clear, actionable troubleshooting procedures for:
        
        - IDPA deployment and configuration issues
        - vCenter integration problems
        - Avamar backup/restore failures
        - Data Domain connectivity and performance
        - Network and authentication issues
        - Component interaction problems
        
        Your solutions should be:
        1. Step-by-step and easy to follow
        2. Include verification steps
        3. Provide escalation criteria
        4. Consider enterprise environment constraints
        5. Include prevention measures
        
        Always prioritize data protection and system stability.
        """
    
    def _get_workflow_system_prompt(self) -> str:
        """System prompt for workflow generation"""
        return """
        You are an expert IDPA system administrator creating troubleshooting workflows.
        Create comprehensive, logical workflows that guide users through systematic problem resolution.
        
        Workflows should include:
        - Clear decision points
        - Parallel investigation paths when appropriate
        - Risk assessment at each step
        - Rollback procedures when needed
        - Documentation requirements
        """
    
    def _parse_solution_response(self, error_message: str, ai_response: str) -> AISolution:
        """Parse AI response into structured solution"""
        try:
            if ai_response.strip().startswith('{'):
                data = json.loads(ai_response)
                return AISolution(
                    error_message=error_message,
                    solution_summary=data.get('solution_summary', 'Solution analysis'),
                    immediate_actions=data.get('immediate_actions', []),
                    detailed_steps=data.get('detailed_steps', []),
                    prevention_measures=data.get('prevention_measures', []),
                    escalation_criteria=data.get('escalation_criteria', 'Escalate if issue persists'),
                    estimated_resolution_time=data.get('estimated_resolution_time', '30-60 minutes'),
                    confidence_score=data.get('confidence_score', 0.7),
                    references=data.get('references', [])
                )
            else:
                return self._parse_freeform_solution(error_message, ai_response)
                
        except Exception as e:
            self.logger.error(f"Failed to parse solution response: {str(e)}")
            return self._create_fallback_solution(error_message)
    
    def _parse_freeform_solution(self, error_message: str, response: str) -> AISolution:
        """Parse free-form solution response"""
        # Extract immediate actions (look for numbered lists or bullet points)
        immediate_actions = []
        lines = response.split('\n')
        for line in lines:
            if any(indicator in line.lower() for indicator in ['immediate', 'first', 'step 1']):
                immediate_actions.append(line.strip())
        
        return AISolution(
            error_message=error_message,
            solution_summary=response[:200] + "..." if len(response) > 200 else response,
            immediate_actions=immediate_actions or ["Review AI-generated solution"],
            detailed_steps=[response],
            prevention_measures=["Implement monitoring for similar issues"],
            escalation_criteria="Escalate if resolution attempts fail",
            estimated_resolution_time="30-60 minutes",
            confidence_score=0.6,
            references=[]
        )
    
    def _create_fallback_solution(self, error_message: str) -> AISolution:
        """Create fallback solution when AI fails"""
        return AISolution(
            error_message=error_message,
            solution_summary="Manual investigation required - AI solution unavailable",
            immediate_actions=[
                "Review error message and context",
                "Check system logs for related issues",
                "Verify component status"
            ],
            detailed_steps=[
                "1. Document the error occurrence time and frequency",
                "2. Check system resources (CPU, memory, disk space)",
                "3. Review network connectivity",
                "4. Consult IDPA documentation",
                "5. Contact Dell EMC support if issue persists"
            ],
            prevention_measures=["Implement regular system monitoring"],
            escalation_criteria="Escalate immediately due to AI analysis failure",
            estimated_resolution_time="60+ minutes",
            confidence_score=0.3,
            references=["Dell EMC IDPA Documentation", "Support Portal"]
        )
    
    def _extract_key_terms(self, error_message: str) -> List[str]:
        """Extract key terms from error message for similarity search"""
        # Remove common words and extract meaningful terms
        import re
        
        # Remove timestamps, thread names, and common log prefixes
        cleaned = re.sub(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}', '', error_message)
        cleaned = re.sub(r'\[[^\]]+\]', '', cleaned)
        cleaned = re.sub(r'-[^:]+:', '', cleaned)
        
        # Extract meaningful terms (3+ characters, not common words)
        terms = re.findall(r'\b[A-Za-z]{3,}\b', cleaned)
        common_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
        
        key_terms = [term.lower() for term in terms if term.lower() not in common_words]
        return list(set(key_terms))  # Remove duplicates
    
    def _group_similar_errors(self, error_list: List[Dict]) -> List[List[Dict]]:
        """Group similar errors to optimize solution generation"""
        # Simple grouping by error message similarity
        groups = []
        processed = set()
        
        for i, error in enumerate(error_list):
            if i in processed:
                continue
                
            group = [error]
            processed.add(i)
            
            # Find similar errors
            for j, other_error in enumerate(error_list[i+1:], i+1):
                if j in processed:
                    continue
                    
                # Simple similarity check (can be enhanced with more sophisticated methods)
                if self._are_errors_similar(error['message'], other_error['message']):
                    group.append(other_error)
                    processed.add(j)
            
            groups.append(group)
        
        return groups
    
    def _are_errors_similar(self, error1: str, error2: str, threshold: float = 0.6) -> bool:
        """Check if two errors are similar enough to group together"""
        # Simple similarity based on common terms
        terms1 = set(self._extract_key_terms(error1))
        terms2 = set(self._extract_key_terms(error2))
        
        if not terms1 or not terms2:
            return False
        
        intersection = len(terms1.intersection(terms2))
        union = len(terms1.union(terms2))
        
        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold
    
    def _customize_solution_for_error(self, base_solution: AISolution, 
                                    error_message: str, context: Dict) -> AISolution:
        """Customize a base solution for a specific error"""
        # Create a new solution with customized error message
        return AISolution(
            error_message=error_message,
            solution_summary=base_solution.solution_summary,
            immediate_actions=base_solution.immediate_actions,
            detailed_steps=base_solution.detailed_steps,
            prevention_measures=base_solution.prevention_measures,
            escalation_criteria=base_solution.escalation_criteria,
            estimated_resolution_time=base_solution.estimated_resolution_time,
            confidence_score=base_solution.confidence_score * 0.9,  # Slightly lower confidence for adapted solution
            references=base_solution.references
        )
    
    def _create_fallback_workflow(self, thread_data: Dict) -> Dict:
        """Create fallback workflow when AI generation fails"""
        return {
            "workflow_name": f"Manual Investigation - {thread_data.get('thread_name', 'Unknown Thread')}",
            "initial_assessment": [
                "Document all error messages and timestamps",
                "Identify affected components",
                "Check system resource utilization"
            ],
            "diagnostic_procedures": [
                "Review system logs for patterns",
                "Test component connectivity",
                "Verify configuration settings"
            ],
            "resolution_steps": [
                "Apply standard troubleshooting procedures",
                "Consult documentation",
                "Contact support if needed"
            ],
            "verification": [
                "Monitor system for error recurrence",
                "Verify all components are functioning"
            ],
            "escalation_path": "Contact Dell EMC support with collected diagnostic information"
        }
