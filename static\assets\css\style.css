/* This css file is to over write bootstarp css
--------------------------------------------------------- /
* Theme Name: Sufee-Admin Admin Template
* Theme URI: http://demos.jeweltheme.com/Sufee-Admin/
* Author: jewel_theme
* Author URI: http://themeforest.net/user/jewel_theme/portfolio
* Description:
* Version: 1.0.0
* License: GNU General Public License v2 or later
* License URI: http://www.gnu.org/licenses/gpl-2.0.html
* Tags: html, themplate, Sufee-Admin
--------------------------------------------------------- */
.bg-flat-color-1 {
  background: #20a8d8; }

.bg-flat-color-2 {
  background: #63c2de; }

.bg-flat-color-3 {
  background: #ffc107; }

.bg-flat-color-4 {
  background: #f86c6b; }

.bg-flat-color-5 {
  background: #4dbd74; }

.transition {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease; }

body {
  background: #f1f2f7;
  display: table;
  font-family: 'Open Sans' sans-serif;
  font-size: 16px;
  width: 100%; }

div[class*="col-"] {
  float: left; }

p {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px; }

p:focus {
  border: none;
  outline: 0; }

a, button {
  text-decoration: none;
  outline: none !important;
  color: #878787;
  -webkit-transition: all 0.25s ease;
  -moz-transition: all 0.25s ease;
  -ms-transition: all 0.25s ease;
  -o-transition: all 0.25s ease;
  transition: all 0.25s ease; }
  a:active, a.active, button:active, button.active {
    background: none !important;
    border: none !important; }

a:hover,
a:focus {
  text-decoration: none;
  color: #000; }

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0; }

ul,
ol {
  padding-left: 0; }

.btn:focus,
button:focus {
  box-shadow: none !important;
  outline: 0; }

img {
  max-width: 100%; }

/* Global Styles */
/* Main Styles */
.basix-container {
  display: table;
  min-height: 100vh;
  position: relative;
  width: 100%; }

aside.left-panel {
  background: #272c33;
  display: table-cell;
  height: 100vh;
  min-height: 100%;
  padding: 0 25px;
  vertical-align: top;
  width: 280px;
  transition: width 0.3s ease; }

.navbar {
  background: #272c33;
  border-radius: 0;
  border: none;
  display: block;
  margin: 0;
  margin-bottom: 100px;
  padding: 0; }
  .navbar .navbar-header {
    float: none;
    text-align: center;
    width: 100%; }
  .navbar .navbar-brand {
    border-bottom: 1px solid #4e4e52;
    color: #f1f2f7 !important;
    font-family: 'Open Sans';
    font-size: 22px;
    float: none;
    line-height: 50px;
    margin: 0;
    text-align: left;
    text-transform: capitalize;
    display: block;
    min-height: 69px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative; }
    .navbar .navbar-brand span {
      font-weight: 600; }
    .navbar .navbar-brand img {
      max-width: 160px; }
    .navbar .navbar-brand.hidden {
      display: none; }
  .navbar .menu-title {
    border-bottom: 1px solid #4e4e52;
    color: #9496a1;
    clear: both;
    display: block;
    font-family: 'Open Sans';
    font-size: 14px;
    font-weight: 700;
    line-height: 50px;
    padding: 15px 0 0 0;
    text-transform: uppercase;
    width: 100%; }
  .navbar .navbar-nav {
    float: none;
    position: relative; }
    .navbar .navbar-nav li {
      width: 100%; }
      .navbar .navbar-nav li.active .menu-icon, .navbar .navbar-nav li:hover .toggle_nav_button:before,
      .navbar .navbar-nav li .toggle_nav_button.nav-open:before {
        color: #fff !important; }
      .navbar .navbar-nav li > a {
        background: none !important;
        color: #c8c9ce !important;
        display: inline-block;
        font-family: 'Open Sans';
        font-size: 14px;
        line-height: 30px;
        padding: 10px 0;
        position: relative;
        width: 100%; }
        .navbar .navbar-nav li > a:hover, .navbar .navbar-nav li > a:hover .menu-icon {
          color: #fff !important; }
        .navbar .navbar-nav li > a .menu-icon {
          color: #8b939b;
          float: left;
          margin-top: 8px;
          width: 55px;
          text-align: left;
          z-index: 9; }
        .navbar .navbar-nav li > a .menu-title-text {
          font-size: 14px; }
        .navbar .navbar-nav li > a .badge {
          border-radius: 0;
          font-family: 'Open Sans';
          font-weight: 600;
          float: right;
          margin: 6px 0 0 0;
          padding: 0.4em 0.5em; }
      .navbar .navbar-nav li.menu-item-has-children {
        position: relative; }
        .navbar .navbar-nav li.menu-item-has-children a {
          line-height: 30px; }
          .navbar .navbar-nav li.menu-item-has-children a:before {
            content: "\f105";
            color: #c8c9ce;
            font-family: 'Fontawesome';
            font-size: 16px;
            position: absolute;
            top: 10px;
            right: 0;
            text-align: right;
            -webkit-transition: all .25s ease;
            -moz-transition: all .25s ease;
            -ms-transition: all .25s ease;
            -o-transition: all .25s ease;
            transition: all .25s ease; }
          .navbar .navbar-nav li.menu-item-has-children a:hover:before {
            color: #fff; }
        .navbar .navbar-nav li.menu-item-has-children .sub-menu {
          background: #272c33;
          border: none;
          box-shadow: none;
          overflow-y: hidden;
          padding: 0 0 0 35px; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu li {
            position: relative; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu i {
            color: #c8c9ce;
            float: left;
            padding: 0;
            position: absolute;
            left: 0;
            font-size: 14px;
            top: 9px; }
          .navbar .navbar-nav li.menu-item-has-children .sub-menu a {
            padding: 2px 0 2px 30px; }
            .navbar .navbar-nav li.menu-item-has-children .sub-menu a:before {
              content: '';
              display: none; }
            .navbar .navbar-nav li.menu-item-has-children .sub-menu a .menu-icon {
              top: 13px;
              text-align: left;
              width: 25px; }
        .navbar .navbar-nav li.menu-item-has-children.show a:before {
          content: "\f107"; }
        .navbar .navbar-nav li.menu-item-has-children.show .sub-menu {
          max-height: 1000px;
          opacity: 1;
          position: static !important; }

.navbar .navbar-nav > .active > a,
.navbar .navbar-nav > .active > a:focus,
.navbar .navbar-nav > .active > a:hover {
  color: #d7d9e3 !important; }

.navbar-nav li span.count {
  background: #a9d86e;
  border-radius: 50%;
  color: #fff;
  font-family: 'Open Sans';
  font-size: 9px;
  font-weight: 700;
  float: right;
  height: 20px;
  width: 20px;
  line-height: 20px;
  margin-right: 15px;
  text-align: center; }

body.open .navbar .navbar-brand.hidden {
  display: block; }

.open aside.left-panel {
  max-width: 70px;
  width: 70px; }
  .open aside.left-panel .navbar .navbar-brand {
    display: none; }
    .open aside.left-panel .navbar .navbar-brand.hidden {
      display: flex !important;
      justify-content: center;
      align-items: center;
      padding-left: 0;
      padding-right: 0;
      text-align: center; }
      .open aside.left-panel .navbar .navbar-brand.hidden img {
        max-width: 30px;
        margin: 0 auto; }
    .open aside.left-panel .navbar .navbar-brand.d-md-none {
      display: block !important;
      margin: 13px 0 0;
      min-height: 67px;
      padding: 0;
      text-align: center; }
  .open aside.left-panel .navbar .navbar-nav:before {
    display: none !important; }
  .open aside.left-panel .navbar .navbar-nav li {
    position: relative; }
    .open aside.left-panel .navbar .navbar-nav li a {
      font-size: 0;
      z-index: 0;
      transition: none; }
      .open aside.left-panel .navbar .navbar-nav li a .menu-icon {
        font-size: 20px;
        z-index: -1;
        width: inherit; }
      .open aside.left-panel .navbar .navbar-nav li a .menu-title-text {
        font-size: 0; }
      .open aside.left-panel .navbar .navbar-nav li a .badge {
        display: none; }
    .open aside.left-panel .navbar .navbar-nav li > a {
      max-width: 60px;
      padding-left: 0; }
    .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children {
      overflow: hidden; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children a:before {
        content: '';
        display: none; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children ul {
        padding-left: 0; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu {
        display: block;
        left: inherit;
        right: -180px;
        top: 0; }
        .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu li a {
          display: block;
          font-size: 14px;
          max-width: inherit;
          padding: 2px 15px 2px 25px;
          width: 100%; }
          .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children .sub-menu li a .menu-icon {
            text-align: center; }
      .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children.show {
        overflow: visible; }
        .open aside.left-panel .navbar .navbar-nav li.menu-item-has-children.show .sub-menu {
          position: absolute !important; }
    .open aside.left-panel .navbar .navbar-nav li span.count {
      display: none;
      margin-right: 5px;
      z-index: 1; }
    .open aside.left-panel .navbar .navbar-nav li.active a:after {
      content: '';
      display: none; }
  .open aside.left-panel .navbar .navbar-nav .menu-title {
    font-size: 0;
    line-height: 0;
    opacity: 0;
    padding: 0; }

/* Right panel */
.right-panel {
  display: table-cell;
  padding-left: 0 !important;
  -webkit-transition: all .35s ease;
  -moz-transition: all .35s ease;
  -ms-transition: all .35s ease;
  -o-transition: all .35s ease;
  transition: all .35s ease; }

.right-panel header.header {
  background: #fff;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15);
  clear: both;
  display: inline-block;
  padding: 15px 20px 13px 20px;
  width: 100%; }

.open .right-panel {
  margin-left: -210px; }

header.fixed-top {
  background: #fff;
  padding: 20px; }

.header-menu .col-sm-7 {
  position: inherit; }

.menutoggle {
  background: #e74c3c;
  border-radius: 50%;
  color: #fff !important;
  cursor: pointer;
  font-size: 18px;
  height: 43px;
  line-height: 44px;
  margin: -2px 20px 0 -57px;
  text-align: center;
  width: 43px; }

.open .menutoggle i:before {
  content: "\f0a4"; }

.search-trigger {
  background: transparent;
  border: none;
  color: #272c33;
  cursor: pointer;
  font-size: 16px;
  height: 41px;
  width: 43px;
  line-height: 38px; }

header .form-inline {
  background: #263238;
  display: none;
  height: 70px;
  margin: 0;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 9999; }
  header .form-inline .search-form {
    height: 100%;
    max-width: 1025px;
    margin: 0 auto;
    position: relative; }
    header .form-inline .search-form input[type="text"] {
      background: #263238;
      border: none;
      border-radius: 0;
      box-shadow: none;
      color: #d3d3d3;
      font-size: 16px;
      height: inherit;
      margin-right: 0 !important;
      padding: 10px 36px 10px 15px;
      width: 100%; }
    header .form-inline .search-form input[type="text"].active,
    header .form-inline .search-form input[type="text"]:focus {
      border-color: rgba(0, 0, 0, 0.125);
      outline: 0; }
    header .form-inline .search-form button {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 16px;
      position: absolute;
      right: 15px;
      top: 50%;
      margin-top: -14px !important; }
    header .form-inline .search-form button:active,
    header .form-inline .search-form button:focus,
    header .form-inline .search-form button:visited,
    header .form-inline .search-form .btn-outline-success:hover {
      background: transparent;
      border: none !important;
      box-shadow: none;
      outline: 0 !important; }
    header .form-inline .search-form.close {
      display: none; }

.header-left.open .form-inline {
  display: block; }

.header-left .dropdown {
  display: inline-block; }
  .header-left .dropdown .dropdown-toggle {
    background: transparent;
    border: none;
    color: #272c33;
    font-size: 16px; }
    .header-left .dropdown .dropdown-toggle:after {
      display: none; }
    .header-left .dropdown .dropdown-toggle .count {
      border-radius: 50%;
      color: #fff;
      font-size: 11px;
      height: 15px;
      width: 15px;
      line-height: 15px;
      right: 0;
      top: 0;
      position: absolute; }
  .header-left .dropdown .dropdown-menu {
    background: #fff;
    border: none;
    border-radius: 0;
    box-shadow: none;
    top: 49px !important; }
    .header-left .dropdown .dropdown-menu p {
      font-size: 15px;
      margin: 0;
      padding: 5px 15px; }
    .header-left .dropdown .dropdown-menu .dropdown-item {
      color: #272c33;
      font-size: 13px;
      padding: 10px 15px 3px;
      text-overflow: ellipsis; }
      .header-left .dropdown .dropdown-menu .dropdown-item .photo {
        float: left;
        margin-right: 15px;
        width: 25px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message .name {
        margin-top: -5px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message .time {
        font-size: 11px; }
      .header-left .dropdown .dropdown-menu .dropdown-item .message p {
        clear: both;
        font-size: 14px;
        margin: 0;
        padding: 0;
        text-overflow: ellipsis; }
      .header-left .dropdown .dropdown-menu .dropdown-item:hover {
        background: transparent; }

.dropdown-menu {
  border-radius: 0;
  transform: none !important; }

.dropdown-toggle::after {
  display: none; }

.for-notification .dropdown-menu .dropdown-item {
  padding: 5px 15px !important;
  text-overflow: ellipsis; }
  .for-notification .dropdown-menu .dropdown-item i {
    float: left;
    font-size: 14px;
    margin: 5px 5px 0 0;
    text-align: left;
    width: 20px; }
  .for-notification .dropdown-menu .dropdown-item p {
    padding: 0 !important;
    text-overflow: ellipsis; }

.user-area {
  float: right;
  padding-right: 0;
  position: relative; }
  .user-area .user-menu {
    background: #fff;
    border: none;
    font-family: 'Open Sans';
    left: inherit !important;
    right: 0;
    top: 55px !important;
    margin: 0;
    max-width: 150px;
    padding: 5px 10px;
    position: absolute;
    width: 100%;
    z-index: 999;
    min-width: 150px; }
    .user-area .user-menu .nav-link {
      color: #272c33;
      display: block;
      font-size: 14px;
      line-height: 22px;
      padding: 5px 0; }
  .user-area .user-avatar {
    float: right;
    margin-top: 4px;
    width: 32px; }
  .user-area .user-info .name {
    color: #8c8c8c;
    font-size: 14px;
    position: relative;
    text-transform: uppercase; }
  .user-area .count {
    background: #d9534f;
    border-radius: 50%;
    color: #fff;
    font-family: 'Open Sans';
    font-size: 9px;
    font-weight: 700;
    float: right;
    height: 20px;
    width: 20px;
    line-height: 20px;
    text-align: center; }

#menuToggle2 {
  padding-left: 25px; }

#language-select {
  color: #f1f2f7;
  float: right;
  margin: 7px 20px 0 0;
  max-width: 80px; }
  #language-select:focus, #language-select:visited {
    border: none;
    outline: 0; }
  #language-select .dropdown-toggle::after {
    display: none; }
  #language-select .dropdown-menu {
    background: #fff;
    border: none;
    border-radius: 0;
    left: -8px !important;
    min-width: inherit;
    padding: 0 5px;
    top: 46px !important; }
    #language-select .dropdown-menu .dropdown-item {
      margin-right: 0;
      max-width: 25px;
      padding: 0; }
      #language-select .dropdown-menu .dropdown-item:hover {
        background: #fff; }
      #language-select .dropdown-menu .dropdown-item .flag-icon {
        margin-right: 0;
        width: 25px; }

.notification-show + .dropdown-menu,
.message-show + .dropdown-menu,
.language-show + .dropdown-menu {
  display: block; }

/*# sourceMappingURL=style.css.map */
