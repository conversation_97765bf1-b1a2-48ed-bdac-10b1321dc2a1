"""
AI-Powered Error Analysis Module for IDPA Log Analyzer
This module enhances the existing error analysis with GenAI capabilities
"""

import openai
import json
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import re

@dataclass
class AIErrorAnalysis:
    """Data class for AI error analysis results"""
    error_message: str
    root_cause: str
    severity: str  # Critical, High, Medium, Low
    component: str  # vCenter, ESX, Avamar, DataDomain, etc.
    impact_assessment: str
    confidence_score: float
    suggested_actions: List[str]
    related_errors: List[str]

class AIErrorAnalyzer:
    """AI-powered error analyzer that enhances the existing rule-based approach"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        """
        Initialize the AI Error Analyzer
        
        Args:
            api_key: OpenAI API key
            model: Model to use (gpt-3.5-turbo, gpt-4, etc.)
        """
        openai.api_key = api_key
        self.model = model
        self.logger = logging.getLogger(__name__)
        
        # IDPA-specific component keywords for context
        self.component_context = {
            'vCenter': ['vcenter', 'vsphere', 'esx', 'vmware', 'virtual'],
            'Avamar': ['avamar', 'backup', 'restore', 'dpn', 'mcs'],
            'DataDomain': ['datadomain', 'dd', 'dedup', 'storage'],
            'DPA': ['dpa', 'protection', 'appliance'],
            'DPC': ['dpc', 'controller'],
            'DPS': ['dps', 'service'],
            'LDAP': ['ldap', 'authentication', 'directory'],
            'CDRA': ['cdra', 'replication']
        }
    
    def analyze_error(self, error_message: str, thread_context: List[str], 
                     thread_name: str, timestamp: str) -> AIErrorAnalysis:
        """
        Analyze an error using AI to provide enhanced insights
        
        Args:
            error_message: The error message to analyze
            thread_context: List of related log lines from the same thread
            thread_name: Name of the thread where error occurred
            timestamp: When the error occurred
            
        Returns:
            AIErrorAnalysis object with AI-generated insights
        """
        try:
            # Prepare context for AI analysis
            context_text = "\n".join(thread_context[-10:])  # Last 10 lines for context
            
            prompt = self._create_analysis_prompt(
                error_message, context_text, thread_name, timestamp
            )
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Low temperature for consistent analysis
                max_tokens=1000
            )
            
            # Parse AI response
            ai_response = response.choices[0].message.content
            return self._parse_ai_response(error_message, ai_response)
            
        except Exception as e:
            self.logger.error(f"AI analysis failed for error: {error_message[:100]}... Error: {str(e)}")
            return self._create_fallback_analysis(error_message)
    
    def analyze_error_patterns(self, error_list: List[str]) -> Dict:
        """
        Analyze patterns across multiple errors to identify systemic issues
        
        Args:
            error_list: List of error messages to analyze for patterns
            
        Returns:
            Dictionary with pattern analysis results
        """
        try:
            # Group similar errors
            error_sample = error_list[:20]  # Analyze first 20 errors to avoid token limits
            
            prompt = f"""
            Analyze these IDPA log errors for patterns and systemic issues:
            
            Errors:
            {chr(10).join([f"{i+1}. {error}" for i, error in enumerate(error_sample)])}
            
            Identify:
            1. Common patterns or root causes
            2. Cascading failures (errors that cause other errors)
            3. System-wide issues vs isolated problems
            4. Priority order for investigation
            5. Potential preventive measures
            
            Format as JSON with keys: patterns, cascading_failures, systemic_issues, investigation_priority, prevention_measures
            """
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert IDPA system administrator analyzing log patterns."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1500
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            self.logger.error(f"Pattern analysis failed: {str(e)}")
            return {"error": "Pattern analysis unavailable"}
    
    def _create_analysis_prompt(self, error_message: str, context: str, 
                               thread_name: str, timestamp: str) -> str:
        """Create a detailed prompt for error analysis"""
        return f"""
        Analyze this IDPA (Integrated Data Protection Appliance) log error:
        
        Error Message: {error_message}
        Thread Name: {thread_name}
        Timestamp: {timestamp}
        
        Context (recent log lines):
        {context}
        
        Provide analysis in this JSON format:
        {{
            "root_cause": "Detailed explanation of what caused this error",
            "severity": "Critical|High|Medium|Low",
            "component": "Primary component affected (vCenter|ESX|Avamar|DataDomain|DPA|DPC|DPS|LDAP|CDRA|Network|Other)",
            "impact_assessment": "Description of potential impact on system functionality",
            "confidence_score": 0.85,
            "suggested_actions": ["Action 1", "Action 2", "Action 3"],
            "related_errors": ["Pattern 1", "Pattern 2"]
        }}
        
        Consider IDPA-specific components and their interactions.
        """
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt that defines the AI's role"""
        return """
        You are an expert Dell EMC IDPA (Integrated Data Protection Appliance) system administrator 
        with deep knowledge of:
        - vCenter and vSphere integration issues
        - Avamar backup and restore operations
        - Data Domain storage and deduplication
        - Network connectivity and authentication problems
        - Component interactions and dependencies
        
        Analyze log errors with focus on:
        1. Root cause identification
        2. Impact assessment on data protection operations
        3. Practical troubleshooting steps
        4. Prevention strategies
        
        Always provide actionable insights and consider the enterprise nature of IDPA deployments.
        """
    
    def _parse_ai_response(self, error_message: str, ai_response: str) -> AIErrorAnalysis:
        """Parse AI response into structured analysis"""
        try:
            # Try to parse as JSON first
            if ai_response.strip().startswith('{'):
                data = json.loads(ai_response)
                return AIErrorAnalysis(
                    error_message=error_message,
                    root_cause=data.get('root_cause', 'Analysis unavailable'),
                    severity=data.get('severity', 'Medium'),
                    component=data.get('component', 'Other'),
                    impact_assessment=data.get('impact_assessment', 'Impact assessment unavailable'),
                    confidence_score=data.get('confidence_score', 0.5),
                    suggested_actions=data.get('suggested_actions', []),
                    related_errors=data.get('related_errors', [])
                )
            else:
                # Parse free-form response
                return self._parse_freeform_response(error_message, ai_response)
                
        except Exception as e:
            self.logger.error(f"Failed to parse AI response: {str(e)}")
            return self._create_fallback_analysis(error_message)
    
    def _parse_freeform_response(self, error_message: str, response: str) -> AIErrorAnalysis:
        """Parse free-form AI response"""
        # Extract severity
        severity = 'Medium'
        if any(word in response.lower() for word in ['critical', 'severe', 'fatal']):
            severity = 'Critical'
        elif any(word in response.lower() for word in ['high', 'major', 'important']):
            severity = 'High'
        elif any(word in response.lower() for word in ['low', 'minor', 'trivial']):
            severity = 'Low'
        
        # Extract component
        component = 'Other'
        for comp, keywords in self.component_context.items():
            if any(keyword in response.lower() for keyword in keywords):
                component = comp
                break
        
        return AIErrorAnalysis(
            error_message=error_message,
            root_cause=response[:200] + "..." if len(response) > 200 else response,
            severity=severity,
            component=component,
            impact_assessment="Impact assessment from AI analysis",
            confidence_score=0.7,
            suggested_actions=["Review AI analysis for detailed recommendations"],
            related_errors=[]
        )
    
    def _create_fallback_analysis(self, error_message: str) -> AIErrorAnalysis:
        """Create fallback analysis when AI fails"""
        return AIErrorAnalysis(
            error_message=error_message,
            root_cause="AI analysis unavailable - using rule-based analysis",
            severity="Medium",
            component="Other",
            impact_assessment="Manual review required",
            confidence_score=0.3,
            suggested_actions=["Manual investigation required"],
            related_errors=[]
        )

# Integration helper functions
def enhance_thread_analysis(thread_data: Tuple, ai_analyzer: AIErrorAnalyzer) -> Dict:
    """
    Enhance existing thread analysis with AI insights
    
    Args:
        thread_data: Tuple from mainanalyzer.py (thread_name, error_count, stack, etc.)
        ai_analyzer: AIErrorAnalyzer instance
        
    Returns:
        Enhanced thread data with AI insights
    """
    thread_name, error_count, error_stack, start_time, end_time, age, activity, owner, full_stack, solutions = thread_data
    
    # Analyze the most critical errors in this thread
    ai_insights = []
    for error in error_stack[:3]:  # Analyze top 3 errors
        analysis = ai_analyzer.analyze_error(
            error_message=error,
            thread_context=full_stack,
            thread_name=thread_name,
            timestamp=start_time
        )
        ai_insights.append(analysis)
    
    # Create enhanced thread data
    enhanced_data = {
        'original_data': thread_data,
        'ai_insights': ai_insights,
        'ai_severity': max([insight.severity for insight in ai_insights], key=lambda x: ['Low', 'Medium', 'High', 'Critical'].index(x)),
        'ai_components': list(set([insight.component for insight in ai_insights])),
        'ai_confidence': sum([insight.confidence_score for insight in ai_insights]) / len(ai_insights) if ai_insights else 0
    }
    
    return enhanced_data
