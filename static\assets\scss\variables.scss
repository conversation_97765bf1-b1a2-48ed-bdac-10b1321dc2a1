$container-bg: #f1f2f7;
$menu-bg: #272c33;
$header-color: #00acc1;
$header-bg: #fff;
$menu-color: #272c33;

$color-red-error: rgb(185, 74, 72);
$color-grey-arrow: rgba(204, 204, 204, 0.2);

$width-default: 220px; // 3 960px-grid columns

$zindex-select-dropdown: 1035; // must be lower than a modal background (1040) but higher than the fixed navbar (1030)


/* Bootstrap */
// stylelint-disable
$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #adb5bd !default;
$gray-600: #868e96 !default;
$gray-700: #495057 !default;
$gray-800: #343a40 !default;
$gray-900: #212529 !default;
$black:    #000 !default;

$header-color: #00acc1;
$bg-color: #272c33;
$section-title-bg: #263238;
$dark-text-color: #fff;
$dark-text-second-color: #99abb4;
$dark-border-color: rgba(120, 130, 140, 0.4);
$dark-hover-color: #868e96;

$border-color:                        $gray-200 !default;
$layout-transition-speed:             .25s !default;


// Cards
$card-spacer-y:            .75rem !default;
$card-spacer-x:            1.25rem !default;



// Social Colors

$facebook:                            #3b5998 !default;
$twitter:                             #00aced !default;
$linkedin:                            #4875b4 !default;
$google-plus:                         #d34836 !default;
$flickr:                              #ff0084 !default;
$tumblr:                              #32506d !default;
$xing:                                #026466 !default;
$github:                              #4183c4 !default;
$html5:                               #e34f26 !default;
$openid:                              #f78c40 !default;
$stack-overflow:                      #fe7a15 !default;
$youtube:                             #b00 !default;
$css3:                                #0170ba !default;
$dribbble:                            #ea4c89 !default;
$google-plus:                         #bb4b39 !default;
$instagram:                           #517fa4 !default;
$pinterest:                           #cb2027 !default;
$vk:                                  #45668e !default;
$yahoo:                               #400191 !default;
$behance:                             #1769ff !default;
$dropbox:                             #007ee5 !default;
$reddit:                              #ff4500 !default;
$spotify:                             #7ab800 !default;
$vine:                                #00bf8f !default;
$foursquare:                          #1073af !default;
$vimeo:                               #aad450 !default;

// Default BootStrap Variables
$text-muted:                  $gray-600 !default;


$spacer: 1rem !default;
$spacers: (
  0: 0,
  1: ($spacer * .25),
  2: ($spacer * .5),
  3: $spacer,
  4: ($spacer * 1.5),
  5: ($spacer * 3)
) !default;

$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`
$font-size-lg:                ($font-size-base * 1.25) !default;
$font-size-sm:                ($font-size-base * .875) !default;

$body-bg:                   $white !default;
$body-color:                $gray-900 !default;
$blue:    #007bff !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #dc3545 !default;
$orange:  #fd7e14 !default;
$yellow:  #ffc107 !default;
$green:   #28a745 !default;
$teal:    #20c997 !default;
$cyan:    #17a2b8 !default;

$primary:       $blue !default;
$secondary:     $gray-600 !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-100 !default;
$dark:          $gray-800 !default;

$theme-colors: () !default;
$theme-colors: map-merge((
  "primary":    $primary,
  "secondary":  $secondary,
  "success":    $success,
  "info":       $info,
  "warning":    $warning,
  "danger":     $danger,
  "light":      $light,
  "dark":       $dark
), $theme-colors);
// stylelint-enable